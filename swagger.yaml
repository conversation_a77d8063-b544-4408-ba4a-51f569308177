# Path tags must not be cyrillic, otherwise expand and other features fail
swagger: '2.0'

info:
  description: |
    ## Доступ
    Доступ до API відбувається по протоколу HTTPS.

    ## Авторизація
    Авторизація з API відбувається за допомогою передачі заголовка при
    кожному запиті даних:
    1. Authorization: Token &lt;token&gt;, де &lt;token&gt; – унікальний токен, котрий
    надаєтся системою для зв'язки користувач+компанія.

    ## Обмеження
    Обмеження на завантаження файлів:
      1. Одне завантаження не повинно містити більше, ніж 500 файлів, сумарним
      розміром не більше 100Мб.
      2. Вага одного файла не повинна перевищувати 5Мб.

    ## Питання та пропозиції
    Очікуємо за адресою: <EMAIL>
  termsOfService: https://vchasno.com.ua/terms-of-use/
  title: API Вчасно
  version: '2.0'
basePath: /api
schemes:
  - https
validatorUrl: null

components:
  responses:
    # 2**
    Success:
      description: Запит виконано успішно
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Success'
    Created:
      description: Об'ект створено
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Success'
    # 4**
    BadRequest:
      description: Неправильно сформований запит
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    NotFound:
      description: Не знайдено
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    Unauthorized:
      description: Необхідна авторизація запиту або доступ до вибранної дії
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    # 5**
    ServerError:
      description: Виникла помилка серверу
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
  schemas:
    Error:
      type: object
      properties:
        code:
          type: string
        reason:
          type: string
        details:
          type: object
      required:
        - code
        - reason
    Success:
      type: object
      properties:
        status:
          type: integer
      required:
        - status
definitions:
  Comment:
    type: object
    properties:
      text:
        type: string
      type:
        type: string
      author:
        $ref: '#/definitions/CommentAuthor'
      date_created:
        type: string
    required:
      - text
      - type
      - author
      - date_created
  CommentAuthor:
    type: object
    properties:
      edrpou:
        type: string
      email:
        type: string
      is_legal:
        type: boolean
      first_name:
        type: string
      second_name:
        type: string
      last_name:
        type: string
    required:
      - edrpou
      - email
      - is_legal
  Document:
    type: object
    properties:
      id:
        type: string
        description: Внутрішній ID документу у системі Вчасно
        required: true
      vendor:
        type: string
        description:
          Значення, якою інтеграцією був завантажений документ – в нашому
          випадку тут завжди буде значення "API"
        required: true
      vendor_id:
        type: string
        description: ID документу у системі клієнта
        required: true
      status:
        type: integer
        description: Статус документу в системі “Вчасно”
        required: true
      signatures_to_finish:
        type: integer
        description:
          Кількість підписів, після котрих документ буде визнано завершеним
          (для рахунка - 1, для актів/інших документів - 2)
        required: true
      first_sign_by:
        type: string
        description: Чий перший підпис
        required: true
      extension:
        type: string
        description: Формат документу
        required: true
      title:
        type: string
        description: Назва документу
        required: true
      type:
        type: string
        description: Тип документу, наприклад “Рахунок”
      date:
        type: string
        description: Дата документу у форматі ISO 8601
      date_created:
        type: string
        description: Дата завантаження документу у форматі ISO 8601
        required: true
      date_delivered:
        type: string
        description: Дата отримання документу у форматі ISO 8601
      number:
        type: string
        description: Зовнішній номер документу
      preview_url:
        type: string
        description:
          Сторінка перегляду документу в сервісі Вчасно. Якщо не null - доступ
          до такої сторінки можливий без логіну та паролю
      url:
        type: string
        description: Сторінка документу в сервісі Вчасно
        required: true
      is_delivered:
        type: boolean
        description: Документ отримано в сервісі Вчасно
        required: true
      signatures:
        type: array
        items:
          $ref: '#/definitions/DocumentSignature'
        required: true
  DocumentSignature:
    type: object
    properties:
      date_created:
        type: string
        required: true
      email:
        type: string
        required: true
      id:
        type: string
        format: uuid
        required: true
  SignSession:
    type: object
    properties:
      created_by:
        type: string
        format: uuid
        required: true
      document_id:
        type: string
        format: uuid
        required: true
      document_status:
        type: string
      edrpou:
        type: string
        required: true
      email:
        type: string
        required: true
      id:
        type: string
        format: uuid
        required: true
      is_legal:
        type: boolean
        required: true
      on_cancel_url:
        type: string
      on_document_comment_hook:
        type: string
      on_document_reject_hook:
        type: string
      on_document_sign_hook:
        type: string
      on_document_view_hook:
        type: string
      on_finish_url:
        type: string
      role_id:
        type: string
        format: uuid
        required: true
      status:
        type: string
        required: true
      type:
        type: string
        required: true
      url:
        type: string
        required: true
        description: URL сесії перегляду документу
      vendor:
        type: string
        required: true
parameters:
  # Headers
  auth_token:
    in: header
    name: Authorization
    description: Токен авторизації
    schema:
      type: string
      format: uuid
    required: true
  # Paths
  document_id:
    in: path
    name: document_id
    schema:
      type: string
      format: uuid
    required: true
    description: ID Документу
paths:
  # Comments
  /v2/documents/{document_id}/comments:
    get:
      tags:
      - Comments
      summary: Коментарі до документу
      produces:
      - application/json
      parameters:
      - $ref: '#/parameters/auth_token'
      - $ref: '#/parameters/document_id'
      responses:
        200:
          description: Коментарі до документу відібрані
          schema:
            type: object
            properties:
              comments:
                type: array
                items:
                  $ref: '#/definitions/Comment'
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
    post:
      tags:
      - Comments
      summary: Додати коментар до документу
      description: Додати коментар до документу
      produces:
      - application/json
      parameters:
      - $ref: '#/parameters/auth_token'
      - $ref: '#/parameters/document_id'
      - in: body
        name: data
        description: Тіло запиту
        required: true
        schema:
          type: object
          properties:
            text:
              type: string
              description: Коментар
              retuired: true
      responses:
        201:
          description: Коментар створено
          schema:
            $ref: '#/definitions/Comment'
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
  # Documents
  /v2/documents:
    get:
      tags:
      - Documents
      summary: Отримання статусів документів
      produces:
      - application/json
      description: |
        ## Значення поля “Статус”
        * 7000 - документ завантажений у систему
        * 7001 - документ завантажений у систему і готовий для надіслання
        * 7002 - документ надісланий контрагенту
        * 7004 - документ підписаний власником
        * 7006 - документ відхилений контрагентом
        * 7008 - документ підписаний всіма сторонами (для рахунка власником,
        для актів/інших документів власником і контрагентом)
        * 7009 - документ видалений
        ---
        ## Значення “has_changed”
        * “1” - з моменту останнього запиту була здійснена одна чи більше дій
        над документом (завантаження, коментар, підписання, відправлення,
        відхилення)
        * “0” - над документом не здійснювалася ні одна з дій, перерахованих
        вище, з моменту останнього запиту
        ---
        ## Значення “is_delivered”
        * “1” - проставляється якщо будь-який користувач, що має доступ до
        ЄДРПОУ отримувача виконав одну з наступних дій або увійшов у систему і
        побачив перелік документів, або не виходячи з системи переключився на
        цю компанію, або перебуваючи в компанії не виходив із системи і  оновив
        сторінку із переліком документів
        * “0” - над документом не здійснювалася ні одна з дій, перерахованих
        вище
        ---
        ## Зауваження
        Застаріла версія API за адресою /api/v1/documents відрізняється
        відсутністю параметру cursor та поля next_cursor.
      parameters:
      - $ref: '#/parameters/auth_token'
      - in: query
        name: date_from
        schema:
          type: string
        description:
          Повертаються документи із датою завантаження більшою або рівною за
          вказану
      - in: query
        name: date_to
        schema:
          type: string
        description:
          Повертаються документи із датою завантаження меншою або рівною за
          вказану
      - in: query
        name: date_finished_from
        schema:
          type: string
        description:
          Повертаються документи із датою останнього підпису більшою або рівною
          за вказану (статус 7008)
      - in: query
        name: date_finished_to
        schema:
          type: string
        description:
          Повертаються документи із датою останнього підпису меншою або рівною
          за вказану (статус 7008)
      - in: query
        name: date_rejected_from
        schema:
          type: string
        description:
          Повертаються документи із датою відхилення більшою або рівною за
          вказану
      - in: query
        name: date_rejected_to
        schema:
          type: string
        description:
          Повертаються документи із датою відхилення меншою або рівною за
          вказану
      - in: query
        name: date_document_from
        schema:
          type: string
        description:
          Повертаються документи із датою документу більшою або рівною за
          вказану
      - in: query
        name: date_document_to
        schema:
          type: string
        description:
          Повертаються документи із датою документу меншою або рівною за
          вказану
      - in: query
        name: extension
        schema:
          type: string
        description: Формат завантаженого документу (наприклад .xml)
      - in: query
        name: recipient_edrpou
        schema:
          type: string
        description: ЕДРПОУ отримувача документу
      - in: query
        name: status
        schema:
          type: integer
        description: Код статусу документа
      - in: query
        name: vendor
        schema:
          type: string
        description: Значення, якою інтеграцією був завантажений документ
      - in: query
        name: vendor_id
        schema:
          type: string
        description: ID документа у системі клієнта (1C, SAP, і тд)
      - in: query
        name: cursor
        schema:
          type: string
        description:
          Токен пагінації. значення, отримане із поля "next_cursor" відповіді
          на попередній запит. Використовується для отримання наступної партії
          документів.
      - in: query
        name: ids
        schema:
          type: string
        description:
          ID документа. Цей параметр можна повторювати багато разів для
          повернення статусів документів за переліком ID.
      - in: query
        name: has_changed
        schema:
          type: integer
        description:
          З моменту останнього запиту була здійснена одна чи більше дій над
          документом (завантаження, коментар, підписання, відправлення,
          відхилення). Після запиту статус has_changed змінюється на False.
          Наприклад "?has_changed=1"
      - in: query
        name: is_delivered
        schema:
          type: integer
        description: Документ отримано в сервісі Вчасно.
      responses:
        200:
          description: Формат відповідей
          schema:
            type: object
            properties:
              documents:
                type: array
                items:
                  $ref: '#/definitions/Document'
              next_cursor:
                type: string
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
    post:
      tags:
      - Documents
      summary: Завантаження документів
      description: |
        ## Формат назви файлів
        Для спрощення роботи у сервісі “Вчасно” і автоматичного представлення
        даних про контрагентів та документ, прийнятий наступний формат назви
        файлів:
        * `edrpouOwner_edrpouRecipient_date_type_number.pdf`
        * `edrpouOwner_edrpouRecipient_date_type_number_emailRecipient.pdf`
        * `edrpouOwner_edrpouRecipient_date_type_number_emailRecipient1,emailRecipient2.pdf`
        * `edrpouOwner_edrpouRecipient_date_type_number_emailRecipient_vendorId.pdf`

        де,
        * `edrpouOwner` – ЄДРПОУ/ІПН власника документу
        * `edrpouRecipient` – ЄДРПОУ/ІПН отримувача документу
        * `date` – дата формування документу
        * `type` – тип документу
        * `number` – порядковий номер документу
        * `emailRecipient` (необов’язково) - email отримувача документу
        * `vendorId` (необов’язково) - ID документу за яким можна
        синхронізувати статуси документів із відправником (власником)

        ## Для типу файлів .xml
        Дотримання формату назви файлу не є важливим завдяки можливості
        передачі мета даних у відповідних полях XML файлу.

        ## Приклад формату імені файлу
        `ХХХХХХХХ_XXXXXXXX_YYYYMMDD_ХХХ_NNNN_ABC@test.com_ХХХ-ХХХ`.pdf

        ## Приклад назви документу:
        `2893102110_3235608644_20170213_РахунокНаОплатуПокупцю_Sch<EMAIL>.ua_d33d73c8-8784-18e8-8786-d43d7eaa0aae.pdf`

        ## Завантаження архіва
        Можливе завантаження архіва .zip, що містить до 500 таких документів.

        Після надсилання у “Вчасно” файлу з таким іменем, всі мета-дані про
        документ будуть автоматично заповнені.
      produces:
      - application/json
      parameters:
      - $ref: '#/parameters/auth_token'
      - in: query
        description: Кількість необхідних підписів
        name: signatures_to_finish
        schema:
          type: integer
          enum: [1, 2]
          default: 2
      - in: query
        description: Яка з сторін - перший підписант документу
        name: first_sign_by
        schema:
          type: string
          enum: [owner, recipient]
          default: owner
      requestBody:
        description: Файл або архів для завантаження
        required: true
        content:
          multipart/form-data:
            schema:
              properties:
                filename:
                  type: array
                  items:
                    type: string
                    format: binary
      responses:
        201:
          description: Файли завантажено успішно
          schema:
            type: object
            properties:
              documents:
                type: array
                items:
                  $ref: '#/definitions/Document'
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
  /v2/download-documents:
    get:
      tags:
      - Documents
      summary: Отримання вхідних документів
      produces:
      - application/json
      parameters:
      - $ref: '#/parameters/auth_token'
      - in: query
        name: ids
        schema:
          type: string
        description:
          ID документу. Цей параметр може повторюватися багато разів для
          повернення статусів документів за переліком ID
      responses:
        200:
          description: Отримання вхідних документів
          schema:
            type: object
            properties:
              documents:
                type: array
                items:
                  type: object
                  properties:
                    archive_url:
                      type: string
                    extension:
                      type: string
                    id:
                      type: string
                    original_url:
                      type: string
                    xml-to-pdf_url:
                      type: string
                    status:
                      type: string
                      enum: [pending, ready]
                  required:
                  - archive_url
                  - extension
                  - id
                  - original_url
                  - status
              pending:
                type: string
              ready:
                type: integer
              status:
                type: string
                enum: [empty, pending, ready]
              total:
                type: integer
            required:
            - documents
            - pending
            - ready
            - status
            - total
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
  /v2/incoming-documents:
    get:
      tags:
      - Documents
      summary: Отримання вхідних документів
      produces:
      - application/json
      parameters:
      - $ref: '#/parameters/auth_token'
      - in: query
        name: date_created_from
        schema:
          type: string
        description:
          Повертаються документи із датою створення більшою або рівною за
          вказану
      - in: query
        name: date_created_to
        schema:
          type: string
        description:
          Повертаються документи із датою створення меншою або рівною за
          вказану
      - in: query
        name: date_sent_from
        schema:
          type: string
        description:
          Повертаються документи із датою надсилання більшою або рівною за
          вказану
      - in: query
        name: date_sent_to
        schema:
          type: string
        description:
          Повертаються документи із датою надсилання меншою або рівною за
          вказану
      - in: query
        name: date_document_from
        schema:
          type: string
        description:
          Повертаються документи із датою документу більшою або рівною за
          вказану
      - in: query
        name: date_document_to
        schema:
          type: string
        description:
          Повертаються документи із датою документу меншою або рівною за
          вказану
      - in: query
        name: extension
        schema:
          type: string
        description: Формат завантаженого документу (наприклад .xml)
      - in: query
        name: cursor
        schema:
          type: string
        description:
          Токен пагінації. Значення, отримане із поля "next_cursor" відповіді
          на попередній запит. Використовується для отримання наступної партії
          документів
      - in: query
        name: ids
        schema:
          type: string
        description:
          ID документу. Цей параметр може повторюватися багато разів для
          повернення статусів документів за переліком ID
      responses:
        200:
          description: Отримання вхідних документів
          schema:
            type: object
            properties:
              documents:
                type: array
                items:
                  $ref: '#/definitions/Document'
              next_cursor:
                type: string
            required:
            - documents
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
  /v2/documents/{document_id}/archive:
    get:
      tags:
      - Documents
      summary: Завантаження ZIP архіву із документом і підписами
      parameters:
      - $ref: '#/parameters/auth_token'
      - $ref: '#/parameters/document_id'
      responses:
        200:
          description: Отримання ZIP архіву із документом і підписами
          content:
            application/zip:
              schema:
                type: string
                format: binary
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
  /v2/documents/{document_id}/original:
    get:
      tags:
      - Documents
      summary: Завантаження оригіналу документу
      parameters:
      - $ref: '#/parameters/auth_token'
      - $ref: '#/parameters/document_id'
      responses:
        200:
          description: Отримання оригіналу документу
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
  /v2/documents/{document_id}/xml-to-pdf:
    get:
      tags:
      - Documents
      summary: Завантаження PDF відображення XML документу
      parameters:
      - $ref: '#/parameters/auth_token'
      - $ref: '#/parameters/document_id'
      responses:
        200:
          description: Отримання PDF відображення XML документу
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
    post:
      tags:
      - Documents
      summary: Завантаження PDF відображення XML документу
      parameters:
      - $ref: '#/parameters/auth_token'
      - $ref: '#/parameters/document_id'
      - in: body
        name: data
        description: Тіло запиту
        required: false
        schema:
          type: object
          properties:
            force:
              type: boolean
              description:
                Створити PDF заново (True) або залишити існуючий PDF файл без
                змін (False)
              retuired: true
      responses:
        200:
          description: Отримання PDF відображення XML документу
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        201:
          description: Отримання PDF відображення XML документу
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
  /v2/documents/{document_id}:
    delete:
      tags:
      - Documents
      summary: Видалення документу
      description:
        Документи у статусі 7008 (підписаний всіма сторонами) видалити не
        можна
      parameters:
      - $ref: '#/parameters/auth_token'
      - $ref: '#/parameters/document_id'
      produces:
      - application/json
      responses:
        204:
          description: Документ видалено
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'
  # Integration with client account
  /v2/sign-sessions:
    post:
      tags:
      - Integration into client account
      summary: Створення сесії перегляду документу
      parameters:
      - $ref: '#/parameters/auth_token'
      - in: body
        name: data
        description: Тіло запиту
        required: true
        schema:
          type: object
          properties:
            document_id:
              type: string
              format: uuid
              description: Документ, для якого буде створена сесія перегляду
              retuired: true
            edrpou:
              type: string
              description:
                ЕДРПОУ користувача, для якого буде створена сесія перегляду
              retuired: true
            email:
              type: string
              description:
                Пошта користувача, для якого буде створена сесія перегляду
              retuired: true
            is_legal:
              type: bool
              description:
                Вказаний ЕДРПОУ є Юридичною особою (True), чи Фізичною особою
                (False)
              retuired: true
            type:
              type: string
              description:
                Тип створеної сесії. Для сесії перегляду документу -
                view_session
              retuired: true
      produces:
      - application/json
      responses:
        200:
          description:
            Нова сесія перегляду не створена, відображені дані сесії,
            створеної раніше
          schema:
            $ref: '#/definitions/SignSession'
        201:
          description:
            Нова сесія перегляду створена для вказаного користувача
          schema:
            $ref: '#/definitions/SignSession'
        400:
          $ref: '#/components/responses/BadRequest'
        403:
          $ref: '#/components/responses/Unauthorized'

x-webpack-args: &webpack-args
  image: ${IMAGE_ASSETS:-evodoc-assets}
  build:
    context: .
    dockerfile: config/docker/local/frontend.dockerfile
    cache_from:
      - ${IMAGE_ASSETS:-evodoc-assets}
    args:
      VCHASNO_LEVEL: local
      VCHASNO_CONFIG: ${VCHASNO_CONFIG:-config/vchasno/local/docker/config.yaml}
      BUILDKIT_INLINE_CACHE: 1
  volumes: [ '.:/work', '/work/node_modules' ]
  entrypoint: [ 'node', '--max_old_space_size=4096', '/work/node_modules/webpack/bin/webpack.js', '--config', 'webpack.config.js', '--progress', '-w']

x-postgres: &postgres
  image: postgres:15
  restart: unless-stopped
  environment: &postgres-environment
    POSTGRES_DB: evodoc
    POSTGRES_DB_EVENTS: evodoc_events
    POSTGRES_USER: evodoc
    POSTGRES_PASSWORD: evodoc

x-elastic: &elastic
  image: elasticsearch:8.12.0
  environment:
    - discovery.type=single-node
    - "ES_JAVA_OPTS=-Xms1g -Xmx1g -Djdk.lang.Process.launchMechanism=vfork"  # prevent fails on M1.
    - xpack.security.enabled=false
  ulimits:
    memlock:
      soft: -1
      hard: -1


services:

  python-dev: &python-dev
    image: ${IMAGE_TOOLS:-evodoc-tools}
    build:
      context: .
      dockerfile: config/docker/local/backend.dockerfile
      cache_from:
        - ${IMAGE_TOOLS:-evodoc-tools}
      args:
        BUILDKIT_INLINE_CACHE: 1
    volumes: [ '.:/work' ]
    working_dir: '/work'
    environment: &app-environment
      PYTHONPATH: .
      PYTHONUNBUFFERED: 1
      PYTHONASYNCIODEBUG: 1
      OSPLM_CONFIG: /work/config/vchasno/local/eusign
      VCHASNO_CONFIG: ${VCHASNO_CONFIG:-config/vchasno/local/docker/config.yaml}
      VCHASNO_LEVEL: local

  postgres-test:
    <<: *postgres
    tmpfs:
      - /run
      - /var/cache
      - /var/lib/postgresql/data
      - /tmp
    volumes:
      - ./scripts/postgres/docker-compose-initdb-test.sh:/docker-entrypoint-initdb.d/docker-compose-initdb-test.sh
      - postgres_socket:/var/run/postgresql
    command:
      - postgres
      - --fsync=off
      - --full_page_writes=off
      - --synchronous_commit=off
      - --autovacuum=off
      - --wal_level=minimal
      - --max_wal_senders=0
      - --archive_mode=off
      - --shared_buffers=2GB
      - --work_mem=64MB

  postgres-events:
    <<: *postgres
    volumes:
      - ./scripts/postgres/docker-compose-initdb.sh:/docker-entrypoint-initdb.d/docker-compose-initdb.sh
    ports: [ '5433:5432' ]
    environment:
      POSTGRES_DB: evodoc_events
      POSTGRES_USER: evodoc
      POSTGRES_PASSWORD: evodoc

  postgres:
    <<: *postgres
    ports: [ '5432:5432' ]
    volumes:
      - ./scripts/postgres/docker-compose-initdb.sh:/docker-entrypoint-initdb.d/docker-compose-initdb.sh
      - ./scripts:/scripts

  kafka:
    image: apache/kafka-native:3.8.0
    ports: ["9094:9094"]
    environment:
      KAFKA_NODE_ID: 1
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: 'CONTROLLER:PLAINTEXT,INTERNAL:PLAINTEXT,OUTSIDE:PLAINTEXT'
      # to connect to kafka servers use:
      #  - kafka:9092 - inside docker network (from other docker container)
      #  - localhost:9094 - outside of docker network (from localhost)
      KAFKA_ADVERTISED_LISTENERS: 'INTERNAL://kafka:9092,OUTSIDE://localhost:9094'
      KAFKA_PROCESS_ROLES: 'broker,controller'
      KAFKA_CONTROLLER_QUORUM_VOTERS: '1@kafka:29093'
      KAFKA_LISTENERS: 'CONTROLLER://:29093,OUTSIDE://:9094,INTERNAL://:9092'
      KAFKA_INTER_BROKER_LISTENER_NAME: 'INTERNAL'
      KAFKA_CONTROLLER_LISTENER_NAMES: 'CONTROLLER'
      CLUSTER_ID: '4L6g3nShT-eMCtK--X86sw'
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_HEAP_OPTS: "-Xmx256M -Xms256M"


  redis: &redis
    image: redis:7.4-alpine

  redis-test:
    <<: *redis

  elasticsearch:
    <<: *elastic
    volumes:
      - es_data:/usr/share/elasticsearch/data
    ports: ['9200:9200']

  elasticsearch-test:
    <<: *elastic

  # TODO: use that service directly from url2pdf repository
  url2pdf.ua:
    restart: unless-stopped
    image: registry.evo.dev/evodoc/url2pdf
    ports: [ '8090:8090' ]

  # service for converting DOCX to PDF
  gotenberg:
    image: gotenberg/gotenberg:8
    ports: [ '3000:3000' ]

  http-server: &http-server
    <<: *python-dev
    tty: true
    command: ["./scripts/local/run-server.sh"]
    restart: unless-stopped
    ports: [ '8000:8000' ]
    depends_on: [ postgres, postgres-events, kafka, redis, elasticsearch, minio ]

  # If you need url2pdf locally, please up 'docker-compose up url2pdf.ua' before starting web
  web:
    <<: *http-server
    depends_on: [ postgres, postgres-events, kafka, redis, elasticsearch, minio, cron, worker, indexator, maildev, gotenberg]

  worker:
    <<: *python-dev
    tty: true
    command: ["./scripts/local/run-worker.sh"]
    restart: unless-stopped
    depends_on: [ postgres, postgres-events, kafka, redis, minio, dockerhost ]

  dockerhost:
    image: qoomon/docker-host
    cap_add: ['NET_ADMIN', 'NET_RAW']
    restart: unless-stopped

  cron:
    <<: *python-dev
    tty: true
    command: ["./scripts/local/run-cron.sh"]
    restart: unless-stopped
    depends_on: [ kafka, redis, postgres, minio ]

  indexator:
    <<: *python-dev
    tty: true
    command: ["./scripts/local/run-indexator.sh"]
    restart: unless-stopped
    depends_on: [ kafka, elasticsearch, redis, postgres, postgres-events ]

  maildev:
    image: maildev/maildev:2.1.0
    ports:
      - "1080:1080"
      - "1025:1025"

  webpack:
    tty: true
    <<: *webpack-args

  yarn:
    tty: true
    <<: *webpack-args
    entrypoint: yarn

  yarn-dev:
    tty: true
    <<: *webpack-args
    entrypoint: yarn dev

  cslint:
    <<: *webpack-args
    entrypoint: ['yarn', 'run', 'lint']

  cs-vulnerabilities-check:
    <<: *webpack-args
    entrypoint:  ['yarn', 'run', 'audit:code']

  cstest:
    <<: *webpack-args
    entrypoint: ['yarn', 'test']

  mypy:
    <<: *python-dev
    entrypoint: [ 'mypy' ]
    command: [ './api', './app', './cron', './scripts', './worker', './indexator' ]

  ruff-formatter:
    <<: *python-dev
    entrypoint: [ 'ruff', 'format', './api', './app', './cron', './scripts', './worker', './indexator', 'conftest.py' ]

  ruff-linter:
    <<: *python-dev
    entrypoint: [ 'ruff', 'check', './api', './app', './cron', './scripts', './worker', './indexator', 'conftest.py' ]

  pytest:
    <<: *python-dev
    tty: true
    volumes:
      - .:/work
      - postgres_socket:/var/run/postgresql
    environment:
      <<: [*app-environment, *postgres-environment]
      VCHASNO_CONFIG: config/vchasno/test/docker/config.yaml
      VCHASNO_LEVEL: test
      EVENTS_PG_HOST: "postgres-events-test"
      EVENTS_PG_USER: "evodoc"
      EVENTS_PG_PASSWORD: "evodoc"
      EVENTS_PG_DB: "evodoc_events"
      OSPLM_CONFIG: "/work/config/vchasno/test/eusign"
    entrypoint: [ './scripts/run-tests.sh' ]
    depends_on: [ postgres-test, redis-test, elasticsearch-test ]

  pytest-slow:
    <<: *python-dev
    tty: true
    volumes:
      - .:/work
      - postgres_socket:/var/run/postgresql
    environment:
      <<: [ *app-environment, *postgres-environment ]
      VCHASNO_CONFIG: config/vchasno/test/docker/config.yaml
      VCHASNO_LEVEL: test
      EVENTS_PG_HOST: "postgres-events-test"
      EVENTS_PG_USER: "evodoc"
      EVENTS_PG_PASSWORD: "evodoc"
      EVENTS_PG_DB: "evodoc_events"
      OSPLM_CONFIG: "/work/config/vchasno/test/eusign"
    entrypoint: [ './scripts/run-tests-slow.sh' ]
    depends_on: [ postgres-test, elasticsearch-test ]

  # Caddy is a web server and reverse proxy
  # Required for local development of collaborative editing
  caddy:
    image: caddy:2
    ports:
      - 80:80
      - 443:443
    volumes:
      - ./config/vchasno/local/Caddyfile:/etc/caddy/Caddyfile
      - ./config/vchasno/local/certs:/etc/caddy/certs
    depends_on:
      - web
      - collabora

  collabora:
    image: collabora/code:*********.1
    restart: unless-stopped
    ports:
      - 9980:9980
    environment:
      - extra_params=--o:ssl.enable=false --o:ssl.termination=true --o:net.service_root=/collabora --o:logging.level=5 --o:per_document.autosave_duration_secs=60 --o:storage.wopi.max_file_size=15000000 --o:per_document.idle_timeout_secs=900 --o:per_document.limit_file_size=15000000
      - dictionaries=en_US en_GB uk
      - TZ=Europe/Kyiv

  # S3-compatible storage
  minio:
    image: bitnami/minio:2025.4.8
    environment:
      - MINIO_DEFAULT_BUCKETS=edo-1,edo-2
      - MINIO_ROOT_USER=minio
      - MINIO_ROOT_PASSWORD=miniosecret
    ports:
      - 9000:9000  # API
      - 9001:9001  # Admin console
    volumes:
      - minio_data:/data

volumes:
  postgres_socket:
    driver: local
  es_data:
    driver: local
  minio_data:

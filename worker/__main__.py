import asyncio
import logging
import os

import logevo
import uvloop
from aiohttp import web
from prometheus_client import start_http_server as start_metrics_server

from app.config import get_level, init_eusign
from app.lib.enums import AppLevel
from app.services import services
from cron.decorators import app_required
from worker.utils import expose_worker_startup_properties

logger = logging.getLogger('worker')


@app_required(logger)
async def main(app: web.Application) -> None:
    from worker.runner import ConsumerWorker

    logger.info('Starting worker')

    # TODO[AK]: remove after DOC-1614 will be done
    # Intialize EUSign library
    if get_level() in {
        AppLevel.local,
        AppLevel.dev,
        AppLevel.prod,
    }:
        init_eusign()

    if debug := services.config.app.debug:
        loop = asyncio.get_event_loop()
        loop.set_debug(debug)
        logging.basicConfig(level=logging.DEBUG)

    concurrency = int(os.getenv('WORKER_CONCURRENCY', 1))

    worker = ConsumerWorker(
        app=app,
        logger=logger,
        concurrency=concurrency,
    )

    try:
        await worker.run()
    finally:
        await worker.stop()


if __name__ == '__main__':
    with logevo.main_context():
        asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

        if os.environ.get('K8S'):
            start_metrics_server(9100)

        level, concurrency = expose_worker_startup_properties()

        logger.info(
            'Worker level has been set',
            extra={
                'level': level,
                'concurrency': concurrency,
            },
        )

        asyncio.run(main())

from collections import defaultdict
from dataclasses import dataclass
from typing import Self

from app.auth.schemas import CompanyConfig
from app.auth.types import User
from app.documents.types import Document
from app.lib.database import DBRow
from app.lib.enums import Language


@dataclass
class DeleteRequestRecipient:
    role_id: str
    email: str
    company_edrpou: str
    company_name: str
    company_id: str
    language: Language | None

    @classmethod
    def from_row(cls, row: DBRow) -> Self:
        return cls(
            role_id=row.role_id,
            email=row.email,
            company_edrpou=row.company_edrpou,
            company_name=row.company_name,
            company_id=row.company_id,
            language=row.language,
        )


@dataclass
class GetDeleteRequestNotificationCtx:
    initiator: User
    message: str | None
    companies_configs: dict[str, CompanyConfig]
    emails_mapping: defaultdict[str, defaultdict[str, list[Document]]]
    recipients_mapping: dict[tuple[str, str], DeleteRequestRecipient]

from datetime import timed<PERSON><PERSON>

import sqlalchemy as sa

from app.documents.tables import listing_table
from app.documents.tests.utils import get_document_recipients
from app.flags import FeatureFlags
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import utc_now
from app.lib.enums import DocumentStatus, UserRole
from app.models import select_all
from app.registration.utils import schedule_sync_recipients_date_received_job
from app.tests.common import (
    TEST_COMPANY_NAME,
    datetime_test,
    prepare_client,
    prepare_document_data,
    prepare_document_recipients,
    prepare_user_data,
    with_elastic,
)
from worker import topics


async def _select_role_listings(conn: DBConnection, role_id: str) -> list[DBRow]:
    query = sa.select([listing_table]).where(listing_table.c.role_id == role_id)
    return await select_all(conn, query)


async def test_daily_notification_about_finished_documents(aiohttp_client, mailbox, test_flags):
    test_flags[FeatureFlags.ENABLE_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS.name] = True

    app, client, user1 = await prepare_client(
        aiohttp_client,
        can_receive_finished_docs=True,
        is_admin=False,
        can_view_document=False,
        company_name=TEST_COMPANY_NAME,
    )
    user2 = await prepare_user_data(
        app,
        email='<EMAIL>',
        can_receive_finished_docs=True,
        can_view_document=False,
        user_role=UserRole.user.value,
    )
    # don't send email to this user
    user3 = await prepare_user_data(
        app,
        email='<EMAIL>',
        can_receive_finished_docs=False,
        can_view_document=False,
        user_role=UserRole.user.value,
    )
    # don't send email to this user because no documents for this user
    await prepare_user_data(
        app,
        email='<EMAIL>',
        can_receive_finished_docs=True,
        can_view_document=False,
        user_role=UserRole.user.value,
    )
    # send email to this user because he is admin
    user5 = await prepare_user_data(
        app,
        email='<EMAIL>',
        can_receive_finished_docs=True,
        user_role=UserRole.admin.value,
    )

    # send 1 email
    d1 = await prepare_document_data(
        app=app,
        owner=user1,
        status_id=DocumentStatus.finished.value,
        date_finished=utc_now(),
    )
    # send 2 emails
    d2 = await prepare_document_data(
        app=app,
        owner=user1,
        status_id=DocumentStatus.finished.value,
        date_finished=utc_now() - timedelta(days=1),
        another_owners=[user2, user3],
    )
    # send 2 email
    d3 = await prepare_document_data(
        app=app,
        owner=user1,
        status_id=DocumentStatus.reject.value,
        date_finished=utc_now() - timedelta(days=1),
        another_owners=[user2, user3],
    )
    # don't send email because status is not finished
    d4 = await prepare_document_data(
        app=app,
        owner=user1,
        status_id=DocumentStatus.uploaded.value,
        date_finished=utc_now() - timedelta(days=1),
        another_owners=[user2, user3],
    )
    # don't send email because date_finished is older than 2 days
    d5 = await prepare_document_data(
        app=app,
        owner=user1,
        status_id=DocumentStatus.reject.value,
        date_finished=utc_now() - timedelta(days=3),
        another_owners=[user2, user3],
    )
    # don't send email because date_finished is today
    d6 = await prepare_document_data(
        app=app,
        owner=user1,
        status_id=DocumentStatus.reject.value,
        date_finished=utc_now(),
        another_owners=[user2, user3],
    )

    # Act
    async with with_elastic(app, [d1.id, d2.id, d3.id, d4.id, d5.id, d6.id]):
        await app['kafka'].send_record(
            topic=topics.SCHEDULE_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS
        )

    # Assert
    assert {
        user1.email,
        user2.email,
        user5.email,
    } == {msg['To'] for msg in mailbox}
    assert mailbox[0]['Subject'] == f'Звіт по завершеним документам {TEST_COMPANY_NAME}'


async def test_sync_recipients_date_received(aiohttp_client, test_flags):
    app, _, owner = await prepare_client(aiohttp_client)

    company_edrpou = '12345678'
    other_edrpou = '87654321'

    now = datetime_test('2023-10-30 00:00:00')
    date1 = datetime_test('2023-10-29 01:00:00')
    date2 = datetime_test('2023-10-28 02:00:00')
    date3 = datetime_test('2023-10-27 03:00:00')
    date4 = datetime_test('2023-10-26 04:00:00')

    document1 = await prepare_document_data(app, owner)
    document2 = await prepare_document_data(app, owner)
    document3 = await prepare_document_data(app, owner)
    document4 = await prepare_document_data(app, owner)

    await prepare_document_recipients(
        data=[
            {
                'document_id': document1.id,
                'edrpou': company_edrpou,
                'emails': ['<EMAIL>'],
                'date_sent': date1,
                'date_received': None,
            },
            {
                'document_id': document2.id,
                'edrpou': company_edrpou,
                'emails': ['<EMAIL>'],
                'date_sent': None,
                'date_received': None,
            },
            {
                'document_id': document3.id,
                'edrpou': company_edrpou,
                'emails': ['<EMAIL>'],
                'date_sent': date2,
                'date_received': date3,
            },
            {
                'document_id': document4.id,
                'edrpou': other_edrpou,
                'emails': ['<EMAIL>'],
                'date_sent': date4,
                'date_received': None,
            },
        ]
    )

    await schedule_sync_recipients_date_received_job(
        company_edrpou=company_edrpou,
        date_registered=now,
    )

    recipients = await get_document_recipients()
    assert len(recipients) == 4
    mapping = {recipient.document_id: recipient for recipient in recipients}

    assert mapping[document1.id].date_sent == date1
    assert mapping[document1.id].date_received == now

    assert mapping[document2.id].date_sent is None
    assert mapping[document2.id].date_received is None

    assert mapping[document3.id].date_sent == date2
    assert mapping[document3.id].date_received == date3

    assert mapping[document4.id].date_sent == date4
    assert mapping[document4.id].date_received is None

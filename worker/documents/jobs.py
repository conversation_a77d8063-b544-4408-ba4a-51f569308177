import logging
from collections import defaultdict
from datetime import (
    datetime,
    timedelta,
)
from typing import (
    Any,
)
from uuid import uuid4

import aiohttp
import sqlalchemy as sa
from aiohttp import web
from multidict import MultiDict

from api.errors import Error
from app.auth.constants import COMPANY_ID_REQUEST_KEY
from app.auth.db import (
    select_roles,
    select_user,
)
from app.auth.enums import RoleStatus
from app.auth.tables import (
    company_table,
    role_table,
    user_active_role_company_join,
    user_table,
)
from app.auth.utils import get_companies_configs_by_ids, get_company_config, get_user
from app.billing.db import select_active_company_rates
from app.billing.utils import get_companies_have_pro_or_higher_rate
from app.documents import db as documents_db
from app.documents import utils as app_utils
from app.documents.db import (
    insert_listings,
    select_document_by_id,
    select_documents,
    select_documents_count_to_limit,
    select_reject_notification_recipients,
    update_documents_date_delivered,
)
from app.documents.enums import AccessSource
from app.documents.notifications import RejectDocumentNotification
from app.documents.tables import (
    document_access_settings_private_table,
    document_recipients_table,
    listing_table,
)
from app.documents.types import Document, ListingDataAggregator
from app.documents.utils import (
    delete_es_documents,
    remove_listings_source,
    schedule_remove_company_listings_es,
)
from app.es.constants import INDEXATOR_TEMP_KEY
from app.es.enums import ESQuerySource
from app.es.utils import build_listing_query, count_es_documents, send_to_indexator
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import edi
from app.lib.constants import (
    EDI_COMDOC006_DOC_TYPE,
    EDI_COMDOC_DOC_TYPE,
    EDI_DELNOT_DOC_TYPE,
    EDI_RETCOMDOC_DOC_TYPE,
)
from app.lib.database import DBConnection, DBRow
from app.lib.datetime_utils import (
    end_of_day,
    optional_parse_raw_iso_datetime,
    start_of_day,
    utc_now,
)
from app.lib.edi import (
    EDIPayloadFinishDealByComdoc,
    EDIPayloadFinishDealByInvoice,
    EDIPayloadFinishDealByRetcomdoc,
)
from app.lib.emailing import can_receive_notification, send_email
from app.lib.enums import (
    DocumentFolder,
    DocumentListSortDate,
    DocumentStatus,
    NotificationType,
    Source,
    UserRole,
)
from app.lib.helpers import not_none
from app.lib.types import (
    DataDict,
)
from app.lib.urls import build_url
from app.models import select_all
from app.registration.utils import schedule_sync_recipients_date_received_job
from app.reviews.tables import review_table
from app.services import services
from app.sign_sessions.db import select_sign_session_by_id
from app.sign_sessions.enums import SignSessionDocumentStatus
from app.sign_sessions.utils import update_sign_session_document_status
from worker import topics
from worker.documents import db
from worker.documents.db import (
    select_documents_ids,
    select_documents_ids_for_recipient,
    select_listing_documents_ids,
)
from worker.documents.enums import ESIndexationDirection
from worker.documents.types import DeleteRequestRecipient, GetDeleteRequestNotificationCtx
from worker.documents.utils import (
    send_emails_about_new_delete_request,
)
from worker.utils import (
    is_migration_duplicate_detected,
    retry_config,
    vchasno_projects_retry_config,
)

MAX_INDEXATION_QUEUE_SIZE = 500_000
DOCUMENT_REINDEX_DELAY = 5  # minutes


async def update_docs_date_delivered(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Update date delivered for given company by role_id

    - role_id - update date delivered for all documents that given role
    has access
    - documents_ids: list[str] | None - list of documents for updating
    """

    role_id: str = data['role_id']
    documents_ids: list[str] = data['documents_ids']
    if not documents_ids:
        return

    date = None
    if date_str := data.get('date_delivered'):
        date = datetime.fromisoformat(date_str)

    async with services.db.acquire() as conn:
        user = await select_user(conn, role_id=role_id)
        await update_documents_date_delivered(
            conn=conn,
            user=user,
            documents_ids=documents_ids,
            date_delivered=date,
        )

    logger.info(
        msg='Updated date_delivered',
        extra={'role_id': role_id, 'documents_ids': documents_ids},
    )


async def update_sign_session_documents_date_delivered(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Update date delivered for document which has been opened within sign session
    """

    sign_session_id: str = data['sign_session_id']
    document_ids: list[str] = data['document_ids']
    date_delivered: datetime = data['date_delivered']

    async with services.db.acquire() as conn:
        sign_session = await select_sign_session_by_id(
            conn=conn,
            sign_session_id=sign_session_id,
        )
        if not sign_session:
            logger.info('Sign session is not found', extra={'sign_session_id': sign_session_id})
            return

        await documents_db.update_sign_session_documents_date_delivered(
            conn=conn,
            sign_session=sign_session,
            document_ids=document_ids,
            date_delivered=date_delivered,
        )

    logger.info(
        msg='Updated sign session documents date_delivered',
        extra={
            'sign_session_id': sign_session_id,
            'document_ids': document_ids,
            'date_delivered': date_delivered,
        },
    )


async def create_documents_role_access(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    role_id: str | None = data['role_id']
    data.setdefault('limit', 100)
    data.setdefault('offset', 0)

    extra = data.copy()
    extra['job_id'] = str(uuid4())

    logger.info('Start listing synchronization', extra=extra)

    async with app['db'].acquire() as conn:
        user = await select_user(
            conn=conn,
            role_id=role_id,
            use_active_filter=False,
        )
        if not user:
            logger.warning('Bad role', extra=extra)
            return

        document_ids, has_next = await select_documents_ids_for_recipient(
            conn=conn, user=user, offset=data['offset'], limit=data['limit']
        )

        logger.info(
            'Documents with access selected',
            extra=dict(extra, has_next=has_next, count=len(document_ids)),
        )

        listing_data = ListingDataAggregator()
        for document_id in document_ids:
            listing_data.add(
                document_id=document_id,
                access_edrpou=user.company_edrpou,
                role_id=user.role_id,
                source=AccessSource.default,
            )

        if listing_data.as_db():
            await insert_listings(conn=conn, data=listing_data.as_db())
            logger.info('Created documents access', extra=dict(extra, count=len(document_ids)))

        await send_to_indexator(app['redis'], document_ids, to_slow_queue=True)

    if has_next:
        await app['kafka'].send_record(
            topics.CREATE_DOCUMENTS_ROLE_ACCESS,
            value=dict(data, offset=data['offset'] + data['limit']),
        )


@vchasno_projects_retry_config
async def send_document_finish_status_to_edi(
    app: web.Application, data: DataDict, _: logging.Logger
) -> None:
    """Send document finish status to EDI.

    :param data: dict:
    - document_id: str - ID of finished document
    """
    document_id: str = data['document_id']
    document_type: str = data['type']

    if not services.config.edi:
        return

    client = edi.Client()

    if document_type in (EDI_DELNOT_DOC_TYPE, EDI_COMDOC006_DOC_TYPE):
        await client.request(EDIPayloadFinishDealByInvoice(invoice_id=document_id))
    elif document_type == EDI_RETCOMDOC_DOC_TYPE:
        await client.request(EDIPayloadFinishDealByRetcomdoc(retcomdoc_id=document_id))
    elif document_type == EDI_COMDOC_DOC_TYPE:
        await client.request(EDIPayloadFinishDealByComdoc(comdoc_id=document_id))


@vchasno_projects_retry_config
async def send_edi_request(app: web.Application, data: DataDict, _: logging.Logger) -> None:
    """
    Send EDI request.

    Why do we need job but not direct call without queue?
    Queue handles retry and errors.
    """

    method = data.pop('method')

    client = edi.Client()
    await client.raw_request(method, **data)


@retry_config(max_attempts=500, max_age=timedelta(days=2))
async def delete_document_access_source(
    app: web.Application, data: DataDict, _: logging.Logger
) -> None:
    """Remove access source for documents."""
    listings_ids: list[str] | None = data.get('listings_ids')
    if not listings_ids:
        return

    source: AccessSource = AccessSource(data['source'])

    async with app['db'].acquire() as conn:
        # Get documents ids that should be re-indexed before we remove an access source
        # and empty listings
        documents_ids = await select_listing_documents_ids(conn, listings_ids=listings_ids)

        removed_listings_ctx = await remove_listings_source(
            conn=conn,
            listings_ids=listings_ids,
            source=source,
        )

    # Reindex listings for all documents that could be affected
    await send_to_indexator(document_ids=documents_ids, to_listing=True)

    await schedule_remove_company_listings_es(
        company_listing_ids=removed_listings_ctx.company_listing_ids
    )


async def send_to_indexator_by_ids(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    documents_ids: list[str] = data['documents_ids']
    await send_to_indexator(app['redis'], documents_ids, to_slow_queue=False)


async def send_to_indexator_by_listings_ids(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Select documents by listings and send them to the ES indexator"""
    listings_ids: list[str] = data['listings_ids']
    async with app['db'].acquire() as conn:
        documents_ids = await select_listing_documents_ids(conn, listings_ids)

    await send_to_indexator(app['redis'], documents_ids, to_slow_queue=True)


async def remove_documents_from_index(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    document_ids = data.get('document_ids', [])
    company_listing_ids = data.get('company_listing_ids')

    log_extra = {'document_ids': document_ids, 'company_listing_ids': company_listing_ids}
    logger.info('[ES][Document] Removing documents from index', extra=log_extra)
    try:
        await delete_es_documents(
            documents_ids=document_ids, company_listing_ids=company_listing_ids
        )
    except Exception:
        logger.exception(
            '[ES][Document] Error deleting documents from index',
            extra=log_extra,
        )
        raise
    logger.info('[ES][Document] Documents removed from index')


async def send_document_status_callback(
    __: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Make callback to company API with status about document"""
    document_id = data['document_id']
    uploaded_by_edrpou = data['uploaded_by_edrpou']

    log_extra: DataDict = {'data': data}

    response = None
    try:
        response = await app_utils.send_document_status_callback(
            document_id=document_id,
            uploaded_by_edrpou=uploaded_by_edrpou,
        )

        response_data: dict[str, Any] = {}
        if response:
            try:
                raw_response = await response.json()
                # to simplify further processing, response data is always dict
                if isinstance(raw_response, dict):
                    response_data = raw_response
                else:
                    response_data = {'raw_response_data': raw_response}

            except (ValueError, aiohttp.ClientResponseError):
                log_extra = {'data': data, 'response_text': await response.text()}
                logger.exception('Cant get company API response', extra=log_extra)

            if response.status == 200 or response_data.get('success'):
                logger.info('Document finish status notification succeeded', extra=log_extra)
                return

            log_extra = {
                'data': data,
                'response_text': await response.text(),
                'response_status': response.status,
                'request_url': response.url,
            }
            logger.info(
                'Document finish status notification failed',
                extra=log_extra,
            )
    finally:
        if response is not None:
            response.release()
            await response.wait_for_close()

    log_extra = {'data': data, 'response_data': response_data}
    async with services.db.acquire() as conn:
        company_config = await get_company_config(conn, company_edrpou=uploaded_by_edrpou)

        config_retries = 0
        delay = 0
        if api_config := company_config.api:
            config_retries = api_config.get('retries', 0)
            delay = api_config.get('retry_delay_min', 0)

        retries = data.get('retries', 0) + 1
        if retries > config_retries:
            if config_retries:
                logger.warning('Document status notification retries exceeded', extra=log_extra)
            return

        data['retries'] = retries
        await services.kafka.add_task(
            conn=conn,
            topic=topics.SEND_DOCUMENT_STATUS_CALLBACK,
            delay_min=delay,
            data=data,
        )

    logger.warning(
        'Document finish status notification failed, retrying with delay',
        extra=log_extra,
    )


@retry_config(max_attempts=500, max_age=timedelta(days=2), delay_minutes=2)
async def send_documents_to_indexator(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    company_edrpous: list[str] = data.get('company_edrpous', [])
    excluded_edrpous: list[str] = data.get('excluded_edrpous', [])
    document_ids: list[str] = data.get('ids', [])
    is_listing_only = data.get('is_listing_only', False)

    # workaround to have ability to stop that job in case of emergency
    if get_flag(FeatureFlags.ES_DISABLE_REINDEXATION_JOB):
        return

    if document_ids:
        await send_to_indexator(
            document_ids=document_ids,
            to_temp=True,
            to_listing=is_listing_only,
        )
        return

    date_gte = None
    date_lte = None
    try:
        date_gte = optional_parse_raw_iso_datetime(data.get('date_gte'))
        date_lte = optional_parse_raw_iso_datetime(data.get('date_lte'))
    except ValueError:
        logger.error('Invalid date', exc_info=True)

    if not (company_edrpous or excluded_edrpous or date_gte or date_lte):
        return

    data.setdefault('limit', 3000)

    extra = data.copy()
    extra['job_id'] = str(uuid4())

    logger.info('Start sending to indexator', extra=extra)

    last_document_id = data.get('last_document_id')

    async with services.db_readonly.acquire() as conn:
        document_ids, has_next = await select_documents_ids(
            conn=conn,
            company_edrpous=company_edrpous,
            excluded_edrpous=excluded_edrpous,
            limit=data['limit'],
            last_document_id=last_document_id,
            date_gte=date_gte,
            date_lte=date_lte,
        )

        if not document_ids:
            logger.info('Documents for reindex not found')
            return

        last_document_id = document_ids[-1]

        logger.info(
            'Documents for indexator selected',
            extra=dict(
                extra,
                has_next=has_next,
                count=len(document_ids),
                last_document_id=last_document_id,
            ),
        )

        await send_to_indexator(
            document_ids=document_ids,
            to_temp=True,
            to_listing=is_listing_only,
        )

    if has_next:
        await app['kafka'].send_record(
            topics.SEND_DOCUMENTS_TO_INDEXATOR,
            value=dict(data, last_document_id=last_document_id),
        )


@retry_config(max_attempts=500, max_age=timedelta(days=2), delay_minutes=2)
async def reindex_documents(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """Task to reindex all documents in ElasticSearch"""

    # workaround to have ability to stop that job in case of emergency
    if get_flag(FeatureFlags.ES_DISABLE_REINDEXATION_JOB):
        return

    documents_ids: list[str] | None = data.get('ids')
    seqnum: int | None = data.get('seqnum')
    min_seqnum: int | None = data.get('min_seqnum')
    max_seqnum: int | None = data.get('max_seqnum')
    direction_raw: str = data.get('direction', 'asc')
    is_listing_only: bool = data.get('is_listing_only', False)
    limit: int = data.get('limit', 3000)
    direction = ESIndexationDirection(direction_raw)

    log_extra = {
        'seqnum': seqnum,
        'limit': limit,
        'min_seqnum': min_seqnum,
        'max_seqnum': max_seqnum,
        'is_listing_only': is_listing_only,
        'direction_raw': direction_raw,
    }

    logger.info('Start sending documents to indexator', extra=log_extra)

    last_seqnum: int | None = None
    if not documents_ids:
        async with services.db_readonly.acquire() as conn:
            documents = await db.select_documents_for_indexation(
                conn=conn,
                seqnum=seqnum,
                limit=limit,
                direction=direction,
                max_seqnum=max_seqnum,
                min_seqnum=min_seqnum,
            )

        if not documents:
            logger.info('Last reindex iteration', extra=log_extra)
            return

        last_seqnum = documents[-1].seqnum
        documents_ids = [document.id for document in documents]

    logger.info(
        msg='Documents for indexation selected',
        extra=dict(
            log_extra,
            count=len(documents_ids),
            last_seqnum=last_seqnum,
        ),
    )

    indexation_key = await send_to_indexator(
        redis=services.redis,
        document_ids=documents_ids,
        to_temp=True,
        to_listing=is_listing_only,
    )

    # Try to not raise queue in redis to high
    delay_min: int | None = None
    queue_size = await services.redis.zcard(indexation_key) if indexation_key else 0
    if queue_size >= MAX_INDEXATION_QUEUE_SIZE:
        delay_min = DOCUMENT_REINDEX_DELAY

    if last_seqnum:
        async with services.db.acquire() as conn:
            await services.kafka.add_task(
                conn=conn,
                topic=topics.REINDEX_DOCUMENTS,
                delay_min=delay_min,
                data={
                    'seqnum': last_seqnum,
                    'limit': limit,
                    'min_seqnum': min_seqnum,
                    'max_seqnum': max_seqnum,
                    'is_listing_only': is_listing_only,
                    'direction': direction,
                },
            )


@retry_config(max_attempts=50, max_age=timedelta(days=2), delay_minutes=2)
async def reindex_documents_with_reviews(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    if get_flag(FeatureFlags.DISABLE_REINDEX_REVIEWED_DOCS):
        logger.info('Reindex disabled by feature flag')
        return

    cursor: str = data.get('cursor', '00000000-0000-0000-0000-000000000000')

    if await is_migration_duplicate_detected(
        key='reindex_reviewed_documents',
        ttl_seconds=4 * 60,  # 4 minutes
        cursor_uuid=cursor,
    ):
        return

    # Lower the pace in case of too many documents in indexation queue
    reindex_queue_size = await services.redis.zcard(INDEXATOR_TEMP_KEY)
    if reindex_queue_size >= 20_000:
        async with services.db.acquire() as conn:
            await services.kafka.add_task(
                conn=conn,
                topic=topics.REINDEX_REVIEWED_DOCUMENTS,
                data=data,
                delay_min=5,
            )
        return

    async with services.db_readonly.acquire() as conn:
        rows = await select_all(
            conn=conn,
            query=(
                sa.select([review_table.c.document_id])
                .where(review_table.c.document_id > cursor)
                .order_by(review_table.c.document_id.asc())
                .distinct(review_table.c.document_id)
                .limit(1000)
            ),
        )
        if not rows:
            logger.info('Reviewed docs indexation is finished')
            return

        await send_to_indexator(
            document_ids=[row.document_id for row in rows],
            to_temp=True,
        )

        data['cursor'] = rows[-1].document_id

    await services.kafka.send_record(topics.REINDEX_REVIEWED_DOCUMENTS, data)


async def schedule_daily_notification_about_finished_documents(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Daily task that sends notifications to users about documents
    that have been finished.

    Sends to users, who:
    - enabled `can_receive_finished_docs` option.
    - have finished documents and access to them for some period of time.
    """

    if not get_flag(FeatureFlags.ENABLE_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS):
        logger.info('Feature is disabled')
        return

    yesterday = utc_now() - timedelta(days=1)
    limit = data.get('limit', 1000)
    # use date to avoid situations, when pagination is broken because somebody disabled notification
    offset_date = datetime.fromisoformat(data.get('offset_date', '1970-01-01T00:00:00+00:00'))
    use_master = data.get('use_master', False)

    # pass date to avoid situation when task is executes in the
    # middle of the day and not finished in the same day
    date_lte = end_of_day(datetime.fromisoformat(data.get('date_lte', yesterday.isoformat())))
    date_gte = start_of_day(datetime.fromisoformat(data.get('date_gte', yesterday.isoformat())))

    db = services.db_readonly if not use_master else services.db
    async with db.acquire() as conn:
        roles = await select_roles(
            conn,
            limit=limit + 1,
            order=[role_table.c.date_created],
            can_receive_finished_docs=True,
            role_status=RoleStatus.active,
            date_created_gte=offset_date,
        )

    if len(roles) > limit:
        await app['kafka'].send_record(
            topic=topics.SCHEDULE_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS,
            value={
                'limit': limit,
                'offset_date': roles[-1].date_created.isoformat(),
                'date_lte': date_lte.isoformat(),
                'date_gte': date_gte.isoformat(),
                'use_master': use_master,
            },
        )

    roles = roles[:limit]
    await services.kafka.send_records(
        topic=topics.SEND_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS,
        values=[
            {
                'role_id': role.id,
                'date_lte': date_lte.isoformat(),
                'date_gte': date_gte.isoformat(),
                'use_master': use_master,
            }
            for role in roles
        ],
    )


async def send_daily_notification_about_finished_documents(
    __: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Send notification to user about finished documents.
    """

    if not get_flag(FeatureFlags.ENABLE_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS):
        logger.info('Feature is disabled')
        return

    role_id = data['role_id']
    date_lte = datetime.fromisoformat(data['date_lte'])
    date_gte = datetime.fromisoformat(data['date_gte'])
    use_master = data.get('use_master', False)

    db = services.db if use_master else services.db_readonly
    async with db.acquire() as conn:
        user = await get_user(conn, role_id=role_id)
        if not user:
            logger.warning('User not found', extra=data)
            return

    if not user.email:
        logger.info('User email is not set', extra={'role_id': role_id, 'user_id': user.id})
        return

    es_query = await build_listing_query(
        user,
        services.es.documents,
        {
            'statusIds': [DocumentStatus.finished.value, DocumentStatus.reject.value],
            'sortDate': DocumentListSortDate.date_finished.value,
            'gte': data['date_gte'],
            'lte': data['date_lte'],
        },
        True,
    )
    documents_count = await count_es_documents(es_query, source=ESQuerySource.worker)

    if documents_count == 0:
        logger.info('No documents to send', extra=data)
        return

    logger.info('Sending notification', extra=data)

    get_params: Any = MultiDict()
    get_params.add('date_from', date_gte.strftime('%Y-%m-%d'))
    get_params.add('date_to', date_lte.strftime('%Y-%m-%d'))
    get_params.add('sort_date', 'date_finished')
    get_params.add('folder_id', DocumentFolder.internal.value)
    get_params.add('folder_id', DocumentFolder.not_internal.value)
    get_params.add(COMPANY_ID_REQUEST_KEY, user.company_id)

    await send_email(
        recipient_mixed=user.email,
        subject=_('Звіт по завершеним документам {company_name}').bind(
            company_name=user.company_name
        ),
        template_name='daily_notification_about_finished_documents',
        context={
            'recipient_first_name': user.first_name,
            'date': date_lte.strftime('%Y-%m-%d'),
            'documents_count': documents_count,
            'company_name': user.company_name,
            'filter_url': build_url('app', tail='/documents', get=get_params),
        },
    )


async def set_sign_session_document_status(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """Job for updating sign_session_document status"""

    next_status = data['next_status']
    previous_status = data['previous_status']

    async with services.db.acquire() as conn:
        await update_sign_session_document_status(
            conn,
            sign_session_id=data['sign_session_id'],
            next_status=SignSessionDocumentStatus(next_status),
            previous_status=SignSessionDocumentStatus(previous_status) if previous_status else None,
        )

    logger.info(
        'Updated sign_session_document status',
        extra={
            'document_id': data['document_id'],
            'new_status': next_status,
            'previous_status': previous_status,
        },
    )


async def autosend_document_job(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Autosend document to recipient or owner after sign
    """

    if not get_flag(FeatureFlags.AUTOSEND_DOCUMENTS):
        return

    company_edrpou: str = data['company_edrpou']
    document_id: str = data['document_id']
    role_id: str = data['role_id']
    request_source = Source(data['request_source'])

    async with services.db.acquire() as conn:
        user = await get_user(conn, role_id=role_id)
        if not user:
            logger.warning('User not found for autosending', extra=data)
            return

        try:
            await app_utils._autosend_document(
                conn=conn,
                company_edrpou=company_edrpou,
                document_id=document_id,
                user=user,
                request_source=request_source,
            )
        except Error as e:
            # Ignore all handled errors and log them, other unexpected errors will be raised
            # and handled by retry_config decorator
            logger.info('Error during autosending', extra={**data, 'error': e.to_dict()})
            return


async def send_document_view_limit_notification(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Send notification that the company reached the limit of document views
    """
    max_documents_count: int = data['max_documents_count']
    company_id: str = data['company_id']
    company_edrpou: str = data['company_edrpou']

    # increase limit to process over limit triggers
    max_documents_over_limit = int(max_documents_count * 1.5)

    async with services.db.acquire() as conn:
        active_rates = await select_active_company_rates(conn, company_id=company_id)
        documents_count = await select_documents_count_to_limit(
            conn=conn,
            company_edrpou=company_edrpou,
            limit=max_documents_over_limit,
        )
        await app_utils.notify_when_document_view_limit(
            conn=conn,
            company_id=company_id,
            documents_count=documents_count,
            max_documents_count=max_documents_count,
            active_rates=active_rates,
        )


async def send_delete_request_notifications_job(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Send notifications about new delete request to recipients
    """

    async with services.db_readonly.acquire() as conn:
        ctx = await get_delete_request_notification_context(
            conn=conn,
            data=data,
            logger=logger,
        )
        if not ctx:
            return
        # why don't we put it into ctx?
        # this is used not only for delete request notifications
        # so, to not make an extra query for data that might be not used
        company_ids = list({recipient.company_id for recipient in ctx.recipients_mapping.values()})
        company_has_pro_mapping = await get_companies_have_pro_or_higher_rate(
            conn=conn,
            company_ids=company_ids,
        )

    await send_emails_about_new_delete_request(
        initiator=ctx.initiator,
        emails_mapping=ctx.emails_mapping,
        message=not_none(ctx.message),
        companies_configs=ctx.companies_configs,
        company_has_pro_mapping=company_has_pro_mapping,
        recipients_mapping=ctx.recipients_mapping,
    )

    # DOC-6830 - Temporary disabled
    # await send_mobile_notifications_about_new_delete_request(
    #     initiator_role=initiator_role,
    #     emails_mapping=emails_mapping,
    # )


async def send_reject_document_notifications_job(
    _: web.Application,
    data: DataDict,
    __: logging.Logger,
) -> None:
    """
    Gather recipients and send reject document notifications
    """
    initiator_role_id: str | None = data['initiator_role_id']
    document_id: str = data['document_id']
    comment: str | None = data['comment']
    if not initiator_role_id:
        return

    async with services.db_readonly.acquire() as conn:
        initiator_user = await select_user(conn=conn, role_id=initiator_role_id)
        document = await select_document_by_id(conn=conn, document_id=document_id)
        if not document or not initiator_user:
            return

        initiator_config = await get_company_config(conn, company_id=initiator_user.company_id)

        notification_recipients = await select_reject_notification_recipients(
            conn=conn,
            document=document,  # type: ignore[arg-type]
            rejecter_edrpou=initiator_user.company_edrpou,
        )

    notification = RejectDocumentNotification(
        user=initiator_user,
        user_company_config=initiator_config,
        document=document,  # type: ignore[arg-type]
        involved_users=notification_recipients,
        comment=comment,
    )
    await notification.send()


async def get_delete_request_notification_context(
    conn: DBConnection,
    data: DataDict,
    logger: logging.Logger,
) -> GetDeleteRequestNotificationCtx | None:
    initiator_role_id: str = data['initiator_role_id']
    document_ids: list[str] = data['document_ids']
    message: str | None = data.get('message')

    initiator = await select_user(conn=conn, role_id=initiator_role_id)
    if not initiator:
        logger.info(
            msg='Initiator role not found for delete request notifications',
            extra={'role_id': initiator_role_id},
        )
        return None

    documents = await select_documents(conn, documents_ids=document_ids)

    # Get all involved users except the one who initiated delete request
    private_document_cte = (
        sa.select(
            [
                document_access_settings_private_table.c.edrpou,
                document_access_settings_private_table.c.document_id,
            ]
        )
        .select_from(document_access_settings_private_table)
        .where(
            sa.and_(
                document_access_settings_private_table.c.document_id.in_(document_ids),
                document_access_settings_private_table.c.edrpou != initiator.company_edrpou,
            )
        )
        .cte('private_document_cte')
    )
    is_private_query = sa.exists(
        sa.select([1])
        .select_from(private_document_cte)
        .where(
            sa.and_(
                private_document_cte.c.document_id == listing_table.c.document_id,
                private_document_cte.c.edrpou == listing_table.c.access_edrpou,
            )
        )
    )
    query = (
        sa.select(
            [
                sa.distinct(role_table.c.id.label('role_id')),
                user_table.c.email,
                user_table.c.first_name,
                user_table.c.language,
                role_table.c.user_role,
                role_table.c.can_receive_inbox,
                role_table.c.can_receive_comments,
                role_table.c.can_receive_rejects,
                role_table.c.can_receive_reminders,
                role_table.c.can_receive_reviews,
                role_table.c.can_receive_review_process_finished,
                role_table.c.can_receive_review_process_finished_assigner,
                role_table.c.can_receive_sign_process_finished,
                role_table.c.can_receive_sign_process_finished_assigner,
                role_table.c.can_receive_notifications,
                role_table.c.can_receive_access_to_doc,
                role_table.c.can_receive_delete_requests,
                company_table.c.id.label('company_id'),
                company_table.c.edrpou.label('company_edrpou'),
                company_table.c.name.label('company_name'),
                listing_table.c.document_id,
            ]
        )
        .select_from(
            user_active_role_company_join.join(
                listing_table,
                listing_table.c.access_edrpou == company_table.c.edrpou,
            )
        )
        .where(
            sa.and_(
                listing_table.c.document_id.in_(document_ids),
                sa.or_(
                    # user with direct access to document
                    listing_table.c.role_id == role_table.c.id,
                    # user can view all documents in the company
                    role_table.c.user_role == UserRole.admin.value,
                    sa.and_(
                        role_table.c.can_delete_document_extended.is_(True),
                        role_table.c.can_view_document.is_(True),
                        role_table.c.can_view_private_document.is_(True),
                    ),
                    # if private document - only user with can_view_private_document
                    sa.and_(
                        role_table.c.can_delete_document_extended.is_(True),
                        role_table.c.can_view_private_document.is_(True),
                        is_private_query,
                    ),
                    # if extended document - only user with can_view_document
                    sa.and_(
                        role_table.c.can_delete_document_extended.is_(True),
                        role_table.c.can_view_document.is_(True),
                        ~is_private_query,
                    ),
                ),
                # select only recipients
                listing_table.c.access_edrpou != initiator.company_edrpou,
            )
        )
    )
    recipients = await select_all(conn, query)

    # Generate recipients mapping for each document
    recipients_mapping: defaultdict[str, list[DBRow]] = defaultdict(list)
    for recipient in recipients:
        recipients_mapping[recipient.document_id].append(recipient)

    # Generate emails mapping
    emails_mapping: defaultdict[str, defaultdict[str, list[Document]]] = defaultdict(
        lambda: defaultdict(list)
    )
    # Sample: {
    #   '<EMAIL>': {
    #       'company_id_1': [
    #           document_1, document_2, ...,
    #       ], ...
    #   }, ...
    # }
    for document in documents:
        for user in recipients_mapping[document.id]:
            # Don't send an email for recipient if he refused to receive notifications
            if not can_receive_notification(user, NotificationType.delete_requests):
                continue
            emails_mapping[user.email][user.company_id].append(document)

    company_ids = list({recipient.company_id for recipient in recipients})
    companies_configs = await get_companies_configs_by_ids(conn, companies_ids=company_ids)

    if not emails_mapping:
        logger.info('No emails to send for revoke request notification')
        return None

    return GetDeleteRequestNotificationCtx(
        initiator=initiator,
        message=message,
        companies_configs=companies_configs,
        emails_mapping=emails_mapping,
        recipients_mapping={
            (row.email, row.company_id): DeleteRequestRecipient.from_row(row) for row in recipients
        },
    )


async def sync_recipients_date_received(
    _: web.Application,
    data: DataDict,
    __: logging.Logger,
) -> None:
    """
    After company registration, we try to find all documents that were sent to the company
    and set "date_received" to the date of the company registration for those documents

    If the company is already registered, we set "date_received" when the document was sent to
    the company. Check "update_recipients_date_received_raw" for more details.
    """
    company_edrpou: str = data['company_edrpou']
    date_registered_raw: str = data['date_registered']
    date_registered = datetime.fromisoformat(date_registered_raw)

    # Enable only after index "ix_document_recipients_edrpou_id_not_received" will be created
    if not get_flag(FeatureFlags.ENABLE_SYNC_RECIPIENTS_DATE_RECEIVED):
        return

    recipients_cte = (
        sa.select([document_recipients_table.c.id])
        .select_from(document_recipients_table)
        .where(
            sa.and_(
                document_recipients_table.c.edrpou == company_edrpou,
                document_recipients_table.c.date_sent.isnot(None),
                document_recipients_table.c.date_received.is_(None),
            )
        )
        .limit(1000)
        .cte('recipients_cte')
    )

    async with services.db.acquire() as conn:
        rows = await select_all(
            conn=conn,
            query=(
                sa.update(document_recipients_table)
                .values(date_received=date_registered)
                .where(sa.and_(document_recipients_table.c.id == recipients_cte.c.id))
                .returning(document_recipients_table.c.id)
            ),
        )

    if not rows:
        return

    # Retry this job until there will be no rows to update
    await schedule_sync_recipients_date_received_job(
        company_edrpou=company_edrpou,
        date_registered=date_registered,
    )

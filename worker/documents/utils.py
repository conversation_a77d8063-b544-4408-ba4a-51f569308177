from collections import defaultdict

from app.auth.constants import COMPANY_ID_REQUEST_KEY
from app.auth.db import select_base_user
from app.auth.schemas import CompanyConfig
from app.auth.types import User
from app.auth.utils import (
    get_default_company_config,
)
from app.documents.emailing import YOUTUBE_URL, build_delete_request_sender_line
from app.documents.types import Document
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import emailing, urls
from app.lib.database.types import DBRow
from app.lib.types import (
    DataDict,
)
from app.mobile.notifications import notifications as mobile_notifications
from app.services import services
from worker.documents.types import DeleteRequestRecipient


async def send_emails_about_new_delete_request(
    initiator: User,
    message: str,
    companies_configs: dict[str, CompanyConfig],
    emails_mapping: defaultdict[str, defaultdict[str, list[Document]]],
    company_has_pro_mapping: dict[str, bool],
    recipients_mapping: dict[tuple[str, str], DeleteRequestRecipient],
) -> None:
    """
    Parse emails_mapping and send emails
    """

    for email, documents_mapping in emails_mapping.items():
        recipient_doc_data = []
        for company_id, documents in documents_mapping.items():
            recipient = recipients_mapping.get((email, company_id))
            if not recipient:
                continue

            for document in documents:
                recipient_doc_data.append(
                    {
                        'document': document,
                        'url': urls.build_url(
                            route='app',
                            tail=f'/documents/{document.id}',
                            get={COMPANY_ID_REQUEST_KEY: company_id},
                        ),
                    }
                )

            if len(documents_mapping) == 1:
                subject = _('Ви отримали запит на видалення документа')
            else:
                subject = _('Ви отримали запит на видалення документів')

            company_config = companies_configs.get(company_id) or get_default_company_config()

            initiator_line = build_delete_request_sender_line(
                company_name=initiator.company_name,
                company_edrpou=initiator.company_edrpou,
                user_email=initiator.email,
                company_config=company_config,
            )
            context = {
                'recipient_company_edrpou': recipient.company_edrpou,
                'recipient_company_name': recipient.company_name,
                'initiator_line': initiator_line,
                'documents_data': recipient_doc_data,
                'initiator_message': message,
                'header': subject,
                # for pro advice block
                'image_sofia': urls.build_static_url('images/sofia.png'),
                'youtube_link': (
                    f'{YOUTUBE_URL}?utm_medium=email&utm_source=youtube_link'
                    '&utm_campaign=delete_documents_request'
                ),
                'enable_pro_advice_block': get_flag(FeatureFlags.ENABLE_PRO_ADVICE_BLOCK_IN_EMAILS)
                and not bool(company_has_pro_mapping.get(company_id)),
            }

            await emailing.send_email(
                recipient_mixed=email,
                subject=subject,
                template_name='delete_request_new',
                context=context,
                language=recipient.language,
            )


async def send_mobile_notifications_about_new_delete_request(
    initiator_role: DBRow,
    emails_mapping: DataDict,
) -> None:
    async with services.db_readonly.acquire() as conn:
        initiator_user = await select_base_user(conn=conn, email=initiator_role.user_email)

        for email, documents_mapping in emails_mapping.items():
            recipient = await select_base_user(conn, email=email)
            for _, documents in documents_mapping.items():
                for document in documents:
                    await mobile_notifications.send_push_notification_about_delete_request(
                        recipient=recipient,
                        partner=initiator_user,
                        document=document,
                    )

import logging
from collections import defaultdict

from aiohttp import web

from app.delayed_task.utils import get_unprocessed_delayed_tasks, mark_delayed_tasks_as_processed
from app.lib.datetime_utils import utc_now
from app.lib.types import DataDict
from app.services import services

BATCH_SIZE = 500


async def process_delayed_task(_: web.Application, __: DataDict, ___: logging.Logger) -> None:
    """
    Process delayed task.
    Read messages from delay queue (postgres) and send them to kafka.
    Read messages in batches to prevent timeouts.
    Schedule next job if there are more messages in the queue.
    """

    async with services.db.acquire() as conn:
        async with conn.begin():
            tasks = await get_unprocessed_delayed_tasks(
                conn=conn,
                execute_after_lte=utc_now(),
                limit=BATCH_SIZE,
            )
            if not tasks:
                return

            send_tasks = defaultdict(list)
            for task in tasks:
                send_tasks[task.topic].append(task.to_kafka_message())

            for topic, data in send_tasks.items():
                await services.kafka.send_records(topic, data)

            await mark_delayed_tasks_as_processed(
                conn=conn,
                ids=[task.id for task in tasks],
            )

from datetime import timedelta

from app.delayed_task.utils import (
    add_delayed_task,
    get_processed_delayed_tasks,
    get_unprocessed_delayed_tasks,
)
from app.lib.datetime_utils import utc_now
from app.services import services
from app.tests.common import prepare_app_client
from worker.topics import (
    ESPUTNIK_GENERATE_EVENT,
    PROCESS_DELAYED_TASK,
    SYNC_COMPANY_INFO_FROM_YOUCONTROL,
)


async def test_process_delayed_task(aiohttp_client):
    """
    Test that we can process delayed tasks.
    Given:
    - we have two delayed tasks in the database.
     - one task should be executed in the past
     - one task should be executed in the future
    When:
    - process delayed task job is executed
    Then:
    - we should send messages to kafka
    - we should delete processed tasks
    - task that scheduled in the future should not be processed
    """
    _, __ = await prepare_app_client(aiohttp_client)

    async with services.db.acquire() as conn:
        await add_delayed_task(
            conn=conn,
            topic=ESPUTNIK_GENERATE_EVENT,
            execute_after=utc_now() - timedelta(minutes=1),
            data={'email': '<EMAIL>'},
        )
        await add_delayed_task(
            conn=conn,
            topic=SYNC_COMPANY_INFO_FROM_YOUCONTROL,
            execute_after=utc_now() + timedelta(minutes=1),
            data={'edrpou': '12345678'},
        )

    # Act
    await services.kafka.send_record(PROCESS_DELAYED_TASK, {})

    # Assert
    assert len(services.kafka.messages) == 2
    assert services.kafka.messages[0][0] == 'vchasno-test-process-delayed-task'
    assert services.kafka.messages[1][0] == 'vchasno-test-esputnik-generate-event'

    async with services.db.acquire() as conn:
        # make sure that we have marked as processed
        tasks = await get_processed_delayed_tasks(conn=conn)
        assert len(tasks) == 1
        assert tasks[0].topic == ESPUTNIK_GENERATE_EVENT

        # make sure that we have a future task in the queue
        tasks = await get_unprocessed_delayed_tasks(conn=conn)
        assert len(tasks) == 1
        assert tasks[0].topic == SYNC_COMPANY_INFO_FROM_YOUCONTROL

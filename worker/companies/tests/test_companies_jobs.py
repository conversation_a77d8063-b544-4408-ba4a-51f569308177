import pytest

from app.auth.db import select_company_stat
from app.auth.enums import StatEntity
from app.services import services
from app.tags.tests.common import prepare_tag
from app.tests.common import (
    prepare_automation_template,
    prepare_client,
    prepare_document_field,
)
from worker import topics


@pytest.mark.parametrize(
    'entity',
    [StatEntity.role, StatEntity.tag, StatEntity.template, StatEntity.document_field],
)
async def test_update_company_entity_count(aiohttp_client, entity):
    _, _, user = await prepare_client(aiohttp_client)
    cid = user.company_id

    async with services.db.acquire() as conn:
        stat = await select_company_stat(conn, cid)
        assert stat is None

        if entity == StatEntity.role:
            # one role was initially created
            pass
        elif entity == StatEntity.tag:
            await prepare_tag(company_id=user.company_id)
        elif entity == StatEntity.template:
            await prepare_automation_template(conn, user)
        elif entity == StatEntity.document_field:
            await prepare_document_field(conn, user)

        data = {'company_id': cid, 'entity': entity.value}
        await services.kafka.send_record(topics.UPDATE_COMPANY_ENTITY_COUNT, data)
        stat = await select_company_stat(conn, cid)

    assert stat is not None
    assert getattr(stat, entity.value) == 1

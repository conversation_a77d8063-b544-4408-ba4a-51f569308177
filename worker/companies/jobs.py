import logging
from typing import assert_never

import sqlalchemy as sa
from aiohttp import web
from sqlalchemy.dialects.postgresql import insert

from app.auth import db as auth_db
from app.auth.enums import StatEntity
from app.auth.tables import company_statistic_table, company_table, role_table
from app.document_automation.db import count_document_automation_templates
from app.document_automation.tables import document_automation_template_table
from app.documents_fields.db import count_document_fields
from app.documents_fields.tables import documents_fields_table
from app.lib.types import DataDict
from app.models import select_all
from app.profile import utils as profile
from app.services import services
from app.tags.tables import tag_table
from worker import topics
from worker.utils import retry_config


@retry_config(max_attempts=5)
async def update_company_entity_count(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Update given entity count in companies_statistics table.

    :param_data:
        - company_id - statistic of this company will be updated
        - entity - `StatEntity` enum value of entity which will be updated
    """
    company_id: str = data['company_id']
    entity = StatEntity(data['entity'])
    extra = {'company_id': company_id}

    async with services.db.acquire() as conn:
        company = await auth_db.select_company_by_id(conn, company_id)
        if not company:
            logger.warning('Company not found', extra=extra)
            return

        if entity == StatEntity.role:
            count = await auth_db.count_company_roles(conn, company_id)
        elif entity == StatEntity.tag:
            from app.tags.db import count_tags

            count = await count_tags(conn, company_id)
        elif entity == StatEntity.template:
            count = await count_document_automation_templates(conn, company_id)
        elif entity == StatEntity.document_field:
            count = await count_document_fields(conn, company_id)
        else:
            assert_never(entity.value)

        stat = await auth_db.update_company_entity_count(
            conn, company_id, entity=entity, count=count
        )

    if stat.roles_count and stat.roles_count == 1:
        # Activate company just after first active role was added to it
        await profile.send_create_initial_free_rate_job(company_id=company_id)


async def fill_companies_stats(_: web.Application, data: DataDict, logger: logging.Logger) -> None:
    offset = data.get('offset', 0)
    logger.info('Start filling companies stats', extra={'offset': offset})
    async with services.db.acquire() as conn:
        rows = await select_all(
            conn,
            sa.select([company_table.c.id])
            .order_by(company_table.c.date_created.desc())
            .offset(offset)
            .limit(1000),
        )
        if not rows:
            logger.info('Finished filling companies stats')
            return

        ids = [row.id for row in rows]
        await conn.execute(
            insert(company_statistic_table)
            .from_select(
                ['company_id', 'tags_count'],
                (
                    sa.select(
                        [
                            tag_table.c.company_id,
                            sa.func.count(tag_table.c.company_id).label('tags_count'),
                        ]
                    )
                    .select_from(tag_table)
                    .where(tag_table.c.company_id.in_(ids))
                    .group_by(tag_table.c.company_id)
                ),
            )
            .on_conflict_do_update(
                index_elements=[company_statistic_table.c.company_id],
                set_={'tags_count': sa.text('EXCLUDED.tags_count')},
            )
        )
        await conn.execute(
            insert(company_statistic_table)
            .from_select(
                ['company_id', 'roles_count'],
                (
                    sa.select(
                        [
                            role_table.c.company_id,
                            sa.func.count(role_table.c.company_id).label('roles_count'),
                        ]
                    )
                    .select_from(role_table)
                    .where(role_table.c.company_id.in_(ids))
                    .group_by(role_table.c.company_id)
                ),
            )
            .on_conflict_do_update(
                index_elements=[company_statistic_table.c.company_id],
                set_={'roles_count': sa.text('EXCLUDED.roles_count')},
            )
        )
        await conn.execute(
            insert(company_statistic_table)
            .from_select(
                ['company_id', 'templates_count'],
                (
                    sa.select(
                        [
                            document_automation_template_table.c.company_id,
                            sa.func.count(document_automation_template_table.c.company_id).label(
                                'templates_count'
                            ),
                        ]
                    )
                    .select_from(document_automation_template_table)
                    .where(document_automation_template_table.c.company_id.in_(ids))
                    .group_by(document_automation_template_table.c.company_id)
                ),
            )
            .on_conflict_do_update(
                index_elements=[company_statistic_table.c.company_id],
                set_={'templates_count': sa.text('EXCLUDED.templates_count')},
            )
        )
        await conn.execute(
            insert(company_statistic_table)
            .from_select(
                ['company_id', 'document_fields_count'],
                (
                    sa.select(
                        [
                            documents_fields_table.c.company_id,
                            sa.func.count(documents_fields_table.c.company_id).label(
                                'document_fields_count'
                            ),
                        ]
                    )
                    .select_from(documents_fields_table)
                    .where(documents_fields_table.c.company_id.in_(ids))
                    .group_by(documents_fields_table.c.company_id)
                ),
            )
            .on_conflict_do_update(
                index_elements=[company_statistic_table.c.company_id],
                set_={'document_fields_count': sa.text('EXCLUDED.document_fields_count')},
            )
        )
    logger.info('Company stats filled successfully', extra={'offset': offset})

    await services.kafka.send_record(topics.FILL_COMPANIES_STATS, {'offset': offset + 1000})

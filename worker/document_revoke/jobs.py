import logging

from aiohttp import web

from app.lib.helpers import not_none
from app.lib.types import DataDict
from app.services import services
from worker.document_revoke.utils import (
    send_emails_about_revoke_completed,
    send_emails_about_revoke_initiated,
    send_emails_about_revoke_rejected,
)
from worker.documents.jobs import get_delete_request_notification_context


async def send_revoke_initiated_notifications_job(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Send notifications about new revoke request to recipients
    """

    # Why are delete request recipients used here instead of revoke request recipients?
    # We send email to the same users as for delete request
    async with services.db_readonly.acquire() as conn:
        ctx = await get_delete_request_notification_context(
            conn=conn,
            data=data,
            logger=logger,
        )
        if not ctx:
            return

    await send_emails_about_revoke_initiated(
        initiator=ctx.initiator,
        emails_mapping=ctx.emails_mapping,
        message=not_none(ctx.message),
        companies_configs=ctx.companies_configs,
        recipients_mapping=ctx.recipients_mapping,
    )


async def send_revoke_completed_notifications_job(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Notify about completed revocation process
    """
    async with services.db_readonly.acquire() as conn:
        ctx = await get_delete_request_notification_context(
            conn=conn,
            data=data,
            logger=logger,
        )
        if not ctx:
            return

    await send_emails_about_revoke_completed(
        initiator=ctx.initiator,
        companies_configs=ctx.companies_configs,
        emails_mapping=ctx.emails_mapping,
        recipients_mapping=ctx.recipients_mapping,
    )


async def send_revoke_rejected_notifications_job(
    _: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Send notifications about aborted revocation process
    """

    # Why are delete request recipients used here instead of revoke request recipients?
    # We send email to the same users as for delete request
    async with services.db_readonly.acquire() as conn:
        ctx = await get_delete_request_notification_context(
            conn=conn,
            data=data,
            logger=logger,
        )
        if not ctx:
            return

    await send_emails_about_revoke_rejected(
        initiator=ctx.initiator,
        message=not_none(ctx.message),
        companies_configs=ctx.companies_configs,
        emails_mapping=ctx.emails_mapping,
        recipients_mapping=ctx.recipients_mapping,
    )

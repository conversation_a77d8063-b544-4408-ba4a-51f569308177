from functools import lru_cache

from app.config import read_app_config


@lru_cache(maxsize=1)
def _get_prefix() -> str:
    """
    Get topic prefix.
    We should read it here instead using app.services.services.config
    Topic should be defined before
    """

    config = read_app_config()
    if kafka_config := config.kafka:
        return kafka_config.topic_prefix

    return 'vchasno'


def _get_topic(topic_name: str) -> str:
    prefix = _get_prefix()
    return f'{prefix}-{topic_name}'


# Company jobs
UPDATE_COMPANY_ENTITY_COUNT = _get_topic('update-company-entity-count')
CREATE_INITIAL_FREE_RATE = _get_topic('create-initial-free-rate')
FILL_COMPANIES_STATS = _get_topic('fill-companies-stats')

# Billing
BILLS = _get_topic('bills')
DELETE_BILLING_ACCOUNTS = _get_topic('delete-billing-accounts')
ACTIVATE_COMPANY_RATES = _get_topic('activate-company-rate')
ACTIVATE_COMPANY_EXTENSIONS = _get_topic('activate-company-extensions')
DEACTIVATE_COMPANY_EXTENSIONS_TRIAL = _get_topic('deactivate-company-extensions-trial')
DEACTIVATE_COMPANY_RATES = _get_topic('deactivate-company-rate')
SA_ACTIVATE_COMPANIES_RATES = _get_topic('sa-active-companies-rates')
MIGRATE_TO_NEW_RATE = _get_topic('migrate-to-new-rate')
FETCH_PRIVATBANK_TRANSACTIONS = _get_topic('fetch-privatbank-transactions')
FETCH_PUMB_TRANSACTIONS = _get_topic('fetch-pumb-transactions')
PROCESS_BANK_TRANSACTION = _get_topic('process-bank-transaction')
SEND_PAYMENT_SUCCESSFULL_EMAIL = _get_topic('send-payment-successfull-email')
SEND_GOOGLE_ANALYTICS_EVENT_FOR_BILL_PAYMENT = _get_topic('send-ga-event-for-bill-payment')

# Documents
UPDATE_DOCS_DATE_DELIVERED = _get_topic('update-docs-date-delivered')
UPDATE_SIGN_SESSION_DOCS_DATE_DELIVERED = _get_topic('update-sign-session-docs-date-delivered')
UPDATE_HAS_ROLE_SIGNED_DOCUMENTS = _get_topic('update-has-role-signed-documents')
CREATE_DOCUMENTS_TAGS_BY_CONTACT = _get_topic('create-documents-tags-by-contact')
CREATE_DOCUMENT_ACCESS_ON_DOCUMENT_TAG_PART_1 = _get_topic('tags-access')
CREATE_DOCUMENT_ACCESS_ON_DOCUMENT_TAG_PART_2 = _get_topic('tags-access-for-document')
CREATE_DOCUMENT_ACCESS_ON_ROLE_TAG = _get_topic('create-role-tag-access')
DELETE_DOCUMENT_ACCESS_SOURCE = _get_topic('delete-document-access-source')
SEND_TO_INDEXATOR_BY_IDS = _get_topic('send-to-indexator-by-ids')
SEND_TO_INDEXATOR_BY_LISTINGS_IDS = _get_topic('send-to-indexator-by-listings-ids')
REMOVE_DOCUMENTS_FROM_INDEX = _get_topic('remove-documents-from-index')
AUTOSEND_DOCUMENT = _get_topic('autosend-document')
SEND_DOCUMENT_VIEW_LIMIT_NOTIFICATION = _get_topic('send-document-view-limit-notification')
SYNC_RECIPIENTS_DATE_RECEIVED = _get_topic('sync-recipients-date-received')

# SignSessions
UPDATE_SIGN_SESSION_DOCUMENT_STATUS = _get_topic('update-sign-session-document-status')

# Comments
SEND_COMMENT_NOTIFICATION = _get_topic('send-comment-notification')
INDEX_COMMENTS = _get_topic('index-comments')
REMOVE_COMMENTS_FROM_INDEX = _get_topic('remove-comments-from-index')
SEND_COMMENTS_TO_INDEX = _get_topic('send-comment-to-index')

# Signatures
DOCUMENT_SIGNATURE_ADDED_EVENT = _get_topic('document-signature-added-event')

DOCUMENTS_REJECTED_EVENT = _get_topic('documents-rejected-event')

# Document automation jobs
START_DOCUMENT_AUTOMATION = _get_topic('start-document-automation')
FIND_DOCUMENT_AUTOMATION = _get_topic('find-document-automation')
APPLY_DOCUMENT_AUTOMATION_TEMPLATE = _get_topic('apply-document-automation-template')
# Deprecated can be removed
APPLY_DOCUMENT_TEMPLATE = _get_topic('apply-document-template')

# Different type of notification
INITIATE_FALLBACK_INBOX_DOCUMENT_NOTIFICATIONS = _get_topic(
    'initiate-fallback-inbox-document-notifications'
)
SEND_INBOX_FALLBACK_DOCUMENT_NOTIFICATIONS = _get_topic(
    'send-inbox-fallback-document-notifications'
)
SEND_BLACKBOX_INBOX_DOCUMENTS_NOTIFICATION = _get_topic(
    'send-blackbox-inbox-documents-notification'
)
INVITE_COWORKERS = _get_topic('invite-coworkers')
MULTI_DOWNLOAD = _get_topic('multi-download')
ARCHIVED_DOCUMENTS_DOWNLOAD = _get_topic('archived-documents-download')
SEND_NOTIFICATION_FREE_TRIAL_IS_ENDING = _get_topic('send-notification-free-trial-is-ending')
SEND_REVIEW_REJECT_NOTIFICATION = _get_topic('send-email-review-reject')
SEND_EMAIL_TO_DOCUMENT_OWNER = _get_topic('send-email-to-document-owner')
SEND_FIRST_NOTIFICATION_TO_SIGNERS = _get_topic('first-signers-notification')
SEND_NOTIFICATION_TO_SIGNERS = _get_topic('send-notification-to-signers')
SEND_NOTIFICATION_ABOUT_DOCUMENT_ACCESS = _get_topic('send-notification-about-access')
SEND_NOTIFICATION_ABOUT_CHANGE_BASIC_FREE_RATE = _get_topic(
    'send-notification-about-change-basic-free-rate'
)
SEND_MULTILATERAL_DOCUMENT_TO_RECIPIENTS = _get_topic('notification-multilateral')
SEND_MULTILATERAL_DOCUMENT_NOTIFICATION = _get_topic('send-multilateral-document-notification')
SEND_DOCUMENT_TO_RECIPIENT = _get_topic('send-document-to-recipient')
SEND_SIGN_PROCESS_NOTIFICATION = _get_topic('sign-process-notification')
SEND_SIGNED_BY_COWORKERS_DOCUMENTS = _get_topic('signed-by-coworkers-documents')
SEND_REMINDERS_TO_UNREGISTERED_USERS = _get_topic('send-reminders-to_unreg-users')
START_SENDING_PENDING_REVIEW_REMINDERS = _get_topic('start-sending-pending-review-reminders')
SEND_PENDING_REVIEW_REMINDER = _get_topic('send-pending-review-reminder')
SEND_REVIEW_REQUEST_TELEGRAM = _get_topic('send-telegram-review-request')
SEND_REVIEW_REQUEST_TRIGGER_NOTIFICATION = _get_topic('send-trigger-notification-review-request')
SEND_DOCUMENT_STATUS_CALLBACK = _get_topic('send-document-status')
SEND_SMS_TO_DOCUMENT_RECEIVER = _get_topic('send-sms-to-receiver')
SEND_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS = _get_topic(
    'send-daily-notification-about-finished-documents'
)
SCHEDULE_DAILY_NOTIFICATION_ABOUT_FINISHED_DOCUMENTS = _get_topic(
    'schedule-daily-notification-about-finished-documents'
)
SEND_ACCEPTED_DOCUMENTS = _get_topic('send-accepted-documents')
SEND_DAILY_FEEDBACKS = _get_topic('send-daily-feedbacks')
SEND_REMINDER_ABOUT_NEW_DOCUMENTS = _get_topic('send-reminder-about-new-documents')
SEND_REMINDER_ABOUT_TOKEN_EXPIRATION = _get_topic('send-reminder-about-token-expiration')
SEND_DELETE_REQUEST_NOTIFICATIONS = _get_topic('send-delete-request-notifications')
SEND_REJECT_DOCUMENT_NOTIFICATIONS = _get_topic('send-reject-document-notifications')

SEND_REVOKE_INITIATED_NOTIFICATIONS = _get_topic('send-revoke-initiated-notifications')
SEND_REVOKE_REJECTED_NOTIFICATIONS = _get_topic('send-revoke-rejected-notifications')
SEND_REVOKE_COMPLETED_NOTIFICATIONS = _get_topic('send-revoke-finished-notifications')

# System jobs
CREATE_DOCUMENT_ACTIONS_TABLE_PARTITION = _get_topic('create-document-actions-table-partition')
CREATE_USER_ACTIONS_TABLE_PARTITION = _get_topic('create-user-actions-table-partition')
# remove old s3 files that are not used anymore in the system
REMOVE_OLD_S3_FILES = _get_topic('remove-old-s3-files')

# Low-balance billing notifications
INITIATE_LOW_BALANCE_BILLING_NOTIFICATIONS = _get_topic(
    'initiate-low-balance-billing-notifications'
)
SEND_LOW_BALANCE_BILLING_NOTIFICATION = _get_topic('send-low-balance-billing-notification')

# Documents AI
DOCUMENT_STRUCTURED_DATA_EXTRACTION = _get_topic('documents-ai-structured-data-extraction')


# Abandoned registation
INITIATE_REMINDERS_FOR_ABANDONED_REGISTRATION = _get_topic(
    'initiate-reminders-for-abandoned-registration'
)
SEND_REMINDER_FOR_ABANDONED_REGISTRATION = _get_topic('send-reminder-for-abandoned-registration')

# Unsigned documents
INITIATE_REMINDERS_FOR_UNSIGNED_DOCUMENTS = _get_topic('initiate-reminders-for-unsigned-documents')
SEND_REMINDER_FOR_UNSIGNED_DOCUMENTS = _get_topic('send-reminder-for-unsigned-documents')

# Unpaid bills
REMIND_ABOUT_EMPLOYEES_EXTENSION_BILL_WHEN_TRIAL_EXPIRES = _get_topic(
    'remind-about-employees-extension-bill-when-trial-expires'
)
GATHER_REMINDERS_FOR_UNPAID_BILLS = _get_topic('gather-reminders-for-unpaid-bills')
INITIATE_REMINDERS_FOR_UNPAID_BILL = _get_topic('initiate-reminders-for-unpaid-bill')
SEND_REMINDER_FOR_UNPAID_BILL = _get_topic('send-reminder-for-unpaid-bill')

# New review requests
INITIATE_NEW_REVIEW_REQUEST_EMAILS = _get_topic('initiate-new-review-request-emails')
SEND_NEW_REVIEW_REQUEST_EMAIL = _get_topic('send-new-review-request-email')
SEND_NEW_REVIEW_REQUEST_MOBILE_PUSH_NOTIFICATION = _get_topic(
    'send-new-review-request-mobile-notification'
)

# Documents versions
INITIATE_NEW_DOCUMENT_VERSION_NOTIFICATIONS = _get_topic(
    'initiate-new-document-version-notifications'
)
SEND_NEW_DOCUMENT_VERSION_NOTIFICATION = _get_topic('send-new-document-version-notification')
SEND_VERSION_REVOKED_NOTIFICATION = _get_topic('send-version-revoked-notification')

# New employee registrations
INITIATE_REMINDERS_FOR_NEW_SUCCESSFULLY_REGISTERED_EMPLOYEE = _get_topic(
    'initiate-reminders-for-new-successfully-registered-employee'
)
SEND_REMINDER_FOR_NEW_SUCCESSFULLY_REGISTERED_EMPLOYEE = _get_topic(
    'send-reminder-for-new-successfully-registered-employee'
)
INITIATE_REMINDERS_FOR_FAILED_TO_REGISTER_EMPLOYEE = _get_topic(
    'initiate-reminders-for-failed-to-register-employee'
)
SEND_REMINDER_FOR_FAILED_TO_REGISTER_EMPLOYEE = _get_topic(
    'send-reminder-for-failed-to-register-employee'
)

# Roles
RECALCULATE_SIGNATURES_COUNT_FOR_USEFUL_META = _get_topic(
    'recalculate-signatures-count-for-useful-meta'
)
RECALCULATE_REVIEWS_COUNT_FOR_USEFUL_META = _get_topic('recalculate-reviews-count-for-useful-meta')

# Contact synchronization
SYNC_CONTACTS_FIRST_STEP = _get_topic('sync-contacts-first-step')
SYNC_CONTACTS_SECOND_STEP = _get_topic('sync-contacts-second-step')
SYNC_CONTACTS_THIRD_STEP = _get_topic('sync-contacts-third-step')
SYNC_CONTACTS_COLLECT_FROM_API = _get_topic('sync-contacts-collect-from-api')
SYNC_CONTACTS_SET_RECIPIENTS = _get_topic('sync-contacts-set-recipients')

ESPUTNIK_UPDATE_CONTACT = _get_topic('esputnik-update-contact')
ESPUTNIK_ADD_CONTACT = _get_topic('esputnik-add-contact')
ESPUTNIK_GENERATE_EVENT = _get_topic('esputnik-generate-event')
ESPUTNIK_COMPANY_CHECK_EVENT = _get_topic('esputnik-company-check-event')
ESPUTNIK_FIRST_INCOMING_SIGNING_EVENT = _get_topic('esputnik-first-incoming-signing-event')
ESPUTNIK_DOCUMENTS_SENT_EVENT = _get_topic('esputnik-documents-sent-event')
ESPUTNIK_GENERATE_TOV_TRIAL_EXPIRING_EVENT = _get_topic(
    'esputnik-tov-generate-trial-expiring-event',
)
ESPUTNIK_GENERATE_RATE_EXPIRING_EVENT = _get_topic('esputnik-generate-rate-expiring-event')
ESPUTNIK_COMPANY_REGISTRATION_SEND_EVENT = _get_topic('esputnik-company-registration-send-event')
ESPUTNIK_SEND_EVENT_TO_USERS = _get_topic('esputnik-send-event-to-users')
ESPUTNIK_SEND_TRIAL_ENABLED_EVENT = _get_topic('esputnik-send-trial-enabled-event')
ESPUTNIK_GENERATE_EMPLOYEES_LIMIT_REACHED_EVENT = _get_topic(
    'esputnik-generate-employees-limit-reached-event'
)
ESPUTNIK_NEW_POSITION_SET_EVENT = _get_topic('esputnik-new-position-set-event')
ON_DOCUMENT_CHARGE_EVENT = _get_topic('on-document-charge-event')

# CRM
CRM_SEND_EVENT = _get_topic('crm-send-event')
CRM_SYNC_RATES = _get_topic('crm-sync-rates')

DELETE_OLD_TRIGGER_NOTIFICATION = _get_topic('delete-old-notification')
CREATE_TRIGGER_NOTIFICATION = _get_topic('create-trigger-notification')
NOTIFICATION_ABOUT_COMPANY_REGISTRATION = _get_topic('notification-company-reg')
NOTIFICATION_INVITE_COMPANIES = _get_topic('invite-companies')
UPDATE_NOTIFICATION_INVITE_COMPANIES = _get_topic('update-invite-companies')
DELETE_FULFILLED_TRIGGER_NOTIFICATION = _get_topic('delete-fulfilled-notification')
CLEANUP_OLD_MOBILE_NOTIFICATIONS = _get_topic('cleanup-old-mobile-notifications')
DELETE_REVIEW_REQUESTS_DUPLICATES = _get_topic('delete-review-requests-duplicates')

ACTIVATE_BANNER = _get_topic('activate-banner')

USER_EMAIL_CHANGE_UPDATE_RECIPIENTS = _get_topic('user-email-change-update-recipients')
USER_EMAIL_CHANGE_UPDATE_AUTOMATIONS = _get_topic('user-email-change-update-automations')
USER_EMAIL_CHANGE_UPDATE_CONTACTS = _get_topic('user-email-change-update-contacts')
USER_EMAIL_CHANGE_NOTIFY_ADMINS = _get_topic('user-email-change-notify-admins')

MIGRATION_ENABLE_PHONE_AUTH = _get_topic('migration-enable-phone-auth')

MIGRATE_LATEST_RECIPIENTS = _get_topic('migrate-latest-recipients')
MIGRATE_SIGN_REVIEW_PERMISSION = _get_topic('migrate-sign-review-permission')

# Opening access jobs
CREATE_DOCUMENTS_ROLE_ACCESS = _get_topic('create-documents-role-access')

CLEANUP_UNSUBSCRIPTIONS = _get_topic('cleanup-unsubscription')

# EDI topics
# TODO: EDI_SEND_DOCUMENT_FINISH_STATUS deprecated remove after deploy
EDI_SEND_DOCUMENT_FINISH_STATUS = _get_topic('send-document-finish-status-to-edi')
EDI_SEND_EDI_REQUEST = _get_topic('send-edi-request-to-edi')

# Vchasno profile
START_VCHASNO_PROFILE_SYNC = _get_topic('start-vchasno-profile-sync')
SEND_VCHASNO_PROFILE_SYNC = _get_topic('send-vchasno-profile-sync')


# Service topics
PROCESS_DELAYED_TASK = _get_topic('process-delayed-task')

# Elastic
SEND_DOCUMENTS_TO_INDEXATOR = _get_topic('send-documents-to-indexator')
REINDEX_DOCUMENTS = _get_topic('reindex-documents')
REINDEX_REVIEWED_DOCUMENTS = _get_topic('reindex-reviewed-documents')

# Antivirus
ANTIVIRUS_CHECK = _get_topic('antivirus-check')
ANTIVIRUS_CHECK_DRAFT = _get_topic('antivirus-check-draft')

# Event report (export) requests
USER_ACTIONS_REPORT_PREPARE = _get_topic('user-actions-report-prepare')
DOCUMENT_ACTIONS_REPORT_PREPARE = _get_topic('document-actions-report-prepare')
ACTIONS_REPORT_SEND_EMAIL = _get_topic('actions-report-send-email')
# For cleaning up old actions report files
CLEANUP_OLD_ACTIONS_REPORT_FILES = _get_topic('cleanup-old-actions-report-files')
REMOVE_ACTIONS_REPORT_FILE = _get_topic('remove-actions-report-file')

# Sync company info from youcontrol
SYNC_COMPANY_INFO_FROM_YOUCONTROL = _get_topic('sync-company-info-from-youcontrol')

# Sync roles of a user
SYNC_USER_ROLES = _get_topic('sync-user-roles')

# ==========================
# Groups
# ==========================
GROUPS_CHANGED = _get_topic('groups-changed')

# Contacts
INVITE_UNREGISTERED_CONTACTS = _get_topic('invite-contacts')
INDEX_CONTACT_RECIPIENTS = _get_topic('index-contact-recipients')
DELETE_CONTACTS_FROM_INDEX = _get_topic('delete-contacts-from-index')
DELETE_CONTACTS_PERSONS_FROM_INDEX = _get_topic('delete-contacts-persons-from-index')
REINDEX_CONTACTS_RECIPIENTS_BY_CONTACTS = _get_topic('reindex-contacts-recipients-by-contacts')
REINDEX_CONTACTS_RECIPIENTS_BY_COMPANIES = _get_topic('reindex-contacts-recipients-by-companies')
REINDEX_CONTACTS_RECIPIENTS_BY_ROLES = _get_topic('reindex-contacts-recipients-by-roles')

# ==========================
# Drafts
# ==========================
CLEANUP_OLD_DRAFTS = _get_topic('cleanup-old-drafts')

# AI
INITIATE_BULK_SCHEDULED_DOCUMENT_META_SUGGESTIONS = _get_topic(
    'initiate-bulk-scheduled-document-meta-suggestions'
)
PROCESS_SCHEDULED_DOCUMENT_META_SUGGESTION = _get_topic(
    'process-scheduled-document-meta-suggestion'
)

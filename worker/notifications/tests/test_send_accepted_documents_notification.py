import datetime
import logging

from app.reviews.enums import ReviewType
from app.tests.common import (
    TimeManager,
    prepare_client,
    prepare_document_data,
    prepare_review,
    prepare_review_requests,
    prepare_user_data,
)
from worker.reviews.jobs import send_accepted_documents_job

logger = logging.getLogger(__name__)


async def test_send_accepted_documents(aiohttp_client, mailbox):
    app, client, user_1 = await prepare_client(aiohttp_client)

    async with app['db'].acquire() as conn:
        async with TimeManager(conn) as time:
            # Some random day, but not weekend
            date = datetime.datetime(year=2018, month=8, day=14)
            await time.set_now(date)

            await send_accepted_documents_job(app, {}, logger)
            assert len(mailbox) == 0
            user_2 = await prepare_user_data(app, email='<EMAIL>')
            user_3 = await prepare_user_data(app, email='<EMAIL>')
            user_4 = await prepare_user_data(app, email='<EMAIL>')

            await send_accepted_documents_job(app, {}, logger)
            assert len(mailbox) == 0

            doc = await prepare_document_data(app, user_1)

            await prepare_review_requests(client, doc, user_1, reviewers=[user_2])
            assert len(mailbox) == 1
            await prepare_review(client, user=user_2, document=doc, review_type=ReviewType.approve)
            await send_accepted_documents_job(app, {}, logger)
            assert len(mailbox) == 2

            await prepare_review(client, user=user_3, document=doc, review_type=ReviewType.approve)
            await send_accepted_documents_job(app, {}, logger)
            assert len(mailbox) == 3

            await prepare_review(client, user=user_4, document=doc, review_type=ReviewType.reject)
            await send_accepted_documents_job(app, {}, logger)
            # Email about rejecting document but not about all accepted
            assert len(mailbox) == 4

            await send_accepted_documents_job(app, {}, logger)
            assert len(mailbox) == 4

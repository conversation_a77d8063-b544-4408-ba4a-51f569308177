from app.notifications.db import insert_unsubscription, is_unsubscribed
from app.tests.common import prepare_client, prepare_user_data
from cron.jobs import cleanup_unsubscription


async def test_cleanup_unsubscription(aiohttp_client):
    app, client, user = await prepare_client(aiohttp_client)

    email = '<EMAIL>'

    async with app['db'].acquire() as conn:
        # clean up work even if no unsubscribed users
        await cleanup_unsubscription(app)
        assert not await is_unsubscribed(conn, email)

        await insert_unsubscription(conn, email=email)
        assert await is_unsubscribed(conn, email)

        # after clean up unregistered user still unsubscribed
        await cleanup_unsubscription(app)
        assert await is_unsubscribed(conn, email)

        # after user registration user still unsubscribed,
        # until clean will be called
        await prepare_user_data(app, email=email)
        assert await is_unsubscribed(conn, email)

        # after clean up registered user must be removed from
        # unsubscription table
        await cleanup_unsubscription(app)
        assert not await is_unsubscribed(conn, email)

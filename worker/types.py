from __future__ import annotations

import logging
from collections.abc import Awaitable, Callable
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>
from enum import auto
from typing import Any, NamedTuple

import psycopg2
from aiohttp import web

from app.lib.enums import NamedEnum
from app.lib.types import DataDict

JobFunction = Callable[[web.Application, DataDict, logging.Logger], Awaitable[Any]]
ExceptionTuple = tuple[type[BaseException], ...]

RETRY_EXCEPTIONS: ExceptionTuple = (TimeoutError, psycopg2.OperationalError)


@dataclass
class DelayedJob:
    """Use it as readonly object only!"""

    topic: str
    value: DataDict
    delay_min: int
    log_extra: DataDict
    job_hash: int

    def __hash__(self) -> int:
        return self.job_hash

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, DelayedJob):
            return NotImplemented
        return self.job_hash == other.job_hash


class WorkerType(NamedEnum):
    """
    Checklist for creating new worker type
    1. Add configuration for deployment
        - https://gitlab.vchasno.com.ua/devops/helm/vchasno-app/edo/-/tree/dev/templates/worker
    2. Setup proper values for dev/prod environments
        - https://gitlab.vchasno.com.ua/devops/helm/vchasno-app/edo/-/tree/dev/values
    3. Add new enum field
        - worker/types.py::WorkerType
    4. Create new jobs mapping and assign them to worker
        - worker/jobs.py
        (Don't forget to add new mapping to ALL_JOBS list)
    """

    all_jobs = auto()
    general = auto()
    antivirus = auto()
    reports = auto()
    document_update = auto()
    comments = auto()
    document_process = auto()


class RetryConfig(NamedTuple):
    max_attempts: int
    max_age: timedelta | None
    exceptions: ExceptionTuple
    delay: int

    # By default, we re-raise exception to collect metrics about failed jobs and add logs.
    # But sometimes when you need just to retry a job without polluting logs and metrics,
    # you can set this flag to False.
    raise_on_exception: bool

from api.downloads.types import ArchiveConfig
from api.downloads.utils import get_documents_archive_s3_key
from app.directories.tests.utils import prepare_directory
from app.tests.common import (
    TEST_EXTERNAL_KEY_SIGNATURE_CONTENT,
    TEST_EXTERNAL_STAMP_SIGNATURE_CONTENT,
    prepare_client,
    prepare_document_data,
    prepare_signature_data,
    unzip_archive_test_util,
)
from worker.downloading.utils import create_archive, generate_archive


async def test_create_multi_archive(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    doc1 = await prepare_document_data(
        app,
        user,
        title='doc1',
        content=b'doc1',
    )
    doc2 = await prepare_document_data(app, user, title='doc2', content=b'doc2')
    doc3 = await prepare_document_data(app, user, title='doc3', content=b'doc3')
    await prepare_signature_data(app, user, doc1, update_access=False)

    # Act
    ctx = await create_archive(
        role_id=user.role_id,
        file_group=[doc1.id, doc2.id, doc3.id],
        archive_name='test_archive_name',
        archive_config=ArchiveConfig(
            in_one_folder=False,
        ),
    )

    # Assertions
    assert ctx.files_count == 3
    assert ctx.archive_id is not None

    archive_content = s3_emulation.files[
        get_documents_archive_s3_key(archive_id=ctx.archive_id)
    ].body
    assert len(archive_content) > 0

    expected = sorted(
        [
            'doc1/doc1.pdf',
            'doc1/doc1. Pidpys 11111111.p7s',
            'doc1/doc1. Pechatka 11111111.p7s',
            'doc1/Kvytantsiia.pdf',
            'doc1/Instruktsiia.pdf',
            'doc3/doc3.pdf',
            'doc3/Instruktsiia.pdf',
            'doc2/doc2.pdf',
            'doc2/Instruktsiia.pdf',
        ]
    )
    result = await unzip_archive_test_util(body=archive_content)
    assert sorted(result.keys()) == expected
    assert result['doc1/doc1.pdf'] == b'doc1'
    assert result['doc2/doc2.pdf'] == b'doc2'
    assert result['doc3/doc3.pdf'] == b'doc3'
    assert result['doc1/doc1. Pidpys 11111111.p7s'] == TEST_EXTERNAL_KEY_SIGNATURE_CONTENT


async def test_create_archive_with_directories_tree(aiohttp_client, s3_emulation):
    app, client, user = await prepare_client(aiohttp_client, is_admin=True)
    d1 = await prepare_directory(
        name='d1',
        company_id=user.company_id,
    )
    d2 = await prepare_directory(
        name='d2',
        company_id=user.company_id,
        parent_id=d1.id,
    )
    doc1 = await prepare_document_data(
        app,
        user,
        title='doc1',
        is_archived=True,
        directory_id=d2.id,
        content=b'doc1',
    )
    doc2 = await prepare_document_data(
        app, user, title='doc2', is_archived=True, directory_id=d1.id, content=b'doc2'
    )
    doc3 = await prepare_document_data(app, user, title='doc3', is_archived=True, content=b'doc3')
    await prepare_signature_data(app, user, doc1, update_access=False)

    # Structure that represents final ZIP archive structure: file_path/file_name: file_content
    expected_result = {
        'd1/d2/doc1/doc1.pdf': b'doc1',
        'd1/doc2/doc2.pdf': b'doc2',
        'doc3/doc3.pdf': b'doc3',
        'd1/d2/doc1/doc1. Pidpys 11111111.p7s': TEST_EXTERNAL_KEY_SIGNATURE_CONTENT,
        'd1/d2/doc1/doc1. Pechatka 11111111.p7s': TEST_EXTERNAL_STAMP_SIGNATURE_CONTENT,
    }

    archive_id = await generate_archive(
        role_id=user.role_id,
        document_ids=[doc1.id, doc2.id, doc3.id],
        directory_ids=[d1.id, d2.id],
        archive_name='test_archive_name',
        archive_config=ArchiveConfig(),
    )

    assert archive_id is not None

    archive_content = s3_emulation.files[get_documents_archive_s3_key(archive_id=archive_id)].body
    assert len(archive_content) > 0

    result = await unzip_archive_test_util(body=archive_content)
    assert result == expected_result

import logging

from aiohttp import web

from api.downloads.db import (
    mark_download_documents_archives_as_sent,
)
from api.downloads.enums import ArchiveFilenamesMode
from api.downloads.types import ArchiveConfig
from app.lib.locks import lock_cleaner_on_fail
from app.lib.types import DataDict
from app.services import services
from worker import topics
from worker.downloading.utils import (
    create_archive,
    generate_archive,
    send_documents_archive_email,
)


async def multi_download_archive(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Generate archives of selected documents, upload them to S3 and send email with
    links to uploaded archives. Such as archive can contains preview of XML,
    generating them is very slow process, this job is work in iterative mode,
    via `offset` variable.

    :param data: dict:
        - 'offset': int - offset, that represent processed documents
        - 'archives_ids': List[str] - list of archives already uploaded to S3
        - 'lock_key': str - lock key for reducing server load, only one request to
            create documents archive can be processed at the same time.
        - 'documents_ids': List[str] - list of documents ids for archivation
        - 'role_id': str - role id of user that request this process, used for
            contacts person after archivation.
        - 'archive_name': str - name for generated archives
        - 'request_data': Dict[str, Any]: - any other data related to emailing role

        # for description look to `ArchiveConfig` implementation
        - 'in_one_folder'
        - 'with_instruction'
        - 'with_signatures'
        - 'with_xml_preview'
        - 'with_xml_original'
    """

    logger.info('Start docs archive generation', extra={'data': str(data)})

    files_limit = services.config.consts.max_files

    # we trust that this value will be always in data
    lock_key: str = data['lock_key']

    # remove lock key on job was failed
    async with lock_cleaner_on_fail(services.redis, lock_key):
        # for preventing timeouts, job run in iterative mode, process first N items,
        # increase offset to N, send data over broker again to the same job
        offset = data.get('offset', 0)

        # list of uploaded to S3 archives
        archives_ids: list[str] = data.get('archives_ids', [])

        documents_ids: list[str] = data['documents_ids']
        role_id: str = data['role_id']
        archive_name: str = data['archive_name']
        request_data: DataDict = data['request_data']

        archive_config = ArchiveConfig.from_dict(data)
        file_group = documents_ids[offset : offset + files_limit]

        # If file_group is empty, it means that all documents is processed,
        # and we must send email and return.
        if not file_group:
            await send_documents_archive_email(
                role_id=role_id,
                archive_name=archive_name,
                request_data=request_data,
                archives_ids=archives_ids,
            )
            async with services.db.acquire() as conn:
                await mark_download_documents_archives_as_sent(conn, archives_ids=archives_ids)

            logger.info(
                'Generating document archive was done successfully',
                # extra=data,
            )
            # and don't forget about removing lock key
            await services.redis.delete(lock_key)
            return

        # Generate new archive_ids
        archive_ctx = await create_archive(
            role_id=role_id,
            file_group=file_group,
            archive_name=archive_name,
            archive_config=archive_config,
        )
        if archive_ctx:
            archives_ids.append(archive_ctx.archive_id)
            files_limit = archive_ctx.files_count
        else:
            logger.warning(
                'Archive for file group was not created',
                extra={
                    'role_id': role_id,
                    'file_group': file_group,
                },
            )

        # Continue process data by increasing offset variable
        new_data = {
            **data,
            'offset': offset + files_limit,
            'archives_ids': archives_ids,
        }
        await services.kafka.send_record(topic=topics.MULTI_DOWNLOAD, value=new_data)
        logger.info('Send next message to archive generation job', extra=new_data)


async def prepare_archived_documents_download(
    _: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Generate archive of selected documents with directories tree structure as in Company Archive,
    upload it to S3 and send email with link to uploaded archive.

    Current realisation pack in archive only original documents and signatures that present on S3.
    Process of creating archive is done in stream mode, so we don't keep in memory all
    processed files and final archive.

    :param data: dict:
        - 'lock_key': str - lock key for reducing server load, only one request to
            create archive can be processed at the same time.
        - 'document_ids': List[str] - list of documents ids for archive
        - 'directory_ids': List[int] - list of directories ids for creating tree in archive
        - 'role_id': str - role id of user that request this process, used for
            contacts person after creating archive.
        - 'archive_name': str - name for generated archives

        # for description look to `ArchiveConfig` implementation
        - filenames_mode (currently supports only that param from ArchiveConfig)
    """
    lock_key: str = data['lock_key']
    document_ids: list[str] = data['document_ids']
    directory_ids: list[int] = data['directory_ids']
    role_id: str = data['role_id']
    archive_name: str = data['archive_name']

    # Currently we support only filenames_mode param from data input,
    # other parameters are preset
    archive_config_data = {
        'filenames_mode': data.get('filenames_mode', ArchiveFilenamesMode.default.value),
        'in_one_folder': False,
        'with_signatures': True,
        'with_xml_preview': False,
        'with_instruction': False,
        'with_xml_original': True,
        'signature_format': None,
    }
    archive_config = ArchiveConfig.from_dict(archive_config_data)

    # remove lock key on job was failed
    async with lock_cleaner_on_fail(services.redis, lock_key):
        archive_id = await generate_archive(
            role_id=role_id,
            document_ids=document_ids,
            directory_ids=directory_ids,
            archive_name=archive_name,
            archive_config=archive_config,
        )

        if archive_id is None:
            logger.info("Archive wasn't created")
            return

        await send_documents_archive_email(
            role_id=role_id,
            archive_name=archive_name,
            request_data={},
            archives_ids=[archive_id],
        )

        async with services.db.acquire() as conn:
            await mark_download_documents_archives_as_sent(conn, archives_ids=[archive_id])

    # Lock cleanup
    await services.redis.delete(lock_key)

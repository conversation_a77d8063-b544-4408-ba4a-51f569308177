import logging

import pydantic
from aiohttp import web
from botocore.exceptions import ClientError

from app.auth.db import select_user
from app.documents_ai import db, enums, utils
from app.documents_ai.db import update_extraction_status
from app.documents_ai.enums import (
    StructuredDataErrorKey,
)
from app.documents_ai.enums import (
    StructuredDataExtractionStatus as SDAStatus,
)
from app.documents_ai.errors import DocumentNoContentError
from app.documents_ai.utils import (
    extract_structured_data_with_bedrock,
    get_document_structured_data_s3_key,
    process_document_with_ocr,
)
from app.lib import s3_utils
from app.lib.types import DataDict
from app.services import services
from worker import topics
from worker.utils import retry_config

SCHEDULED_META_SUGGESTIONS_CHUNK_SIZE = 100  # 100 Requests per minute (1/5 from minutely quota)


@retry_config(max_attempts=5, delay_minutes=1)
async def process_structured_data_extraction_job(
    __: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Job payload expects:
      - document_id: str
      - version_id: str | None
      - company_id: str
    """
    document_id: str = data['document_id']
    version_id: str | None = data.get('version_id')
    company_id: str = data['company_id']

    try:
        if await s3_utils.exists(get_document_structured_data_s3_key(document_id)):
            logger.info(
                'Structured data found on S3, skipping extraction',
                extra={'document_id': document_id},
            )
            async with services.db.acquire() as conn:
                await update_extraction_status(
                    conn,
                    document_id=document_id,
                    status=SDAStatus.AWAITING_VALIDATION,
                    company_id=company_id,
                )
            return

        # OCR with Textract
        ocr_data = await process_document_with_ocr(document_id=document_id, version_id=version_id)

        # Structured data extraction with Bedrock
        parsed_obj = await extract_structured_data_with_bedrock(
            ocr_data=ocr_data, document_id=document_id
        )

    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code')
        logger.warning(
            'AWS API call failed during structured data extraction job',
            extra={
                'document_id': document_id,
                'error_code': error_code,
            },
        )
        if error_code in {429, 500, 503, 504}:
            # raise Timeout error for trigger job retry
            raise TimeoutError from e
        raise

    except DocumentNoContentError:
        logger.warning(
            'Document has no content to extract structured data from',
            extra={'document_id': document_id},
        )
        async with services.db.acquire() as conn:
            await update_extraction_status(
                conn,
                document_id=document_id,
                company_id=company_id,
                status=SDAStatus.ERROR,
                error_message=StructuredDataErrorKey.NO_DOCUMENT_CONTENT,
            )
        raise

    except (pydantic.ValidationError, ValueError):
        logger.warning(
            'Structured data parsing/validation failed', extra={'document_id': document_id}
        )
        async with services.db.acquire() as conn:
            await update_extraction_status(
                conn,
                document_id=document_id,
                company_id=company_id,
                status=SDAStatus.ERROR,
                error_message=StructuredDataErrorKey.INVALID_STRUCTURED_DATA,
            )
        raise

    except Exception:
        logger.warning('Extraction job failed', extra={'document_id': document_id})
        async with services.db.acquire() as conn:
            await update_extraction_status(
                conn,
                company_id=company_id,
                document_id=document_id,
                status=SDAStatus.ERROR,
                error_message=StructuredDataErrorKey.UNKNOWN_ERROR,
            )
        raise

    # Successfully extracted structured_data - Mark AWAITING_VALIDATION
    async with services.db.acquire() as conn:
        await update_extraction_status(
            conn,
            document_id=document_id,
            status=SDAStatus.AWAITING_VALIDATION,
            company_id=company_id,
            meta_data=parsed_obj.meta,
        )


async def initiate_bulk_scheduled_document_meta_suggestions(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    async with services.db.acquire() as conn:
        scheduled_suggestions = await db.select_scheduled_documents_meta_suggestions(
            conn=conn,
            limit=SCHEDULED_META_SUGGESTIONS_CHUNK_SIZE,
            status=enums.DocumentMetaSuggestionStatus.scheduled,
        )
        if not scheduled_suggestions:
            logger.info('No scheduled document meta suggestions to process')
            return

        async with conn.begin():
            await db.update_scheduled_document_meta_suggestions(
                conn=conn,
                document_ids=[suggestion.document_id for suggestion in scheduled_suggestions],
                data={'status': enums.DocumentMetaSuggestionStatus.in_progress},
            )

            await services.kafka.send_records(
                topic=topics.PROCESS_SCHEDULED_DOCUMENT_META_SUGGESTION,
                values=[
                    {
                        'document_id': scheduled_suggestion.document_id,
                        'scheduled_by': scheduled_suggestion.scheduled_by,
                    }
                    for scheduled_suggestion in scheduled_suggestions
                ],
            )


async def process_scheduled_document_meta_suggestion(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    document_id: str = data['document_id']
    scheduled_by: str = data['scheduled_by']

    async with services.db_readonly.acquire() as conn:
        user = await select_user(conn=conn, role_id=scheduled_by)

        if not user:
            logger.info(
                'User not found for scheduled meta suggestion',
                extra={
                    'role_id': scheduled_by,
                    'document_id': document_id,
                },
            )
            await db.update_scheduled_document_meta_suggestion(
                conn=conn,
                document_id=document_id,
                data={'status': enums.DocumentMetaSuggestionStatus.failed},
            )
            return

    try:
        suggestion = await utils.suggest_document_meta_by_document_id(
            document_id=document_id,
            user=user,
            as_image=True,
            raise_on_fail=True,
        )
    except Exception as e:
        logger.info(
            'Failed to suggest meta for document',
            extra={
                'role_id': scheduled_by,
                'document_id': document_id,
                'error': str(e),
            },
        )
        async with services.db.acquire() as conn:
            await db.update_scheduled_document_meta_suggestion(
                conn=conn,
                document_id=document_id,
                data={'status': enums.DocumentMetaSuggestionStatus.failed},
            )
            return

    async with services.db.acquire() as conn:
        # AI model hasn't made any suggestions, or we did not send the request to
        # it due to small text length.
        if suggestion is None:
            await db.update_scheduled_document_meta_suggestion(
                conn=conn,
                document_id=document_id,
                data={'status': enums.DocumentMetaSuggestionStatus.finished},
            )
            return

        await db.update_scheduled_document_meta_suggestion(
            conn=conn,
            document_id=document_id,
            data={
                'suggestion_id': suggestion.id,
                'status': enums.DocumentMetaSuggestionStatus.finished,
            },
        )

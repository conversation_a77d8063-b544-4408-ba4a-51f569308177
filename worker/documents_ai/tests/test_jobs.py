import logging
from unittest import mock

import pytest
import ujson

from app.documents_ai import db, enums
from app.documents_ai.db import insert_or_replace_extractions, select_extractions_for_documents
from app.documents_ai.enums import StructuredDataExtractionStatus as SDAStatus
from app.documents_ai.types import DocumentDataExtraction, OCRDataRaw, OCRProcessedDataItem
from app.documents_ai.utils import (
    _prepare_structured_data_output,
    get_document_structured_data_s3_key,
)
from app.services import services
from app.tests import common
from app.tests.common import prepare_client, prepare_document_data
from worker.documents_ai import jobs
from worker.documents_ai.jobs import process_structured_data_extraction_job
from worker.documents_ai.tests.data import TEST_DOCUMENT_BYTES

logger = logging.getLogger(__name__)


def _mock_aws_clients(
    monkeypatch,
    llm_resp_text: str,
    textract_resp=None,
):
    async def mock_download_document_bytes(
        *, document_id: str, version_id: str | None = None
    ) -> bytes | None:
        return TEST_DOCUMENT_BYTES

    async def mock_get_raw_ocr_data(document_content: bytes) -> OCRDataRaw:
        if textract_resp:
            return OCRDataRaw.from_dict(
                {
                    'pages_info': [{'number': 0, 'width': 1000, 'height': 1000}],
                    'ocr_results': [textract_resp],
                }
            )
        return OCRDataRaw.from_dict(
            {
                'pages_info': [{'number': 0, 'width': 1000, 'height': 1000}],
                'ocr_results': [
                    {
                        'Blocks': [
                            {
                                'BlockType': 'LINE',
                                'Id': 'test-id',
                                'Text': 'hello',
                                'Geometry': {
                                    'BoundingBox': {
                                        'Left': 0.1,
                                        'Top': 0.2,
                                        'Width': 0.3,
                                        'Height': 0.1,
                                    }
                                },
                            }
                        ]
                    }
                ],
            }
        )

    class MockAsyncBody:
        async def __aenter__(self):
            return self

        async def __aexit__(self, exc_type, exc, tb):
            return False

        async def read(self) -> bytes:
            return ujson.dumps(
                {
                    'content': [{'text': llm_resp_text}],
                    'usage': {'input_tokens': 1, 'output_tokens': 1},
                }
            ).encode()

    async def mock_bedrock_invoke(**kwargs):
        return {'body': MockAsyncBody()}

    monkeypatch.setattr(
        'app.documents_ai.utils.download_document_bytes', mock_download_document_bytes
    )
    monkeypatch.setattr('app.documents_ai.utils.get_raw_ocr_data', mock_get_raw_ocr_data)
    monkeypatch.setattr('app.services.services.bedrock_client.invoke_model', mock_bedrock_invoke)


async def test_extraction_job_invalid_structured_data(aiohttp_client, monkeypatch):
    """Test extraction job with invalid structured data that should fail validation."""
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    _mock_aws_clients(monkeypatch, llm_resp_text=ujson.dumps({'extracted_data': 'not_a_dict'}))

    extraction_data = DocumentDataExtraction(
        document_id=document.id,
        company_id=user.company_id,
        status=SDAStatus.PENDING,
        error_message=None,
    )
    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(
            conn, company_id=user.company_id, data=[extraction_data]
        )

    import pydantic

    with pytest.raises(pydantic.ValidationError):
        await process_structured_data_extraction_job(
            app,
            data={
                'document_id': document.id,
                'company_id': user.company_id,
            },
            logger=mock.Mock(),
        )

    async with services.db.acquire() as conn:
        rows = await select_extractions_for_documents(
            conn, company_id=user.company_id, document_ids=[document.id]
        )
        assert len(rows) == 1
        assert rows[0].status == SDAStatus.ERROR


async def test_extraction_job_execution(aiohttp_client, monkeypatch):
    """Test successful extraction job execution."""
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='test')

    _mock_aws_clients(
        monkeypatch,
        llm_resp_text=ujson.dumps({'extracted_data': {'details': {'number': {'value': '123'}}}}),
    )

    extraction_data = DocumentDataExtraction(
        document_id=document.id,
        company_id=user.company_id,
        status=SDAStatus.PENDING,
        error_message=None,
    )
    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(
            conn, company_id=user.company_id, data=[extraction_data]
        )

    await process_structured_data_extraction_job(
        app=app,
        data={
            'document_id': document.id,
            'company_id': user.company_id,
        },
        logger=mock.Mock(),
    )

    async with services.db.acquire() as conn:
        rows = await select_extractions_for_documents(
            conn, company_id=user.company_id, document_ids=[document.id]
        )
        assert len(rows) == 1
        assert rows[0].status == SDAStatus.AWAITING_VALIDATION
        assert rows[0].llm_name is not None


async def test_bboxes_from_indexes(aiohttp_client, monkeypatch):
    """Test that bounding boxes are correctly enriched from OCR indexes."""
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='bbox')

    _mock_aws_clients(
        monkeypatch,
        llm_resp_text=ujson.dumps(
            {
                'extracted_data': {
                    'details': {
                        'number': {'value': 'A-1', 'indexes': [0]},
                        'date_created': {'value': '2024-05-05', 'indexes': [1]},
                    }
                }
            }
        ),
        textract_resp={
            'Blocks': [
                {
                    'BlockType': 'LINE',
                    'Id': 'first-id',
                    'Text': 'first',
                    'Page': 1,
                    'Geometry': {
                        'BoundingBox': {'Left': 0.1, 'Top': 0.2, 'Width': 0.3, 'Height': 0.1}
                    },
                },
                {
                    'BlockType': 'LINE',
                    'Id': 'second-id',
                    'Text': 'second',
                    'Page': 2,
                    'Geometry': {
                        'BoundingBox': {'Left': 0.0, 'Top': 0.0, 'Width': 0.1, 'Height': 0.2}
                    },
                },
            ]
        },
    )

    extraction_data = DocumentDataExtraction(
        document_id=document.id,
        company_id=user.company_id,
        status=SDAStatus.PENDING,
        error_message=None,
    )
    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(
            conn, company_id=user.company_id, data=[extraction_data]
        )

    await process_structured_data_extraction_job(
        app,
        data={
            'document_id': document.id,
            'company_id': user.company_id,
        },
        logger=mock.Mock(),
    )

    async with services.db.acquire() as conn:
        rows = await select_extractions_for_documents(
            conn, company_id=user.company_id, document_ids=[document.id]
        )
        assert len(rows) == 1
        assert rows[0].status == SDAStatus.AWAITING_VALIDATION

    ocr_data = [
        OCRProcessedDataItem(text='first', page=0, bbox=[100, 200, 400, 300]),
        OCRProcessedDataItem(text='second', page=1, bbox=[0, 0, 100, 200]),
    ]

    test_data = {
        'details': {
            'number': {'value': 'A-1', 'indexes': [0]},
            'date_created': {'value': '2024-05-05', 'indexes': [1]},
        }
    }

    enriched = _prepare_structured_data_output(test_data, ocr_data)
    assert 'bboxes' in enriched['details']['number']
    assert enriched['details']['number']['bboxes'][0]['page'] == 0
    assert enriched['details']['date_created']['bboxes'][0]['page'] == 1


async def test_extraction_job_with_tables(aiohttp_client, monkeypatch, s3_emulation):
    """Test successful extraction job execution with a document containing tables."""
    app, _client, user = await prepare_client(aiohttp_client)
    document = await prepare_document_data(app, owner=user, title='table_doc')

    # 1. Mock a Textract response that includes a table
    mock_textract_table_resp = {
        'Blocks': [
            # Some non-table text
            {
                'BlockType': 'LINE',
                'Id': 'line1',
                'Text': 'Invoice #123',
                'Page': 1,
                'Geometry': {'BoundingBox': {'Left': 0.1, 'Top': 0.1, 'Width': 0.2, 'Height': 0.1}},
            },
            # Table structure
            {
                'BlockType': 'TABLE',
                'Id': 'table1',
                'Page': 1,
                'Geometry': {'BoundingBox': {'Left': 0.1, 'Top': 0.2, 'Width': 0.8, 'Height': 0.5}},
                'Relationships': [{'Type': 'CHILD', 'Ids': ['cell1', 'cell2', 'cell3', 'cell4']}],
            },
            # Cells
            {
                'BlockType': 'CELL',
                'Id': 'cell1',
                'RowIndex': 1,
                'ColumnIndex': 1,
                'Page': 1,
                'Relationships': [{'Type': 'CHILD', 'Ids': ['word1']}],
                'Geometry': {'BoundingBox': {'Left': 0.1, 'Top': 0.2, 'Width': 0.4, 'Height': 0.1}},
            },
            {
                'BlockType': 'CELL',
                'Id': 'cell2',
                'RowIndex': 1,
                'ColumnIndex': 2,
                'Page': 1,
                'Relationships': [{'Type': 'CHILD', 'Ids': ['word2']}],
                'Geometry': {'BoundingBox': {'Left': 0.5, 'Top': 0.2, 'Width': 0.4, 'Height': 0.1}},
            },
            {
                'BlockType': 'CELL',
                'Id': 'cell3',
                'RowIndex': 2,
                'ColumnIndex': 1,
                'Page': 1,
                'Relationships': [{'Type': 'CHILD', 'Ids': ['word3']}],
                'Geometry': {'BoundingBox': {'Left': 0.1, 'Top': 0.3, 'Width': 0.4, 'Height': 0.1}},
            },
            {
                'BlockType': 'CELL',
                'Id': 'cell4',
                'RowIndex': 2,
                'ColumnIndex': 2,
                'Page': 1,
                'Relationships': [{'Type': 'CHILD', 'Ids': ['word4']}],
                'Geometry': {'BoundingBox': {'Left': 0.5, 'Top': 0.3, 'Width': 0.4, 'Height': 0.1}},
            },
            # Words inside cells
            {
                'BlockType': 'WORD',
                'Id': 'word1',
                'Text': 'Product',
                'Page': 1,
                'Geometry': {'BoundingBox': {'Left': 0.1, 'Top': 0.2, 'Width': 0.4, 'Height': 0.1}},
            },
            {
                'BlockType': 'WORD',
                'Id': 'word2',
                'Text': 'Price',
                'Page': 1,
                'Geometry': {'BoundingBox': {'Left': 0.5, 'Top': 0.2, 'Width': 0.4, 'Height': 0.1}},
            },
            {
                'BlockType': 'WORD',
                'Id': 'word3',
                'Text': 'Laptop',
                'Page': 1,
                'Geometry': {'BoundingBox': {'Left': 0.1, 'Top': 0.3, 'Width': 0.4, 'Height': 0.1}},
            },
            {
                'BlockType': 'WORD',
                'Id': 'word4',
                'Text': '1500',
                'Page': 1,
                'Geometry': {'BoundingBox': {'Left': 0.5, 'Top': 0.3, 'Width': 0.4, 'Height': 0.1}},
            },
        ]
    }

    # 2. Mock an LLM response that analyzes the table
    mock_llm_table_resp_text = ujson.dumps(
        {
            'extracted_data': {'details': {'number': {'value': '123', 'indexes': [0]}}},
            'table_analysis': [
                {
                    'table_id': 0,
                    'is_items_table': True,
                    'column_mapping': {'0': 'name', '1': 'price'},
                    'non_data_rows': [],
                }
            ],
        }
    )

    _mock_aws_clients(
        monkeypatch,
        llm_resp_text=mock_llm_table_resp_text,
        textract_resp=mock_textract_table_resp,
    )

    extraction_data = DocumentDataExtraction(
        document_id=document.id,
        company_id=user.company_id,
        status=SDAStatus.PENDING,
        error_message=None,
    )
    async with services.db.acquire() as conn:
        await insert_or_replace_extractions(
            conn, company_id=user.company_id, data=[extraction_data]
        )

    # 3. Run the job
    await process_structured_data_extraction_job(
        app=app,
        data={'document_id': document.id, 'company_id': user.company_id},
        logger=mock.Mock(),
    )

    # 4. Assertions
    async with services.db.acquire() as conn:
        rows = await select_extractions_for_documents(
            conn, company_id=user.company_id, document_ids=[document.id]
        )
        assert len(rows) == 1
        assert rows[0].status == SDAStatus.AWAITING_VALIDATION

    saved_file = s3_emulation.files.get(get_document_structured_data_s3_key(document.id))
    assert saved_file is not None

    saved_data = ujson.loads(saved_file.body.decode())

    # Verify the non-table data
    assert saved_data['extracted_data']['details']['number']['value'] == '123'

    # Verify the table data
    items = saved_data['extracted_data']['items']
    assert len(items) == 1
    assert items[0]['name']['value'] == 'Laptop'
    assert items[0]['price']['value'] == '1500'
    assert 'bboxes' in items[0]['name']


async def test_process_bulk_scheduled_document_meta_suggestions(aiohttp_client, monkeypatch):
    app, client, user = await common.prepare_client(aiohttp_client)

    document_1 = await common.prepare_document_data(app=app, owner=user)
    document_2 = await common.prepare_document_data(app=app, owner=user)
    document_3 = await common.prepare_document_data(app=app, owner=user)

    common.patch_bedrock_client(monkeypatch)

    async with services.db.acquire() as conn:
        for document in (document_1, document_2, document_3):
            await db.insert_scheduled_document_meta_suggestion(
                conn=conn,
                data={
                    'document_id': document.id,
                    'scheduled_by': user.role_id,
                    'status': enums.DocumentMetaSuggestionStatus.scheduled,
                },
            )

    await jobs.initiate_bulk_scheduled_document_meta_suggestions(app, {}, logger)

    async with services.db.acquire() as conn:
        scheduled_suggestions = await db.select_scheduled_documents_meta_suggestions(conn)
        document_1_suggestions = await db.select_document_suggestions(
            conn=conn,
            document_id=document_1.id,
        )
        document_2_suggestions = await db.select_document_suggestions(
            conn=conn,
            document_id=document_2.id,
        )
        document_3_suggestions = await db.select_document_suggestions(
            conn=conn,
            document_id=document_3.id,
        )

    assert len(document_1_suggestions) == 1
    assert len(document_2_suggestions) == 1
    assert len(document_3_suggestions) == 1

    for suggestion in scheduled_suggestions:
        assert suggestion.document_id in (document_1.id, document_2.id, document_3.id)
        assert suggestion.status == enums.DocumentMetaSuggestionStatus.finished
        assert suggestion.scheduled_by == user.role_id
        assert suggestion.suggestion_id in (
            document_1_suggestions[0].id,
            document_2_suggestions[0].id,
            document_3_suggestions[0].id,
        )

import logging
from typing import assert_never

from app.auth.db import select_base_user
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib import edi, kasa, ttn
from app.lib.database import DBConnection
from app.lib.edi import EDIPayloadChangeUserData
from app.signatures import kep
from app.vchasno_profile.enums import VchasnoProfileSyncEntityType
from app.vchasno_profile.types import VchasnoUser
from worker.vchasno_profile.types import (
    ProjectEnum,
    VchasnoProfileSyncCtx,
    VchasnoProfileSyncEvent,
    VchasnoProfileSyncUserEvent,
)

logger = logging.getLogger(__name__)


async def _send_vchasno_profile_sync_kep(event: VchasnoProfileSyncEvent) -> None:
    """Send vchasno profile sync event to KEP"""
    if not get_flag(FeatureFlags.ENABLE_VCHASNO_PROFILE_SYNC_KEP):
        return

    data = event.to_api()
    await kep.client.sync_vchasno_profile(data)
    logger.info('Vchasno profile sync event sent to KEP')


async def _send_vchasno_profile_sync_kasa(event: VchasnoProfileSyncEvent) -> None:
    """Send vchasno profile sync event to KASA"""
    if not get_flag(FeatureFlags.ENABLE_VCHASNO_PROFILE_SYNC_KASA):
        return

    data = event.to_api()
    await kasa.client.sync_vchasno_profile(data)
    logger.info('Vchasno profile sync event sent to KASA')


async def _send_vchasno_profile_sync_ttn(event: VchasnoProfileSyncEvent) -> None:
    """Send vchasno profile sync event to TTN"""
    if not get_flag(FeatureFlags.ENABLE_VCHASNO_PROFILE_SYNC_TTN):
        return

    data = event.to_api()
    await ttn.client.sync_vchasno_profile(data)
    logger.info('Vchasno profile sync event sent to TTN')


async def _send_vchasno_profile_sync_edi(event: VchasnoProfileSyncEvent) -> None:
    """Send vchasno profile sync event to EDI"""
    if not get_flag(FeatureFlags.ENABLE_VCHASNO_PROFILE_SYNC_EDI):
        return

    client = edi.Client()
    await client.request(
        EDIPayloadChangeUserData(
            entity_id=event.entity_id,
            entity_type=event.entity_type,
            user=event.user,
        )
    )
    logger.info('Vchasno profile sync event sent to EDI')


async def prepare_vchasno_profile_sync_event(
    conn: DBConnection,
    ctx: VchasnoProfileSyncCtx,
) -> VchasnoProfileSyncEvent | None:
    """
    Prepare VchasnoProfileSyncEvent for the given entity ID and entity type
    """
    if ctx.entity_type == VchasnoProfileSyncEntityType.user:
        user = await select_base_user(conn, user_id=ctx.entity_id)
        if not user:
            logger.warning('User not found', extra={'user_id': ctx.entity_id})
            return None

        vchasno_user = VchasnoUser.from_user(user)
        return VchasnoProfileSyncUserEvent(
            entity_id=ctx.entity_id,
            entity_type=VchasnoProfileSyncEntityType.user,
            user=vchasno_user,
        )

    assert_never(ctx.entity_type)


async def send_vchasno_profile_sync(
    project: ProjectEnum,
    event: VchasnoProfileSyncEvent,
) -> None:
    if project == ProjectEnum.kep:
        await _send_vchasno_profile_sync_kep(event)
    elif project == ProjectEnum.kasa:
        await _send_vchasno_profile_sync_kasa(event)
    elif project == ProjectEnum.ttn:
        await _send_vchasno_profile_sync_ttn(event)
    elif project == ProjectEnum.edi:
        await _send_vchasno_profile_sync_edi(event)
    else:
        assert_never(project)

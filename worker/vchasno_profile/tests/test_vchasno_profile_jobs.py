from collections import Counter
from unittest.mock import AsyncMock

from app.flags import FeatureFlags
from app.lib import kasa, ttn
from app.services import services
from app.signatures import kep
from app.signatures.kep.enums import KEPUrl
from app.tests.common import prepare_client, prepare_user_data
from app.vchasno_profile.db import select_vchasno_profile_sync_batch
from app.vchasno_profile.utils import create_vchasno_profile_sync
from worker import topics
from worker.vchasno_profile.jobs import start_vchasno_profile_sync

TEST_EMAIL_1 = '<EMAIL>'


async def test_start_vchasno_profile_sync_with_batch(aiohttp_client, monkeypatch):
    """
    Test that start_vchasno_profile_sync correctly sends the batch to the Kafka topic
    """

    app, _, _ = await prepare_client(aiohttp_client)

    data = {'cursor': 0}
    logger = AsyncMock()

    async with services.db.acquire() as conn:
        for i in range(5):
            user = await prepare_user_data(
                app=app,
                email=f'u{i}@vchasno.ua',
                phone=None,
                first_name='FirstTest',
                second_name='SecondTest',
                last_name='LastTest',
            )
            await create_vchasno_profile_sync(conn, user_id=user.id)

    await start_vchasno_profile_sync(app, data, logger)
    kafka_messages = services.kafka.messages
    topics_counter = Counter([msg[0] for msg in kafka_messages])

    assert topics_counter[topics.SEND_VCHASNO_PROFILE_SYNC] == 20  # users_count * 4 projects
    assert topics_counter[topics.START_VCHASNO_PROFILE_SYNC] == 1

    # Check that items were removed from the sync queue after scheduling
    async with services.db.acquire() as conn:
        sync_items = await select_vchasno_profile_sync_batch(
            conn=conn,
            cursor=0,
            limit=100,
        )
        assert not sync_items


async def test_send_vchasno_profile_sync_kep(aiohttp_client, test_flags, monkeypatch):
    app, _, _ = await prepare_client(aiohttp_client)
    test_flags[FeatureFlags.ENABLE_VCHASNO_PROFILE_SYNC_KEP.value] = True

    data = {'cursor': 0}
    logger = AsyncMock()

    user = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_1,
        phone=None,
        first_name='FirstTest',
        second_name='SecondTEst',
        last_name='LastTEst',
    )

    async with services.db.acquire() as conn:
        await create_vchasno_profile_sync(conn, user_id=user.id)

        sync_items = await select_vchasno_profile_sync_batch(
            conn=conn,
            cursor=0,
            limit=100,
        )
        assert len(sync_items) == 1
        sync_item = sync_items[0]

    request_mock = AsyncMock(return_value=None)
    monkeypatch.setattr(kep.client, 'vchasno_kep_integration_request', request_mock)

    await start_vchasno_profile_sync(app, data, logger)

    assert sync_item.entity_id == user.id
    request_mock.assert_called_once_with(
        path=KEPUrl.vchasno_profile_sync,
        data={
            'entity_id': sync_item.entity_id,
            'entity_type': 'user',
            'user': {
                'id': user.id,
                'date_created': user.date_created.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
                'date_updated': user.date_updated.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
                'date_deleted': None,
                'email': TEST_EMAIL_1,
                'phone': None,
                'contact_phone': None,
                'auth_phone': None,
                'first_name': 'FirstTest',
                'last_name': 'LastTEst',
                'second_name': 'SecondTEst',
            },
        },
    )


async def test_send_vchasno_profile_sync_kasa(aiohttp_client, test_flags, monkeypatch):
    app, _, _ = await prepare_client(aiohttp_client)
    test_flags[FeatureFlags.ENABLE_VCHASNO_PROFILE_SYNC_KASA.value] = True

    data = {'cursor': 0}
    logger = AsyncMock()

    user = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_1,
        phone=None,
        first_name='FirstTest',
        second_name='SecondTEst',
        last_name='LastTEst',
    )

    async with services.db.acquire() as conn:
        await create_vchasno_profile_sync(conn, user_id=user.id)

        sync_items = await select_vchasno_profile_sync_batch(
            conn=conn,
            cursor=0,
            limit=100,
        )
        assert len(sync_items) == 1
        sync_item = sync_items[0]

    request_mock = AsyncMock(return_value=None)
    monkeypatch.setattr(kasa.client, 'sync_vchasno_profile', request_mock)

    await start_vchasno_profile_sync(app, data, logger)

    assert sync_item.entity_id == user.id
    request_mock.assert_called_once_with(
        {
            'entity_id': sync_item.entity_id,
            'entity_type': 'user',
            'user': {
                'id': user.id,
                'date_created': user.date_created.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
                'date_updated': user.date_updated.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
                'date_deleted': None,
                'email': TEST_EMAIL_1,
                'phone': None,
                'contact_phone': None,
                'auth_phone': None,
                'first_name': 'FirstTest',
                'last_name': 'LastTEst',
                'second_name': 'SecondTEst',
            },
        },
    )


async def test_send_vchasno_profile_sync_ttn(aiohttp_client, test_flags, monkeypatch):
    app, _, _ = await prepare_client(aiohttp_client)
    test_flags[FeatureFlags.ENABLE_VCHASNO_PROFILE_SYNC_TTN.value] = True

    data = {'cursor': 0}
    logger = AsyncMock()

    user = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_1,
        phone=None,
        first_name='FirstTest',
        second_name='SecondTEst',
        last_name='LastTEst',
    )

    async with services.db.acquire() as conn:
        await create_vchasno_profile_sync(conn, user_id=user.id)

        sync_items = await select_vchasno_profile_sync_batch(
            conn=conn,
            cursor=0,
            limit=100,
        )
        assert len(sync_items) == 1
        sync_item = sync_items[0]

    request_mock = AsyncMock(return_value=None)
    monkeypatch.setattr(ttn.client, 'sync_vchasno_profile', request_mock)

    await start_vchasno_profile_sync(app, data, logger)

    assert sync_item.entity_id == user.id
    request_mock.assert_called_once_with(
        {
            'entity_id': sync_item.entity_id,
            'entity_type': 'user',
            'user': {
                'id': user.id,
                'date_created': user.date_created.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
                'date_updated': user.date_updated.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
                'date_deleted': None,
                'email': TEST_EMAIL_1,
                'phone': None,
                'contact_phone': None,
                'auth_phone': None,
                'first_name': 'FirstTest',
                'last_name': 'LastTEst',
                'second_name': 'SecondTEst',
            },
        },
    )


async def test_send_vchasno_profile_sync_edi(
    aiohttp_client,
    test_flags,
    monkeypatch,
    edi_request_mock,
):
    app, _, _ = await prepare_client(aiohttp_client)
    test_flags[FeatureFlags.ENABLE_VCHASNO_PROFILE_SYNC_EDI.value] = True

    data = {'cursor': 0}
    logger = AsyncMock()

    user = await prepare_user_data(
        app=app,
        email=TEST_EMAIL_1,
        phone=None,
        first_name='FirstTest',
        second_name='SecondTEst',
        last_name='LastTEst',
    )

    async with services.db.acquire() as conn:
        await create_vchasno_profile_sync(conn, user_id=user.id)

        sync_items = await select_vchasno_profile_sync_batch(
            conn=conn,
            cursor=0,
            limit=100,
        )
        assert len(sync_items) == 1
        sync_item = sync_items[0]

    await start_vchasno_profile_sync(app, data, logger)

    assert sync_item.entity_id == user.id

    edi_request_mock.mock.assert_called_once_with(
        method='change_user_data',
        entity_id=sync_item.entity_id,
        entity_type='user',
        user={
            'id': user.id,
            'date_created': user.date_created.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
            'date_updated': user.date_updated.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
            'date_deleted': None,
            'email': TEST_EMAIL_1,
            'phone': None,
            'contact_phone': None,
            'auth_phone': None,
            'first_name': 'FirstTest',
            'last_name': 'LastTEst',
            'second_name': 'SecondTEst',
        },
    )

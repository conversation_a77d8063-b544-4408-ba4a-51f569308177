import logging
from datetime import timed<PERSON>ta

from aiohttp import web

from api.errors import TemporaryUnavailableError
from app.lib.locks import redis_lock
from app.lib.types import DataDict
from app.services import services
from app.vchasno_profile.db import (
    delete_vchasno_profile_sync_batch,
    select_vchasno_profile_sync_batch,
)
from worker import topics
from worker.types import RETRY_EXCEPTIONS
from worker.utils import retry_config
from worker.vchasno_profile import utils
from worker.vchasno_profile.types import (
    ProjectEnum,
    VchasnoProfileSyncCtx,
)

VCHASNO_PROFILE_SYNC_ITEMS_LIMIT = 100


@retry_config(max_attempts=10)
async def start_vchasno_profile_sync(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Start the sync process by reading the items from the database queue and sending them to the
    project-specific job that makes requests to the project's API.
    """

    cursor: int = data.get('cursor', 0)
    async with services.db.acquire() as conn:
        async with conn.begin():
            batch_items = await select_vchasno_profile_sync_batch(
                conn=conn,
                cursor=cursor,
                limit=VCHASNO_PROFILE_SYNC_ITEMS_LIMIT,
            )
            if not batch_items:
                return

            # Remove duplicates from the batch
            unique_items = {(item.entity_id, item.entity_type): item for item in batch_items}
            items = list(unique_items.values())

            await services.kafka.send_records(
                topic=topics.SEND_VCHASNO_PROFILE_SYNC,
                values=[
                    VchasnoProfileSyncCtx(
                        project=project,
                        entity_id=item.entity_id,
                        entity_type=item.entity_type,
                    ).model_dump(mode='json')
                    for item in items
                    for project in ProjectEnum
                ],
            )

            await delete_vchasno_profile_sync_batch(
                conn=conn,
                sync_ids=[item.id for item in batch_items],
            )

            # Retry this job to check if there are more items
            await services.kafka.send_record(
                topic=topics.START_VCHASNO_PROFILE_SYNC,
                value={'cursor': batch_items[-1].id},
            )


@retry_config(
    max_attempts=200,
    delay_minutes=5,
    max_age=timedelta(days=2),
    exceptions=(*RETRY_EXCEPTIONS, TemporaryUnavailableError),
)
async def send_vchasno_profile_sync(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Prepare and send VchasnoProfileSyncEvent to the given project API
    """
    ctx = VchasnoProfileSyncCtx.model_validate(data)

    # Lock the sync process for the given entity ID and project to avoid sending
    # stale data to the project API. Raises TemporaryUnavailableError on lock timeout.
    async with redis_lock(
        name=f'vchasno_profile_sync_{ctx.entity_id}_{ctx.project.name}',
        blocking_timeout=10,
    ):
        async with services.db.acquire() as conn:
            event = await utils.prepare_vchasno_profile_sync_event(conn=conn, ctx=ctx)

        if not event:
            return

        await utils.send_vchasno_profile_sync(project=ctx.project, event=event)

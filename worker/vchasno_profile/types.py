import enum
from typing import Literal

import pydantic

from app.lib.types import DataDict
from app.vchasno_profile.enums import VchasnoProfileSyncEntityType
from app.vchasno_profile.types import VchasnoUser


class ProjectEnum(enum.Enum):
    kep = 'kep'
    kasa = 'kasa'
    ttn = 'ttn'
    edi = 'edi'


class VchasnoProfileSyncCtx(pydantic.BaseModel):
    entity_id: str
    entity_type: VchasnoProfileSyncEntityType
    project: ProjectEnum


class VchasnoProfileSyncUserEvent(pydantic.BaseModel):
    entity_id: str
    entity_type: Literal[VchasnoProfileSyncEntityType.user]
    user: Vcha<PERSON><PERSON>U<PERSON>

    def to_api(self) -> DataDict:
        return self.model_dump(mode='json')


# If you're adding new event types, you should add them here as tagged unions
type VchasnoProfileSyncEvent = VchasnoProfileSyncUserEvent

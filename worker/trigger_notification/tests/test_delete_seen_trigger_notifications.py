import datetime
import logging
import random
from uuid import uuid4

from app.lib.datetime_utils import local_now
from app.models import select_all
from app.tests.common import (
    prepare_client,
    prepare_local_datetime,
    prepare_trigger_notification,
    prepare_user_data,
)
from app.trigger_notifications.enums import (
    TriggerNotificationStatus,
    TriggerNotificationType,
)
from app.trigger_notifications.tables import trigger_notification_table
from worker.trigger_notification.jobs import delete_old_trigger_notification

logger = logging.getLogger(__name__)


async def test_delete_old_trigger_notification(aiohttp_client):
    """
    Test deleting notifications that are:
    - seen and between 30 and 90 days old
    - older than 90 days
    """
    app, client, user1 = await prepare_client(aiohttp_client)
    user2 = await prepare_user_data(app, email='<EMAIL>', company_edrpou='11223344')

    now = local_now()
    yesterday = now - datetime.timedelta(days=1)
    two_months_ago = now - datetime.timedelta(days=60)

    random.seed(now.day)
    roles = [user2.role_id, user1.role_id]

    notification_mapping = {
        'NEW_YESTERDAY': {
            'id': str(uuid4()),
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.new,
            'role_id': random.choice(roles),
            'display_date': yesterday,
        },
        'NEW_TWO_MONTHS_AGO': {
            'id': str(uuid4()),
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.new,
            'role_id': random.choice(roles),
            'display_date': two_months_ago,
        },
        'NEW_OLD': {
            'id': str(uuid4()),
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.new,
            'role_id': random.choice(roles),
            'display_date': prepare_local_datetime(year=2019, month=1, day=1),
        },
        'SEEN_OLD_1': {
            'id': str(uuid4()),
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.seen,
            'role_id': random.choice(roles),
            'display_date': prepare_local_datetime(year=2019, month=1, day=1),
        },
        'SEEN_OLD': {
            'id': str(uuid4()),
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.seen,
            'role_id': random.choice(roles),
            'display_date': prepare_local_datetime(year=2019, month=3, day=1),
        },
        'SEEN_YESTERDAY': {
            'id': str(uuid4()),
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.seen,
            'role_id': random.choice(roles),
            'display_date': yesterday,
        },
        'SEEN_TWO_MONTHS_AGO': {
            'id': str(uuid4()),
            'type': TriggerNotificationType.how_to,
            'status': TriggerNotificationStatus.seen,
            'role_id': random.choice(roles),
            'display_date': two_months_ago,
        },
    }

    await prepare_trigger_notification(app, list(notification_mapping.values()))

    await delete_old_trigger_notification(app, {}, logger)

    async with app['db'].acquire() as conn:
        query = trigger_notification_table.select().order_by(
            trigger_notification_table.c.display_date.desc(),
            trigger_notification_table.c.status,
        )
        notifications = await select_all(conn, query)

    expected_keys = ['NEW_YESTERDAY', 'SEEN_YESTERDAY', 'NEW_TWO_MONTHS_AGO']
    assert len(expected_keys) == len(notifications)
    for key, result in zip(expected_keys, notifications):
        expected = notification_mapping[key]
        assert expected['id'] == result['id']
        assert expected['display_date'] == result['display_date']
        assert expected['type'] == result['type']
        assert expected['status'] == result['status']

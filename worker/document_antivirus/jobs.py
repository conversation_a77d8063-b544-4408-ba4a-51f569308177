import logging

from aiohttp import web
from botocore.exceptions import ClientError

from api.downloads.utils import get_document_download_options
from app.auth.db import select_company
from app.config import get_level
from app.document_antivirus import utils
from app.document_antivirus.db import select_draft_antivirus_check
from app.document_antivirus.enums import AntivirusProvider
from app.document_antivirus.utils import (
    set_status_draft_antivirus_check,
    update_antivirus_check,
)
from app.documents.db import select_document_by_id
from app.drafts.db import select_draft
from app.drafts.utils import get_draft_s3_key
from app.lib import s3_utils
from app.lib.datetime_utils import optional_parse_raw_iso_datetime, utc_now
from app.lib.enums import AppLevel
from app.lib.s3_utils import DownloadFile
from app.lib.types import DataDict
from app.services import services
from worker.types import RETRY_EXCEPTIONS
from worker.utils import retry_config


class AntivirusCheckException(Exception): ...


@retry_config(max_attempts=5, exceptions=(AntivirusCheckException, *RETRY_EXCEPTIONS))
async def antivirus_check(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Calls antivirus check using https://gitlab.evo.dev/evodoc/antivirus
    is called when document is uploaded.
    :param data: dict:
    - document_id: str - check this document
    - version_id: Optional[str] - if version presents - check antivirus for it
    """

    document_id = data.get('document_id')
    version_id = data.get('version_id')

    if not document_id:
        logger.info('No document_id in data', extra=data)
        return

    async with services.db.acquire() as conn:
        document = await select_document_by_id(conn=conn, document_id=document_id)
        if not document:
            logger.info('Document does not exist', extra={'document_id': document_id})
            return

        download_file = await get_document_download_options(
            conn=conn,
            document_id=document_id,
            version_id=version_id,
        )

    try:
        content, _ = await s3_utils.download(download_file)
    except ClientError as e:
        # Still possible situation when document is deleted
        if e.response['Error']['Code'] == 'NoSuchKey':
            logger.info(
                'Document does not exist',
                extra=data,
            )
            return
        raise e

    status = await utils.check_document(content)
    if status is None:
        logger.info(
            'An error occurred when checking file',
            extra=data,
        )
        if get_level() == AppLevel.local:
            return
        # try to make retry
        raise AntivirusCheckException

    logger.info(
        'Update antivirus check status',
        extra={
            'document_id': document_id,
            'version_id': version_id,
            'status': status,
        },
    )

    async with services.db.acquire() as conn:
        await update_antivirus_check(
            conn,
            document_id=document_id,
            document_version_id=version_id,
            status=status,
            provider=AntivirusProvider.eset,
        )


@retry_config(max_attempts=5, exceptions=(AntivirusCheckException, *RETRY_EXCEPTIONS))
async def antivirus_check_draft(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Sends draft to antivirus check.
    :param data: Dict:
    - draft_id: str - id of draft to check
    - scheduled: str - scheduled time, used to cancel check if another check is scheduled
    """

    draft_id = data.get('draft_id')
    scheduled_str = data.get('scheduled')
    scheduled = optional_parse_raw_iso_datetime(scheduled_str)
    if not scheduled:
        logger.warning('Invalid scheduled time', extra=data)
        scheduled = utc_now()

    if not draft_id:
        logger.info('No draft_id in data', extra=data)
        return

    async with services.db.acquire() as conn:
        check_draft = await select_draft_antivirus_check(
            conn=conn,
            draft_id=draft_id,
        )
        if not check_draft:
            logger.info(
                'Draft antivirus check does not exist',
                extra={'draft_id': draft_id},
            )
            return
        if check_draft.updated > scheduled:
            logger.info(
                'Another antivirus check is scheduled. Skip this',
                extra={'draft_id': draft_id},
            )
            return

        draft = await select_draft(conn=conn, id=draft_id)
        if not draft:
            logger.info('Draft does not exist', extra={'draft_id': draft_id})
            return

        company = await select_company(
            conn=conn,
            company_id=draft.company_id,
        )
        if not company:
            logger.info('Company does not exist', extra={'company_id': draft.company_id})
            return

    file_item = DownloadFile(key=get_draft_s3_key(draft.id))

    try:
        content, _ = await s3_utils.download(file_item)
    except ClientError as e:
        # Still possible situation when draft is deleted
        if e.response['Error']['Code'] == 'NoSuchKey':
            logger.info(
                'Draft does not exist',
                extra=data,
            )
            return
        raise e

    status = await utils.check_document(content)
    if status is None:
        logger.info(
            'An error occurred when checking file',
            extra=data,
        )
        if get_level() == AppLevel.local:
            return
        # try to make retry
        raise AntivirusCheckException

    logger.info(
        'Update antivirus check of draft',
        extra={
            'draft_id': draft_id,
            'status': status,
        },
    )

    async with services.db.acquire() as conn:
        await set_status_draft_antivirus_check(
            conn,
            draft_id=draft_id,
            status=status,
            provider=AntivirusProvider.eset,
        )

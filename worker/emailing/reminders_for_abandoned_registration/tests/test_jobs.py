import logging

import ujson

from app.auth import db as auth_db
from app.auth.utils import update_user
from app.registration import enums as registration_enums
from app.services import services
from app.tests import common
from app.tokens import utils as token_utils
from worker.emailing.reminders_for_abandoned_registration import jobs

logger = logging.getLogger(__name__)


async def test_send_notification_about_abandoned_registration(aiohttp_client, mailbox):
    """
    Given a user which didn't complete registartion
    When calling initiate_notifications_about_abandoned_registration job
    Expected email to be sent successfully
    """
    # Arrange
    app, client = await common.prepare_app_client(aiohttp_client)

    # Act
    await jobs.initiate_notifications_about_abandoned_registration(app, data={}, logger=logger)
    assert len(mailbox) == 0

    # Register user
    await client.post(
        '/auth-api/registration',
        data=ujson.dumps(
            {
                'email': common.TEST_USER_EMAIL,
                'password': common.TEST_USER_PASSWORD,
                'source': registration_enums.RegistrationSource.vchasno.value,
            }
        ),
        headers=common.prepare_referer_headers(client),
    )
    # Assert registration email is sent
    assert len(mailbox) == 1

    # Assert abandoned regisration email wasn't sent
    await jobs.initiate_notifications_about_abandoned_registration(app, data={}, logger=logger)
    assert len(mailbox) == 1

    # Confirm email
    token = token_utils.generate_jwt_token(
        {'email': common.TEST_USER_EMAIL}, services.config.tokens.private_key
    )
    await client.get(f'/registration/confirm/{token}')
    # Assert welcome email is sent
    assert len(mailbox) == 2

    # Initiate job
    await jobs.initiate_notifications_about_abandoned_registration(app, data={}, logger=logger)
    # Assert abandoned registration email was sent
    assert len(mailbox) == 3

    # Update user to complete registration
    async with services.db.acquire() as conn:
        user = await auth_db.select_base_user(conn, email=common.TEST_USER_EMAIL)
        assert user.registration_completed is False
        await update_user(
            conn=conn,
            user_id=user.id,
            data={'registration_completed': True},
        )

    # Assert new email wasn't sent
    await jobs.initiate_notifications_about_abandoned_registration(app, data={}, logger=logger)
    assert len(mailbox) == 3

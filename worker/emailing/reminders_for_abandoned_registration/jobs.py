import logging
from datetime import timedelta

from aiohttp import web

from app.auth import db as auth_db
from app.i18n import _
from app.lib import datetime_utils as dt_utils
from app.lib import emailing
from app.lib import types as core_types
from app.notifications import utils as notifications_utils
from app.services import services
from worker import topics
from worker.emailing.reminders_for_abandoned_registration import db


async def initiate_notifications_about_abandoned_registration(
    _: web.Application, data: core_types.DataDict, logger: logging.Logger
) -> None:
    """
    Initiate reminders to users that doesn't provide ECP after email confirming.
    """

    async with services.db_readonly.acquire() as conn:
        recipients = await db.select_users_with_incomplete_registration(
            conn=conn,
            time_ago=dt_utils.utc_now() - timedelta(days=1),
        )

    if not recipients:
        logger.info('No users with incomplete registration found.')
        return

    await services.kafka.send_records(
        topic=topics.SEND_REMINDER_FOR_ABANDONED_REGISTRATION,
        values=[{'recipient_email': recipient.email} for recipient in recipients],
    )


async def send_notification_about_abandoned_registration(
    __: web.Application, data: core_types.DataDict, logger: logging.Logger
) -> None:
    """
    Send email about incomplete registration, with instruction how to get
    ECP and complete registration.
    """
    recipient_email: str = data['recipient_email']

    async with services.db_readonly.acquire() as conn:
        recipient = await auth_db.select_base_user(conn, email=recipient_email)

    if not recipient or not recipient.email:
        logger.info('Recipient not found.', extra={'email': recipient_email})
        return

    subject = _('У вас виникли труднощі з перевіркою по КЕП/ЕЦП?')
    context = {
        'subject': subject,
        'first_name': recipient.first_name,
        'unsubscribe': await notifications_utils.prepare_unsubscribe_block(email=recipient.email),
    }
    await emailing.send_email(
        recipient_mixed=recipient.email,
        subject=subject,
        template_name='abandoned_registration',
        context=context,
    )

import logging
from collections.abc import AsyncIterator
from typing import (
    Any,
    NamedTuple,
)

from app.document_automation import conditions
from app.document_categories.types import PublicDocumentCategory
from app.documents.db import (
    select_document_access_level,
    select_document_by_id,
    select_document_recipients,
    select_document_uploader_info,
)
from app.documents.enums import DocumentAccessLevel
from app.documents.types import DocumentWithUploader
from app.lib.database import DBConnection, DBRow
from app.lib.types import (
    DataDict,
    StrList,
)

logger = logging.getLogger(__name__)


class ScoredAutomation(NamedTuple):
    automation: DBRow
    score: int


class Recipients(NamedTuple):
    emails: StrList
    edrpous: StrList


async def _prepare_recipients_context(
    conn: DBConnection,
    document: DocumentWithUploader,
    company_edrpou: str,
) -> Recipients:
    """
    Build a list of recipients for document automation.
    Emails - lists of emails who receive a document in a current company.
      We can set emails of coworkers, who receive a document,
      but can't set condition on emails of recipients.
      This list is intended for only for incommoding documents.
    Edrpous - list of ALL companies who receive a document.
      We can set conditions on edrpou of recipients.
      This list can be used for incoming and outgoing documents.
    """
    recipients = await select_document_recipients(conn, document_id=document.id)
    edrpous = set()
    emails = set()

    # Add owner of the document to the list of recipients
    edrpous.add(document.edrpou_owner)

    for recipient in recipients:
        edrpous.add(recipient.edrpou)

        # Skip company for which automation is running
        if recipient.edrpou != company_edrpou:
            continue

        if recipient.emails:
            for email in recipient.emails:
                emails.add(email.lower())

    return Recipients(emails=list(emails), edrpous=list(edrpous))


def _prepare_document_sign_process(document: DocumentWithUploader) -> str:
    if document.is_internal:
        return 'internal'
    if document.is_multilateral:
        return 'multilateral'
    return 'bilateral'


def _prepare_document_side(document: DocumentWithUploader, company_edrpou: str) -> str:
    if document.is_internal:
        return 'internal'
    if document.edrpou_owner == company_edrpou:
        return 'outbox'
    return 'inbox'


async def prepare_document_context(
    conn: DBConnection,
    *,
    document_id: str,
    company_edrpou: str,
) -> DataDict | None:
    document_row = await select_document_by_id(conn, document_id)
    if not document_row:
        logger.info('Document for automation not found')
        return None

    document = DocumentWithUploader.from_row(document_row)

    uploader_info = await select_document_uploader_info(conn, document_id=document_id)

    # sometimes race condition appears, check one more time if value exists
    if not uploader_info:
        logger.info('Document for automation not found [2]')
        return None

    # We skip private documents for automation to avoid opening access to someone
    # who is not allowed to see it. As user can still apply automation template
    # manually in the UI, but we don't do it automatically.
    access_level = await select_document_access_level(
        conn,
        document_id=document_id,
        edrpou=company_edrpou,
    )
    if access_level == DocumentAccessLevel.private:
        logger.info('Document is private and not available for automation')
        return None

    recipients = await _prepare_recipients_context(
        conn,
        document=document,
        company_edrpou=company_edrpou,
    )
    sign_process = _prepare_document_sign_process(document)
    document_side = _prepare_document_side(document, company_edrpou)
    if document.category:
        document_category = document.category
    else:
        document_category = PublicDocumentCategory.other.value
    return {
        'document_side': document_side,
        'document_recipients.email': recipients.emails,
        'document_recipients.edrpou': recipients.edrpous,
        'document_sign_process': sign_process,
        'document_category': document_category,
        'document_uploaded_by': uploader_info.email.lower(),
        'document_uploaded_by_edrpou': document.edrpou_owner,
        'document_amount': document.amount,
    }


def lowercase_email_for_conditions(item: Any) -> Any:
    """
    Finds #document_recipients.email in list and applies lower to next item.
    """
    if isinstance(item, dict):
        key, value = list(item.items())[0]
        return {key: lowercase_email_for_conditions(value)}

    if isinstance(item, list):
        if isinstance(item[0], dict):
            return [lowercase_email_for_conditions(i) for i in item]
        if isinstance(item[0], str):
            if item[0] in {'#document_recipients.email', '#document_uploaded_by'}:
                return [item[0], item[1].lower()]
            return item
    return None


async def get_automations_scores(
    automations: list[DBRow],
    context: DataDict,
) -> AsyncIterator[ScoredAutomation]:
    for automation in automations:
        normalized_conditions = lowercase_email_for_conditions(automation.conditions)

        logger.info(
            msg='Check automation to apply',
            extra={
                'conditions': normalized_conditions,
                'context': context,
                'template_id': automation.id,
            },
        )

        # If at least one conditions can be applied to document context,
        # then we stop iteration and return current automation
        score = conditions.execute(expression=normalized_conditions, context=context)
        logger.info(
            msg='Got score for automation',
            extra={'template_id': automation.id, 'score': score},
        )
        yield ScoredAutomation(automation=automation, score=score)

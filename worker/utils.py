import asyncio
import datetime
import logging
import os
import sys
import uuid
from collections.abc import Callable
from datetime import timedelta
from functools import wraps

import aiohttp
from aiohttp import web

from app.lib.locks import set_bigger_lock
from app.lib.types import DataDict
from app.services import services
from worker.types import (
    RETRY_EXCEPTIONS,
    ExceptionTuple,
    JobFunction,
    RetryConfig,
    WorkerType,
)


class JobTimeoutError(TimeoutError):
    pass


RETRY_ATTEMPT_KEY = '__retry_attempt'
RETRY_START_TIME_KEY = '__retry_start_time'
RETRY_CONFIG_KEY = '__retry_config'
RETRY_TOPIC_KEY = '__retry_topic'
DELAY_QUEUED_TIME_KEY = '__delay_queued_time'
DELAY_MINUTES_KEY = '__delay_minutes'
RETRY_DEFAULT_MAX_AGE = timedelta(hours=5)
RETRY_DEFAULT_DELAY = 5

DEFAULT_JOB_TIMEOUT = 25.0
ACTIONS_REPORT_JOB_TIMEOUT = 600.0  # 10 minutes
UPDATE_DOCUMENT_DATE_DELIVERED_TIMEOUT = 60.0
DOCUMENT_PROCESS_TIMEOUT = 60.0

WORKER_REPORTS_MAX_POLLING_INTERVAL = 1000
WORKER_DEFAULT_POLLING_INTERVAL = 300

logger = logging.getLogger(__name__)


def retry_config(
    *,
    max_attempts: int,
    max_age: timedelta = RETRY_DEFAULT_MAX_AGE,
    exceptions: ExceptionTuple = RETRY_EXCEPTIONS,
    delay_minutes: int = RETRY_DEFAULT_DELAY,
    raise_on_exception: bool = True,
) -> Callable[[JobFunction], JobFunction]:
    """
    Decorator for worker job, that tells retry this job again if something is failed.
    """

    def wrapper(func: JobFunction) -> JobFunction:
        @wraps(func)
        async def wrapped(app: web.Application, data: DataDict, logger: logging.Logger) -> None:
            return await func(app, data, logger)

        wrapped.__dict__[RETRY_CONFIG_KEY] = RetryConfig(
            max_attempts=max_attempts,
            max_age=max_age,
            exceptions=exceptions,
            delay=delay_minutes,
            raise_on_exception=raise_on_exception,
        )
        return wrapped

    return wrapper


def expose_worker_startup_properties() -> tuple[str, str]:
    """
    Parse and set to environment variables worker properties
    Command to run worker properly:
        - python -m worker *service_name* *concurrency*
    where
        - sys.argv[-1] - concurrency level
        - sys.argv[-2] - worker type (i.e. antivirus, document_update etc.)
    """
    try:
        worker_type = WorkerType(sys.argv[-2]).value
        concurrency = str(int(sys.argv[-1]))
    except (ValueError, IndexError):
        logger.info(
            'Worker type and concurrency not found on startup. Setting to default',
        )

        # On local/staging we don't specify worker level
        worker_type = WorkerType.all_jobs.value
        concurrency = '1'

    os.environ['WORKER_TYPE'] = worker_type
    os.environ['WORKER_CONCURRENCY'] = concurrency

    return worker_type, concurrency


def get_consumer_heartbeat_max_polling_interval() -> int:
    """
    Depending on topics that worker type is subscribed to we define
    different polling interval for broker when it executes heartbeat on consumer
    f.e.
        actions report topic has 600s time execution limit
        it means that polling should be ongoing at least 600*1000ms until consumer finishes the job
    """
    poll_interval_seconds = WORKER_DEFAULT_POLLING_INTERVAL
    if worker_type := os.getenv('WORKER_TYPE'):
        poll_interval_seconds = {
            WorkerType.reports: WORKER_REPORTS_MAX_POLLING_INTERVAL,
        }.get(WorkerType(worker_type), WORKER_DEFAULT_POLLING_INTERVAL)
    return poll_interval_seconds * 1000  # milliseconds


def get_job_timeout() -> float:
    """
    Returns job timeout based on topics that consumer is subscribed to:
        - Actions report - 600s
        - Update documents date_delivered - 60s
        - Other topics - 25s
        ...
    """
    if worker_type := os.getenv('WORKER_TYPE'):
        return {
            WorkerType.reports: ACTIONS_REPORT_JOB_TIMEOUT,
            WorkerType.document_update: UPDATE_DOCUMENT_DATE_DELIVERED_TIMEOUT,
            WorkerType.document_process: DOCUMENT_PROCESS_TIMEOUT,
        }.get(WorkerType(worker_type), DEFAULT_JOB_TIMEOUT)

    return DEFAULT_JOB_TIMEOUT


def aio_timeout(func: JobFunction) -> JobFunction:
    """Wrap job into timeout context manager"""

    @wraps(func)
    async def wrapped(app: web.Application, data: DataDict, logger: logging.Logger) -> None:
        if _without_timeout(func):
            return await func(app, data, logger)

        job_timeout = get_job_timeout()
        try:
            async with asyncio.timeout(job_timeout):
                return await func(app, data, logger)
        except TimeoutError as e:
            logger.exception(
                msg='Timeout error during job running',
                extra={'job_name': func.__name__, 'data': data},
            )
            raise JobTimeoutError() from e

    return wrapped


def _without_timeout(func: JobFunction) -> bool:
    """Disable timeout for some specific jobs"""
    if func.__name__ in {'temp_db_migrations'}:
        return True
    return False


vchasno_projects_retry_config = retry_config(
    max_attempts=20,
    exceptions=(TimeoutError, aiohttp.ClientError),
    delay_minutes=20,
    max_age=datetime.timedelta(days=3),
)


async def is_migration_duplicate_detected(
    key: str,
    ttl_seconds: int,
    cursor_uuid: str,
    reset: bool = False,
) -> bool:
    """
    Context manager to detect duplicated jobs by cursor_uuid
    """

    # Can be useful for the first run of the job
    if reset:
        await services.redis.delete(key)

    is_set = await set_bigger_lock(
        key=key,
        value=uuid.UUID(cursor_uuid).int,
        expire=ttl_seconds,
    )
    is_duplicate = not is_set

    if is_duplicate:
        logger.info(
            msg='Duplicated job detected, skip further duplication',
            extra={'cursor': cursor_uuid},
        )

    return is_duplicate

from app.auth.enums import PositionType
from app.esputnik import Event

POSITION_TYPE_ESPUTNIK_EVENT_MAP = {
    PositionType.accountant: Event.new_position_accountant,
    PositionType.director_accountant: Event.new_position_director_accountant,
    PositionType.lawyer: Event.new_position_lawyer,
    PositionType.director_lawyer: Event.new_position_director_lawyer,
    PositionType.hr: Event.new_position_hr,
    PositionType.director_hr: Event.new_position_director_hr,
    PositionType.it: Event.new_position_it,
    PositionType.sales: Event.new_position_sales,
    PositionType.director_sales: Event.new_position_director_sales,
    PositionType.middle_manager: Event.new_position_middle_manager,
    PositionType.top_manager_fop: Event.new_position_top_manager_fop,
    PositionType.top_manager_tov: Event.new_position_top_manager_tov,
    # There is no top_manager because it has special logic,
    # based on the company type (fop or tov)
}

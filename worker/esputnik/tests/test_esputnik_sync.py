from api.public.tests.common import TEST_RECIPIENT_EDRPOU, TEST_RECIPIENT_EMAIL
from app.tests.common import (
    cleanup_on_teardown,
    prepare_client,
    prepare_document_data,
    prepare_user_data,
    sign_document,
)


async def test_esputnik_contact_update(aiohttp_client, esputnik_box):
    app, client, user = await prepare_client(aiohttp_client, create_billing_account=True)
    recipient = await prepare_user_data(
        app, company_edrpou=TEST_RECIPIENT_EDRPOU, email=TEST_RECIPIENT_EMAIL
    )
    document1 = await prepare_document_data(
        app,
        user,
        create_document_access_for_recipients=False,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': [recipient.email]}],
    )
    document2 = await prepare_document_data(
        app,
        user,
        create_document_access_for_recipients=False,
        document_recipients=[{'edrpou': recipient.company_edrpou, 'emails': [recipient.email]}],
    )
    try:
        assert len(esputnik_box) == 0
        # user's first document signing
        await sign_document(
            client,
            document_id=document1.id,
            signer=user,
            recipient_edrpou=recipient.company_edrpou,
            recipient_email=recipient.email,
        )
        # got ESputnik sync request
        # FIXME: temporary removed first sign sync (app.signatures.views:add)
        # assert len(esputnik_box) == 1

        await sign_document(
            client,
            document_id=document2.id,
            signer=user,
            recipient_edrpou=recipient.company_edrpou,
            recipient_email=recipient.email,
        )
        # no requests afterwards
        # FIXME: temporary removed first sign sync
        # assert len(esputnik_box) == 1
    finally:
        await cleanup_on_teardown(app)

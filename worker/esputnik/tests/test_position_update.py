from datetime import timed<PERSON>ta

import pytest

from app.esputnik import Event
from app.lib.datetime_utils import utc_now
from app.profile.utils import _send_new_position_esputnik_event
from app.tests.common import prepare_client


@pytest.mark.parametrize(
    ('role_position', 'expected_event'),
    [
        ('бухгалтер', Event.new_position_accountant),
        ('головний бухгалтер', Event.new_position_director_accountant),
        ('юрист', Event.new_position_lawyer),
        ('керівник юридичного відділу', Event.new_position_director_lawyer),
        ('hr', Event.new_position_hr),
        ('керівник відділу кадрів', Event.new_position_director_hr),
        ('іт-директор', Event.new_position_it),
        ('менеджер з продажу', Event.new_position_sales),
        ('керівник відділу продажів', Event.new_position_director_sales),
        ('адміністратор', Event.new_position_middle_manager),
    ],
)
async def test_esputnik_new_position_set_event(
    aiohttp_client, esputnik_box, role_position, expected_event
):
    """
    Send event because user has no position
    """
    app, client, user = await prepare_client(aiohttp_client, role_position=role_position)

    await _send_new_position_esputnik_event(
        role_id=user.role_id,
        old_position='',
    )
    assert len(esputnik_box) == 1
    assert esputnik_box[0]['data']['eventTypeKey'] == expected_event.value


@pytest.mark.parametrize(
    ('role_position', 'expected_event', 'edrpou'),
    [
        ('Власник', Event.new_position_top_manager_tov, '12345678'),
        ('Власник', Event.new_position_top_manager_fop, '*********'),
        ('Голова ОСББ', Event.new_position_top_manager_tov, '12345678'),
        ('Голова ОСББ', None, '*********'),  # can't use only tov position for fop
        ('фоп', None, '12345678'),  # can't use only fop position for tov
    ],
)
async def test_esputnik_new_position_set_event_top(
    aiohttp_client, esputnik_box, role_position, expected_event, edrpou
):
    app, client, user = await prepare_client(
        aiohttp_client, role_position=role_position, company_edrpou=edrpou
    )

    await _send_new_position_esputnik_event(
        role_id=user.role_id,
        old_position='',
    )
    if expected_event:
        assert len(esputnik_box) == 1
        assert esputnik_box[0]['data']['eventTypeKey'] == expected_event.value
    else:
        assert len(esputnik_box) == 0


async def test_esputnik_new_position_set_event_has_position(aiohttp_client, esputnik_box):
    """
    Do not send event because user already has some position
    """
    app, client, user = await prepare_client(aiohttp_client, role_position='hr')

    await _send_new_position_esputnik_event(
        role_id=user.role_id,
        old_position='somebody',
    )
    assert len(esputnik_box) == 0


async def test_esputnik_new_position_set_event_older_than_60_days(aiohttp_client, esputnik_box):
    """
    Do not send event because user was created more than 60 days ago
    """
    app, client, user = await prepare_client(
        aiohttp_client,
        role_position='hr',
        date_created=utc_now() - timedelta(days=61),
    )

    await _send_new_position_esputnik_event(
        role_id=user.role_id,
        old_position='',
    )
    assert len(esputnik_box) == 0

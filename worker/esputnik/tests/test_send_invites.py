import logging

from app.lib.enums import Source
from app.tests.common import (
    SUPER_ADMIN_EDRPOU,
    prepare_client,
    prepare_contacts,
)
from worker.contacts.jobs import invite_unregistered_contacts

logger = logging.getLogger(__name__)


async def test_send_invites(mailbox, aiohttp_client):
    app, client, user = await prepare_client(
        aiohttp_client=aiohttp_client,
        is_admin=True,
        company_edrpou=SUPER_ADMIN_EDRPOU,
    )
    async with app['db'].acquire() as conn:
        await prepare_contacts(app, conn, user)

    data = {
        'user_role_id': user.role_id,
        'source': Source.api_internal,
    }
    await invite_unregistered_contacts(app=app, data=data, logger=logger)
    assert len(mailbox) == 5

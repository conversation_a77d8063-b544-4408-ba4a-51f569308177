import datetime
import logging

import pytest

from app.esputnik import enums as esputnik_enums
from app.services import services
from app.tests import common
from app.youcontrol import db as youcontrol_db
from app.youcontrol.client import YouControlClient
from app.youcontrol.enums import YouControlResource
from app.youcontrol.exceptions import (
    YouControlNotFoundException,
)
from worker.esputnik.company_registration_event import jobs

logger = logging.getLogger(__name__)


@pytest.mark.parametrize(
    'company_type, expected_event',
    (
        ('Private_no_record', esputnik_enums.Event.company_registered_private_individual),
        ('Private_stopped', esputnik_enums.Event.company_registered_private_individual),
        ('TOV', esputnik_enums.Event.company_registered_tov),
        ('FOP', esputnik_enums.Event.company_registered_fop),
    ),
)
async def test_generate_esputnik_company_registration_event(
    aiohttp_client,
    company_type,
    expected_event,
    esputnik_box,
):
    """
    Given a newly registered company.
        The company has a single user, who is automatically assigned as the admin.
    When the job to generate the eSputnik company registration event is triggered.
    Then a valid registration event should be generated for the company admin.
    """

    # Arrange
    app, client, user = await common.prepare_client(
        aiohttp_client,
        company_edrpou=common.FOP_EDRPOU
        if company_type == 'FOP' or company_type.startswith('Private')
        else common.TOV_EDRPOU,
        is_admin=True,
    )

    async with services.db.acquire() as conn:
        # Arrange youcontrol record for private company
        if company_type == 'Private_no_record':
            await youcontrol_db.upsert_youcontrol_company(
                conn=conn,
                company_id=user.company_id,
                data={
                    'is_company_active': False,
                    'response_edr': {'status': 'Фізична особа'},
                    'date_sync_edr': datetime.datetime.now(),
                },
            )
        elif company_type == 'Private_stopped':
            await youcontrol_db.upsert_youcontrol_company(
                conn=conn,
                company_id=user.company_id,
                data={
                    'is_company_active': False,
                    'contractor_type': 'Фізична особа-підприємець (ФОП)',
                    'response_edr': {},
                    'date_sync_edr': datetime.datetime.now(),
                },
            )
        elif company_type == 'FOP':
            await youcontrol_db.upsert_youcontrol_company(
                conn=conn,
                company_id=user.company_id,
                data={
                    'is_company_active': True,
                    'contractor_type': 'Фізична особа-підприємець (ФОП)',
                    'response_edr': {},
                    'date_sync_edr': datetime.datetime.now(),
                },
            )

    # Act
    await jobs.generate_esputnik_company_registration_event(
        app,
        data={'edrpou': user.company_edrpou},
        logger=logger,
    )

    # Assert
    assert len(esputnik_box) == 1
    assert esputnik_box[0]['data']['eventTypeKey'] == expected_event.value


async def test_youcontrol_fop_company_not_fetched(aiohttp_client, monkeypatch):
    """
    Given a company which doesn't have youcontrol record yet
    When trying to generate esputnik company registartion event
    Expected task to be eneuqued with valid attempt
    """
    # Arrange
    app, client, user = await common.prepare_client(
        aiohttp_client,
        company_edrpou=common.FOP_EDRPOU,
        is_admin=True,
    )

    async def mock_youcontrol_request(edrpou: str, resource: YouControlResource):
        raise YouControlNotFoundException(edrpou=edrpou)

    monkeypatch.setattr(YouControlClient, 'get_resource_for_edrpou', mock_youcontrol_request)

    # Act
    await jobs.generate_esputnik_company_registration_event(
        app,
        data={'edrpou': user.company_edrpou},
        logger=logger,
    )

    # Assert jobs were enqueued
    # 1 - Initial job was run manually above
    # 2 - YouControl sync triggered - successfull sync for private individual
    #   (see mock_youcontrol_request above)
    # 3 - Send eSputnik event based on fetched youcontrol data
    assert services.kafka.messages == [
        (
            'vchasno-test-sync-company-info-from-youcontrol',
            {
                'edrpou': common.FOP_EDRPOU,
            },
        ),
        (
            'vchasno-test-esputnik-company-registration-send-event',
            {
                'attempt': 2,
                'edrpou': common.FOP_EDRPOU,
            },
        ),
        (
            'vchasno-test-esputnik-send-event-to-users',
            {
                'company_id': user.company_id,
                'event': esputnik_enums.Event.company_registered_private_individual.value,
            },
        ),
    ]

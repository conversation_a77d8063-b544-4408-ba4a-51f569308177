import logging

from aiohttp import web

from app.auth import db as auth_db
from app.auth import utils as auth_utils
from app.esputnik import enums as esputnik_enums
from app.flags.enums import FeatureFlags
from app.flags.utils import get_flag
from app.lib import types as core_types
from app.services import services
from app.youcontrol import db as youcontrol_db
from worker import topics

MAX_YOUCONTROL_FETCH_ATTEMPTS = 20


async def generate_esputnik_company_registration_event(
    _: web.Application,
    data: core_types.DataDict,
    logger: logging.Logger,
) -> None:
    if get_flag(FeatureFlags.DISABLE_ESPUTNIK_COMPANY_REGISTRATION_SEND_EVENT):
        logger.info('Skipping event generation')
        return

    edrpou: str = data['edrpou']
    attempt: int = data.get('attempt', 1)

    async with services.db_readonly.acquire() as conn:
        company = await auth_db.select_company_by_edrpou(conn, edrpou)
        if not company:
            logger.info('Company is not found for generating company registration event')
            return

    if auth_utils.is_fop(edrpou):
        # Fetch youcontrol to decide whether edrpou is of a private individual or FOP
        async with services.db_readonly.acquire() as conn:
            youcontrol_company = await youcontrol_db.select_youcontrol_company(
                conn=conn,
                company_id=company.id,
            )

        # Send sync job and retry to generate event again in 5 minutes
        # if company info is not available yet
        if not youcontrol_company:
            if attempt > MAX_YOUCONTROL_FETCH_ATTEMPTS:
                # If maximum attempts reached, just treat company as FOP by default
                event = esputnik_enums.Event.company_registered_fop
            else:
                logger.info(
                    'Company info has not been fetched from YouControl yet. '
                    'Sync and retry in 5 minutes'
                )
                await services.kafka.send_record(
                    topic=topics.SYNC_COMPANY_INFO_FROM_YOUCONTROL,
                    value={'edrpou': company.edrpou},
                )
                async with services.db.acquire() as conn:
                    await services.kafka.add_task(
                        topic=topics.ESPUTNIK_COMPANY_REGISTRATION_SEND_EVENT,
                        delay_min=5,
                        data={'edrpou': edrpou, 'attempt': attempt + 1},
                        conn=conn,
                    )
                return
        elif youcontrol_company.is_private_individual:
            event = esputnik_enums.Event.company_registered_private_individual
        else:
            event = esputnik_enums.Event.company_registered_fop
    else:
        # TOV Edrpous can't be private individuals
        event = esputnik_enums.Event.company_registered_tov

    await services.kafka.send_record(
        topic=topics.ESPUTNIK_SEND_EVENT_TO_USERS,
        value={'event': event, 'company_id': company.id},
    )

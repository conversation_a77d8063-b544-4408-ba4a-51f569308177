import logging

from app.auth.enums import PositionType
from app.auth.utils import is_fop
from app.esputnik import Event
from app.esputnik.types import Contact
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.database import DBRow
from app.models import select_one
from app.profile.utils import build_esputnik_users_query
from app.services import services
from worker.esputnik.constants import POSITION_TYPE_ESPUTNIK_EVENT_MAP

logger = logging.getLogger(__name__)


async def get_esputnik_contact_by_email(
    email: str | None,
) -> Contact | None:
    if not email:
        logger.info('Esputnik contact email was not passed')
        return None

    use_readonly = get_flag(FeatureFlags.USE_READONLY_DB_FOR_ESPUTNIK)
    db = services.db_readonly if use_readonly else services.db

    query = build_esputnik_users_query(limit=1, offset=0, email=email)
    async with db.acquire() as conn:
        raw_contact = await select_one(conn, query)
    if not raw_contact:
        logger.info('Failed to sync ESputnik contact - user not found', extra={'email': email})
        return None

    return Contact.from_db(raw_contact)


def get_next_documents_sent_threshold(company_meta: DBRow) -> int | None:
    """
    Get next threshold when we should send esputnik event about sent documents
    """
    thresholds = [0, 1, 10, 25, 50]
    curr_value = 0
    if company_meta and company_meta.sent_documents_count_synced is not None:
        curr_value = company_meta.sent_documents_count_synced

    try:
        next_value_idx = thresholds.index(curr_value) + 1
        return thresholds[next_value_idx]
    except (ValueError, IndexError):
        return None


def get_esputnik_event_for_position_type(position_type: PositionType, edprou: str) -> Event | None:
    """
    Check if given top manger position is available for company.
    So position ONLY for fop can't be used as tov event and vice versa.
    """
    # position avaialbe for both fop and tov
    if position_type == PositionType.top_manager:
        return (
            Event.new_position_top_manager_fop
            if is_fop(edprou)
            else Event.new_position_top_manager_tov
        )
    # check if position is available for company type
    if (
        position_type == PositionType.top_manager_fop
        and not is_fop(edprou)
        or position_type == PositionType.top_manager_tov
        and is_fop(edprou)
    ):
        return None

    return POSITION_TYPE_ESPUTNIK_EVENT_MAP.get(position_type)

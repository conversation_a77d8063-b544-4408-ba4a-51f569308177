import logging
from datetime import timedelta

from aiohttp import web

from app import esputnik
from app.auth.db import (
    select_base_user,
    select_company_by_id,
    select_company_meta,
    select_role,
    select_user,
    update_company_documents_sent,
)
from app.auth.enums import PositionType
from app.es.utils import count_company_documents_sent
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.lib.datetime_utils import utc_now
from app.lib.types import DataDict
from app.models import select_one
from app.profile.utils import build_esputnik_users_query
from app.services import services
from worker.esputnik.utils import (
    get_esputnik_contact_by_email,
    get_esputnik_event_for_position_type,
    get_next_documents_sent_threshold,
)
from worker.topics import ESPUTNIK_GENERATE_EVENT
from worker.utils import retry_config


@retry_config(max_attempts=5)
async def generate_esputnik_documents_sent_event(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    if get_flag(FeatureFlags.DISABLE_ESPUTNIK_DOCUMENTS_SENT_EVENT):
        logger.warning('Worker job is disabled')
        return

    client = esputnik.get_client()
    if not client:
        logger.warning('ESputnik config was not found, job cancelled')
        return

    company_id: str = data['company_id']
    email: str = data['email']
    extra: DataDict = {'company_id': company_id, 'email': email}

    contact = await get_esputnik_contact_by_email(email)
    if not contact:
        logger.info('Contact was not found, documents sent event failed', extra=extra)
        return

    async with services.db.acquire() as conn:
        company = await select_company_by_id(conn, company_id)
        if not company:
            logger.info('Company was not found, documents sent event failed', extra=extra)
            return

        meta = await select_company_meta(conn, company_id=company_id)
        count = await count_company_documents_sent(company.edrpou)
        if not count:
            return

        next_threshold = get_next_documents_sent_threshold(meta)
        if not next_threshold or count < next_threshold:
            return

        extra['next_threshold'] = next_threshold

        event = esputnik.Event.get_event_by_count(next_threshold)
        if await client.generate_event(contact, event):
            logger.info('Sent documents sent event', extra=extra)
            await update_company_documents_sent(conn, company_id, next_threshold)
        else:
            logger.info('Documents sent event failed', extra=extra)


@retry_config(max_attempts=5)
async def generate_esputnik_first_incoming_signing_event(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    client = esputnik.get_client()
    if not client:
        logger.warning('ESputnik config was not found, job cancelled')
        return

    email: str = data['email']
    extra = {'email': email}

    query = build_esputnik_users_query(1, 1, email)

    use_readonly = get_flag(FeatureFlags.USE_READONLY_DB_FOR_ESPUTNIK)
    db = services.db_readonly if use_readonly else services.db

    async with db.acquire() as conn:
        raw_contact = await select_one(conn, query)
        if not raw_contact:
            logger.info('Contact was not found, first signing event failed', extra=extra)
            return

        event_exists = await esputnik.exists_esputnik_event(
            conn=conn,
            user_id=raw_contact.user_id,
            event=esputnik.Event.first_incoming_sign,
        )
        if event_exists:
            return

    contact = esputnik.Contact.from_db(raw_contact)
    if await client.generate_event(contact, esputnik.Event.first_incoming_sign):
        logger.info('Sent first signing event', extra=extra)
        async with services.db.acquire() as conn:
            await esputnik.create_esputnik_event(
                conn=conn,
                user_id=raw_contact.user_id,
                event=esputnik.Event.first_incoming_sign,
            )
    else:
        logger.warning('First signing event failed', extra=extra)


@retry_config(max_attempts=5)
async def add_new_esputnik_contact(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    contact = await get_esputnik_contact_by_email(data['email'])
    if not contact:
        logger.info('User was not found, add esputnik user failed', extra=data)
        return
    client = esputnik.get_client()
    if not client:
        logger.warning('ESputnik config was not found, job cancelled', extra=data)
        return
    await client.add_contact(contact=contact)
    logger.info('New contact was added to ESputnik', extra=data)


@retry_config(max_attempts=5)
async def update_esputnik_fields(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    # It's completely OK to get "None" email here because some users
    # might not have email in their profile at all (registered by phone)
    email = data.pop('email')
    contact = await get_esputnik_contact_by_email(email=email)
    if not contact:
        logger.info('User was not found, update esputnik fields failed', extra=data)
        return
    client = esputnik.get_client()
    if not client:
        logger.warning('ESputnik config was not found, job cancelled', extra=data)
        return
    await client.update_contact(contact=contact, fields=data)
    logger.info('Synced contact with ESputnik', extra=data)


@retry_config(max_attempts=5)
async def generate_esputnik_company_check_event(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    client = esputnik.get_client()
    if not client:
        logger.warning('ESputnik config was not found, job cancelled')
        return

    user_id: str = data['user_id']
    extra = {'user_id': user_id}
    async with services.db.acquire() as conn:
        event_exists = await esputnik.exists_esputnik_event(
            conn=conn,
            user_id=user_id,
            event=esputnik.Event.company_check,
        )
        if event_exists:
            return
        user = await select_base_user(conn, user_id=user_id)
        if not user:
            return
        contact = await get_esputnik_contact_by_email(user.email)
        if not contact:
            logger.info('Contact not found, company check event failed', extra=extra)
            return

        if await client.generate_event(contact, esputnik.Event.company_check):
            await esputnik.create_esputnik_event(
                conn=conn,
                user_id=user_id,
                event=esputnik.Event.company_check,
            )
            logger.info('Sent company check event', extra=extra)
        else:
            logger.warning('Company check event failed', extra=extra)


@retry_config(max_attempts=5)
async def generate_esputnik_event(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    email = data.get('email')
    log_extra = {'email': email}
    contact = await get_esputnik_contact_by_email(email)
    if not contact:
        logger.info('User was not found, generate esputnik event failed', extra=log_extra)
        return

    client = esputnik.get_client()
    if not client:
        logger.warning('ESputnik config was not found, job cancelled', extra=log_extra)
        return

    raw_event = data['event']
    log_extra['event'] = raw_event
    try:
        event = esputnik.Event[raw_event]
    except KeyError:
        logger.info('Invalid Esputnik event', extra=log_extra)
        return

    await client.generate_event(contact=contact, event=event)
    logger.info('Generated ESputnik event', extra=log_extra)


@retry_config(max_attempts=5)
async def esputnik_new_position_set_event(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Send event on new position set
    Rules when to send event:
    - User was created less than 60 days ago
    - User has no position
    - User has position from ROLE_POSITION_EVENT_MAP
    """
    role_id = data['role_id']
    old_position = data['old_position']

    if old_position:
        logger.info('Old position is not empty', extra={'role_id': role_id})
        return

    async with services.db.acquire() as conn:
        user = await select_user(conn, role_id=role_id)
        if not user:
            logger.info('User was not found', extra={'role_id': role_id})
            return

        role = await select_role(conn, role_id=role_id)
        if not role:
            logger.info('Role was not found', extra={'email': user.email})
            return

    if user.date_created + timedelta(days=60) < utc_now():
        logger.info('User was created more than 60 days ago', extra={'role_id': role_id})
        return

    if not role.position:
        logger.info('Role position is empty', extra={'role_id': role_id})
        return

    position_type = PositionType.from_role_position(role.position)
    if position_type is None:
        logger.info(
            'Position is not found',
            extra={'role_id': role_id, 'position_type': role.position.lower().strip()},
        )
        return

    event = get_esputnik_event_for_position_type(position_type, role.company_edrpou)
    if event is None:
        logger.info(
            'Event is not found',
            extra={'role_id': role_id, 'position_type': position_type},
        )
        return

    data = {'email': user.email, 'event': event.name}
    await services.kafka.send_record(ESPUTNIK_GENERATE_EVENT, data)

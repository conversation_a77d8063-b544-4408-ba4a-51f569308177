import datetime
import logging

import pytest

from app.events.document_actions import tables as document_action_tables
from app.events.user_actions import tables as user_actions_tables
from app.lib import datetime_utils as _datetime
from app.lib import partitions as partitions_utils
from app.services import services
from app.tests import common
from worker.migrations.jobs import (
    create_document_actions_table_partition_job,
    create_user_actions_table_partition_job,
)

logger = logging.getLogger(__name__)


@pytest.mark.parametrize(
    'mock_dt, expected_partition_name',
    (
        [
            datetime.datetime(2024, 12, 31),
            f'{document_action_tables.document_actions_table.name}_2025_01',
        ],
        [
            datetime.datetime(2023, 6, 1),
            f'{document_action_tables.document_actions_table.name}_2023_07',
        ],
        [
            datetime.datetime(2024, 10, 3),
            f'{document_action_tables.document_actions_table.name}_2024_11',
        ],
        [
            datetime.datetime(2024, 1, 1),
            f'{document_action_tables.document_actions_table.name}_2024_02',
        ],
    ),
)
async def test_create_document_actions_table_partition(
    aiohttp_client, mocker, mock_dt, expected_partition_name
):
    """
    Given an events database
    When calling partitioning for table document_actions
    Expected to create partitioned table
    """
    app, client = await common.prepare_app_client(aiohttp_client)

    # Patch current datetime
    mocker.patch.object(_datetime.UTC_TZ, 'localize', return_value=mock_dt)

    # Run partition creation job
    await create_document_actions_table_partition_job(app, {}, logger)

    # Assert partition created
    async with services.events_db.acquire() as conn:
        is_partition_exists = await partitions_utils.exists_partition_by_name(
            conn=conn, partition_name=f'{expected_partition_name}'
        )

    assert is_partition_exists is True


async def test_dont_create_document_actions_table_partition(aiohttp_client):
    """
    Given an events database and partition of document actions that already exists
    When calling partitioning for table document_actions
    Expected not to create any partitions
    """

    app, client = await common.prepare_app_client(aiohttp_client)

    # Run worker job
    await create_document_actions_table_partition_job(app, {}, logger)

    # Assert partition was created
    async with services.events_db.acquire() as conn:
        partitions_count = await partitions_utils.count_table_partitions(
            conn=conn,
            table_name=document_action_tables.document_actions_table.name,
        )
        assert partitions_count == 2

    # Run worker job AGAIN
    await create_document_actions_table_partition_job(app, {}, logger)

    # Assert there is no partitions created
    async with services.events_db.acquire() as conn:
        partitions_count = await partitions_utils.count_table_partitions(
            conn=conn,
            table_name=document_action_tables.document_actions_table.name,
        )
        assert partitions_count == 2


@pytest.mark.parametrize(
    'mock_dt, expected_partition_name',
    (
        [
            datetime.datetime(2024, 12, 31),
            f'{user_actions_tables.user_actions_table.name}_2025_01',
        ],
        [
            datetime.datetime(2023, 6, 1),
            f'{user_actions_tables.user_actions_table.name}_2023_07',
        ],
        [
            datetime.datetime(2024, 10, 3),
            f'{user_actions_tables.user_actions_table.name}_2024_11',
        ],
        [
            datetime.datetime(2024, 1, 1),
            f'{user_actions_tables.user_actions_table.name}_2024_02',
        ],
    ),
)
async def test_create_user_actions_table_partition(
    aiohttp_client, mocker, mock_dt, expected_partition_name
):
    """
    Given an events database
    When calling partitioning for table user_actions
    Expected to create partitioned table
    """
    app, client = await common.prepare_app_client(aiohttp_client)

    # Patch current datetime
    mocker.patch.object(_datetime.UTC_TZ, 'localize', return_value=mock_dt)

    # Run partition creation job
    await create_user_actions_table_partition_job(app, {}, logger)

    # Assert partition was created successfully
    async with services.events_db.acquire() as conn:
        is_partition_exists = await partitions_utils.exists_partition_by_name(
            conn=conn, partition_name=f'{expected_partition_name}'
        )

    assert is_partition_exists is True


async def test_dont_create_user_actions_table_partition(aiohttp_client):
    """
    Given an events database and partition of user actions that already exists
    When calling partitioning for table user_actions
    Expected not to create any partitions
    """

    app, client = await common.prepare_app_client(aiohttp_client)

    # Run worker job
    await create_user_actions_table_partition_job(app, {}, logger)

    # Assert partition was created
    async with services.events_db.acquire() as conn:
        partitions_count = await partitions_utils.count_table_partitions(
            conn=conn,
            table_name=user_actions_tables.user_actions_table.name,
        )
        # Asserting 2 because of d8b4d90cab2e revision
        assert partitions_count == 2

    # Run worker job AGAIN
    await create_user_actions_table_partition_job(app, {}, logger)

    # Assert there is no partitions created
    async with services.events_db.acquire() as conn:
        partitions_count = await partitions_utils.count_table_partitions(
            conn=conn,
            table_name=user_actions_tables.user_actions_table.name,
        )
        # Asserting 2 because of d8b4d90cab2e revision
        assert partitions_count == 2

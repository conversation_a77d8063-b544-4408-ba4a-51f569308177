import logging

from app.auth.db import select_user
from app.services import services
from app.tests.common import TEST_DOCUMENT_EMAIL_RECIPIENT, prepare_client, prepare_user_data
from worker.migrations.jobs import migrate_sign_review_permission

logger = logging.getLogger(__name__)


async def test_migrate_sign_review_permission(aiohttp_client):
    """
    Make sure job is working and updates the database correctly
    """
    app, client, user = await prepare_client(
        aiohttp_client,
        can_sign_and_reject_document=False,
    )
    user2 = await prepare_user_data(
        app,
        email=TEST_DOCUMENT_EMAIL_RECIPIENT,
        can_sign_and_reject_document=True,
    )

    await migrate_sign_review_permission(app, {'limit': 1}, logger)

    # Assert
    async with services.db.acquire() as conn:
        user_updated = await select_user(
            conn=conn,
            role_id=user.role_id,
        )
        user2_updated = await select_user(
            conn=conn,
            role_id=user2.role_id,
        )

    assert user_updated.can_sign_and_reject_document is False
    assert user_updated.can_sign_and_reject_document_external is False
    assert user_updated.can_sign_and_reject_document_internal is False
    assert user2_updated.can_sign_and_reject_document is True
    assert user2_updated.can_sign_and_reject_document_external is True
    assert user2_updated.can_sign_and_reject_document_internal is True

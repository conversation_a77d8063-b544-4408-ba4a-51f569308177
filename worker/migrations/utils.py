import logging

import sqlalchemy as sa

from app.auth.tables import company_table
from app.lib import datetime_utils as dt_utils
from app.lib import partitions as partitions_utils
from app.lib.database import DBConnection, DBRow
from app.models import select_one

logger = logging.getLogger(__name__)


async def select_next_company(
    conn: DBConnection,
    last_edrpou: str | None = None,
    disable_big_companies: bool = False,
) -> DBRow | None:
    filters = []

    query = company_table.select().order_by(company_table.c.edrpou.asc()).limit(1)

    if last_edrpou:
        filters.append(company_table.c.edrpou > last_edrpou)

    if disable_big_companies:
        filters.append(
            company_table.c.edrpou.notin_(
                [
                    '55555555',
                    '22859846',  # LIFE
                    '31316718',  # NP
                    '38324133',  # POSTFINANCE
                    '40283641',  # ZAKUPKI
                    '37193071',  # ROZETKA
                ]
            )
        )

    if filters:
        query = query.where(sa.and_(*filters))

    return await select_one(conn, query)


async def create_table_partition_for_next_month(conn: DBConnection, table_name: str) -> None:
    """
    Creates a monthly partition for table

    Example:
        - table_name = user_actions
        - current date - 2024-02-01
        - next partition datetime range - 2024-03-01 - 2024-04-01

    IMPORTANT: before trying to create a partition for any table,
    you need to be sure this table supports partitioning (by range, by seqnum etc.)
    """
    current_datetime = dt_utils.utc_now()

    # Get time ranges for the next monthly partition
    partition_datetime = dt_utils.first_day_next_month(current_datetime)
    next_partition_datetime = dt_utils.first_day_next_month(partition_datetime)

    log_extra = {
        'table_name': table_name,
        'partition_datetime': partition_datetime,
        'next_partition_datetime': next_partition_datetime,
    }

    logger.info('Partition meta', extra=log_extra)

    partition_prefix = partition_datetime.strftime('_%Y_%m')

    is_partition_exists = await partitions_utils.exists_partition_by_name(
        conn=conn, partition_name=f'{table_name}{partition_prefix}'
    )
    if is_partition_exists is True:
        logger.info('Partition already been created. Skipping.', extra=log_extra)
        return

    # Create partition within time range
    await partitions_utils.create_partition(
        conn=conn,
        table_name=table_name,
        prefix=partition_prefix,
        from_dt=partition_datetime,
        to_dt=next_partition_datetime,
    )

    logger.info('Partition successfully created', extra=log_extra)

import logging
import typing as t

from aiohttp import web

from app.auth.db import (
    select_role_by_id,
    update_role_has_signed_documents,
)
from app.crm.utils import send_role_to_crm
from app.documents.db import select_document
from app.documents.enums import DocumentSource
from app.lib import edi
from app.lib.edi import EDIPayloadHandleAddSignature
from app.lib.hrs.client import HRSClientError
from app.lib.types import DataDict
from app.services import services
from app.signatures.db import select_signature_by_id
from worker.utils import vchasno_projects_retry_config

if t.TYPE_CHECKING:
    from app.lib.hrs.client import HRSClient


async def update_has_role_signed_documents(
    app: web.Application, data: DataDict, logger: logging.Logger
) -> None:
    """
    Set has_signed_documents to True for given role.

    :param data: dict:
       - role_id: str - given role for updating has_signed_documents column
    """
    role_id: str = data['role_id']

    async with app['db'].acquire() as conn:
        role = await select_role_by_id(conn, role_id)
        if not role:
            msg = 'Can not update has_signed_documents for role'
            logger.warning(msg=msg, extra={'role_id': role_id})
            return

        await update_role_has_signed_documents(conn, role_id)

    await send_role_to_crm(role_id)


@vchasno_projects_retry_config
async def send_signature_to_vchasno_projects(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """Send signature to EDI or HRS."""
    document_id: str = data['document_id']
    vendor_id: str | None = data.get('vendor_id')
    signature_id: str = data['signature_id']
    signature_edrpou: str = data['signature_edrpou']
    document_source = DocumentSource(data['document_source'])

    if document_source.is_from_edi:
        await _send_signature_to_edi(
            document_id=document_id,
            vendor_id=vendor_id,
            signature_id=signature_id,
            signature_edrpou=signature_edrpou,
            document_source=document_source,
        )

    if document_source.is_from_hrs:
        await _send_signature_to_hrs(
            document_id=document_id,
            signature_id=signature_id,
            logger=logger,
        )


async def _send_signature_to_edi(
    document_id: str,
    vendor_id: str | None,
    signature_id: str,
    signature_edrpou: str,
    document_source: DocumentSource,
) -> None:
    """Send signature to EDI."""

    if not services.config.edi:
        return

    client = edi.Client()
    await client.request(
        EDIPayloadHandleAddSignature(
            document_id=document_id,
            vendor_id=vendor_id,
            signature_id=signature_id,
            signature_edrpou=signature_edrpou,
            document_source=document_source,
        )
    )


async def _send_signature_to_hrs(
    document_id: str,
    signature_id: str,
    logger: logging.Logger,
) -> None:
    """Send signature to HRS."""
    client: HRSClient = services.hrs_client

    async with services.db.acquire() as conn:
        signature = await select_signature_by_id(
            conn=conn,
            signature_id=signature_id,
        )

        if not signature or not signature.user_email:
            logger.warning(
                'Cannot send signature to HRS: signature not found or user email is empty',
                extra={'document_id': document_id, 'signature_id': signature_id},
            )
            return

    async with services.db.acquire() as conn:
        role = await select_role_by_id(conn=conn, role_id=signature.role_id)

    if not role:
        logger.warning(
            'Cannot send signature to HRS: role not found',
            extra={'document_id': document_id, 'signature_id': signature_id},
        )
        return

    await client.sign_document(
        document_id=document_id,
        email=role.user_email,
        vchasno_id=role.user_id,
        edrpou=role.company_edrpou,
    )


@vchasno_projects_retry_config
async def send_documents_rejected_to_hrs(
    app: web.Application,
    data: DataDict,
    logger: logging.Logger,
) -> None:
    """
    Send document rejected to HRS.
    """
    document_ids: list[str] = data['document_ids']
    initiator_role_id: str | None = data['initiator_role_id']
    comment: str = data['comment'] or ''

    async with services.db.acquire() as conn:
        role = await select_role_by_id(conn=conn, role_id=initiator_role_id)

    if not role:
        logger.warning(
            'Cannot send document rejected to HRS: role not found',
            extra={'document_ids': document_ids, 'initiator_role_id': initiator_role_id},
        )
        return

    client: HRSClient = services.hrs_client

    for document_id in document_ids:
        try:
            async with services.db.acquire() as conn:
                document = await select_document(conn=conn, document_id=document_id)

                if not document or not document.source.is_from_hrs:
                    continue

                await client.reject_document(
                    document_id=document_id,
                    email=role.user_email,
                    vchasno_id=role.user_id,
                    edrpou=role.company_edrpou,
                    reject_reason=comment,
                )
        except HRSClientError:
            logger.error(
                'Cannot send document rejected to HRS',
                extra={'document_id': document_id, 'initiator_role_id': initiator_role_id},
            )

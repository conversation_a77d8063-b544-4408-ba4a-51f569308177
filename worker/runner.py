import asyncio
import logging
import time
import typing as t
import uuid
from collections import defaultdict
from collections.abc import Iterator
from contextlib import contextmanager, suppress
from copy import deepcopy

from aiohttp import web
from aiokafka import (
    AIOKafkaConsumer,
    ConsumerRebalanceListener,
    ConsumerRecord,
    TopicPartition,
)
from aiokafka.errors import KafkaError
from logevo import set_current_rxid

from app.lib import error_reporting, tracking
from app.lib.types import ConsumedChunk, DataDict
from app.services import services
from worker.jobs import TOPIC_JOBS
from worker.types import DelayedJob, JobFunction, RetryConfig
from worker.utils import (
    RETRY_ATTEMPT_KEY,
    RETRY_CONFIG_KEY,
    RETRY_START_TIME_KEY,
    JobTimeoutError,
    aio_timeout,
)

NO_RETRY_CONFIG = RetryConfig(
    max_attempts=1,
    max_age=None,
    exceptions=(),
    delay=1,
    raise_on_exception=True,
)
CPU_UTILIZATION_DELAY = 0.1  # seconds


class Task:
    def __init__(
        self,
        records: t.Iterable[ConsumerRecord],
        app: web.Application,
        logger: logging.Logger,
    ) -> None:
        self.app = app
        self.logger = logger
        self.finished = False
        self.stopped = False
        self.started = False
        self.records = records
        self.current_offset: int | None = None
        self.delayed_jobs: set[DelayedJob] = set()

        # current record processed by Task object
        self.current_job: asyncio.Future[t.Any] | None = None

        # coroutine within which all Task.records will be processed
        # (bound to Task.instance to avoid GC destroys long-running tasks)
        self.coroutine: asyncio.Future[t.Any] | None = None

    def run(self) -> None:
        self.coroutine = asyncio.create_task(self.run_async())

    async def run_async(self) -> None:
        if self.stopped:
            return

        self.started = True

        for record in self.records:
            if self.stopped:
                break  # type: ignore

            # process records from same partitions sequentially
            self.current_job = asyncio.create_task(self._process_record(record))
            await self.current_job

            self.current_offset = record.offset + 1

        self.finished = True

    async def _process_record(self, record: ConsumerRecord) -> None:
        topic = record.topic
        data = record.value
        job_hash = str(
            hash(
                (
                    topic,
                    record.partition,
                    record.offset,
                    record.timestamp,
                )
            )
        )

        # Get rxid from data or generate new uuid for rxid
        rxid = data.pop('rxid', str(uuid.uuid4()))
        with set_current_rxid(rxid):
            self.logger.info(
                'Record consumed',
                extra={
                    'partition': record.partition,
                    'offset': record.offset,
                    'job_hash': job_hash,
                    'topic_key': record.key,
                    'topic': topic,
                    'data': data,
                },
            )
            job = TOPIC_JOBS.get(topic)
            if not job:
                return

            start_time = time.time()

            with error_reporting.configure_scope():
                error_reporting.set_context(
                    key='consumer',
                    value={
                        'topic': topic,
                        'offset': record.offset,
                        'partition': record.partition,
                        'job_hash': job_hash,
                    },
                )
                error_reporting.set_attachment(
                    key=f'{topic}__{record.offset}.json',
                    value=data,
                )

                try:
                    with _run_job_tracking(topic=topic):
                        await self._run_job(
                            topic=topic,
                            job=job,
                            data=data,
                            job_hash=job_hash,
                        )
                except Exception as exception:
                    error_reporting.capture_exception(exception)
                    exception_time = time.time()
                    err_log_extra = getattr(exception, 'log_extra', {})
                    self.logger.exception(
                        'Worker job failed',
                        extra={
                            **err_log_extra,
                            'job_name': job.__name__,
                            'job_hash': job_hash,
                            'job_execution_time': exception_time - start_time,
                            'data': data,
                        },
                    )
                    return

    async def _run_job(
        self,
        topic: str,
        job: JobFunction,
        data: DataDict,
        job_hash: str,
    ) -> None:
        job_name = job.__name__
        job = aio_timeout(job)

        start_time = time.time()

        log_extra = {'job_name': job_name, 'data': data, 'job_hash': job_hash}
        config: RetryConfig = getattr(job, RETRY_CONFIG_KEY, NO_RETRY_CONFIG)

        def _raise_or_return(ex: BaseException) -> None:
            if config.raise_on_exception:
                raise ex
            return

        try:
            await job(self.app, data, self.logger)
        except config.exceptions as exception:
            attempt = data.get(RETRY_ATTEMPT_KEY, 0)
            if attempt + 1 >= config.max_attempts:
                self.logger.info('Max attempts is reached', extra=log_extra)
                return _raise_or_return(exception)

            job_finish = time.time()  # seconds
            job_start = data.get(RETRY_START_TIME_KEY, start_time)
            job_age = job_finish - job_start
            if config.max_age and job_age > config.max_age.total_seconds():
                self.logger.info('Max job age reached', extra=log_extra)
                return _raise_or_return(exception)

            self.delayed_jobs.add(
                DelayedJob(
                    topic=topic,
                    value={
                        **data,
                        RETRY_START_TIME_KEY: job_start,
                        RETRY_ATTEMPT_KEY: attempt + 1,
                    },
                    delay_min=config.delay,
                    log_extra=log_extra,
                    job_hash=int(job_hash),
                )
            )
            _raise_or_return(exception)

        self.logger.info(
            msg='Worker job completed',
            extra={**log_extra, 'time': time.time() - start_time},
        )
        return None

    async def stop(self) -> None:
        self.stopped = True
        if not self.started:
            self.finished = True

        if not self.current_job or self.finished:
            return

        with suppress(Exception):
            # Try to wait partition current record processing
            await self.current_job


class ConsumerWorker(ConsumerRebalanceListener):
    # Min commit topic partition offsets interval
    COMMIT_OFFSET_INTERVAL_SEC = 5

    # Run tasks from different partitions concurrently with
    # default max_number of concurrent tasks
    DEFAULT_CONCURRENCY = 1

    def __init__(
        self,
        app: web.Application,
        logger: logging.Logger,
        concurrency: int | None = None,
    ) -> None:
        self.app = app
        self.logger = logger
        self.consumer: AIOKafkaConsumer = app['kafka'].consumer
        self.active_tasks: dict[TopicPartition, Task] = {}
        self.offsets_to_commit: dict[TopicPartition, int] = {}
        self.delayed_jobs: dict[TopicPartition, set[DelayedJob]] = defaultdict(set)
        self.last_commit_time = time.time()  # seconds
        self.concurrency = concurrency or self.DEFAULT_CONCURRENCY
        self.stopped = False

        # We don't use the auto_commit feature because of
        # possibility to fetch more than one record from partition,
        # so only Task instance knows which offset is last for current partition
        # and can be committed
        self.consumer._enable_auto_commit = False

    async def run(self) -> None:
        self.consumer.subscribe(
            topics=list(TOPIC_JOBS.keys()),
            listener=self,
        )
        await self.consumer.start()

        while True:
            if self.stopped:
                return

            try:
                max_records = self.concurrency - len(self.active_tasks)
                chunk: ConsumedChunk | None = None
                if max_records == 0:
                    await asyncio.sleep(CPU_UTILIZATION_DELAY)
                else:
                    chunk = await self.consumer.getmany(max_records=max_records, timeout_ms=100)
            except KafkaError:
                self.logger.exception('Unhandled kafka error, polling consumer')
                await asyncio.sleep(60)
                continue

            self._process_chunk(chunk)
            await self._check_active_tasks()
            await self._commit_offsets()

    async def stop(self) -> None:
        self.stopped = True

        for partition, task in self.active_tasks.items():
            await task.stop()
            if task.current_offset:
                self.offsets_to_commit[partition] = task.current_offset
            self.delayed_jobs[partition] = self.delayed_jobs[partition].union(task.delayed_jobs)

        try:
            await self._commit_offsets(nowait=True)
        except Exception:
            self.logger.exception('Failed commit offsets during graceful shutdown')

    def _process_chunk(self, chunk: ConsumedChunk | None) -> None:
        if not chunk:
            return

        partitions_to_pause: set[TopicPartition] = set()

        for topic_partition, records in chunk.items():
            partitions_to_pause.add(topic_partition)

            # Run tasks from different partitions concurrently
            task = Task(records=records, app=self.app, logger=self.logger)
            task.run()
            self.active_tasks[topic_partition] = task

        if partitions_to_pause:
            # Pause consuming from partition to avoid records
            # from same partition process concurrently
            # (possible to face duplicate reads, hell with commit offsets)
            self.consumer.pause(*list(partitions_to_pause))

    async def _check_active_tasks(self) -> None:
        finished_task_partitions: set[TopicPartition] = set()
        for partition, task in self.active_tasks.items():
            if task.finished:
                finished_task_partitions.add(partition)
            if task.current_offset:
                self.offsets_to_commit[partition] = task.current_offset

            self.delayed_jobs[partition] = self.delayed_jobs[partition].union(task.delayed_jobs)

        for partition in finished_task_partitions:
            self.active_tasks.pop(partition, None)

        if finished_task_partitions:
            self.consumer.resume(*list(finished_task_partitions))

    async def _commit_offsets(self, nowait: bool = False) -> None:
        if not self.offsets_to_commit:
            return

        current_time = time.time()
        if nowait or (current_time - self.last_commit_time >= self.COMMIT_OFFSET_INTERVAL_SEC):
            try:
                await self.consumer.commit(self.offsets_to_commit)
                self.offsets_to_commit.clear()
                self.last_commit_time = current_time

                # Publish delayed jobs after successfully committed offsets
                await self._publish_delayed_jobs(deepcopy(self.delayed_jobs))
            except KafkaError:
                self.logger.exception('Failed to commit offsets')

    async def _publish_delayed_jobs(
        self, delayed_jobs: dict[TopicPartition, set[DelayedJob]]
    ) -> None:
        async with services.db.acquire() as conn:
            for partition, jobs in delayed_jobs.items():
                for job in jobs:
                    self.logger.info('Sending worker job to delay queue', extra=job.log_extra)
                    try:
                        await services.kafka.add_task(
                            conn=conn,
                            topic=job.topic,
                            delay_min=job.delay_min,
                            data=job.value,
                        )
                        self.delayed_jobs[partition].discard(job)
                    except KafkaError:
                        self.logger.exception('Failed to publish delayed job', extra=job.log_extra)

    async def on_partitions_revoked(self, revoked: list[TopicPartition]) -> None:
        """Handle consumer group re-balancing event.
        See the consumer.subscribe listener parameter
        """

        self.logger.info('Consumer group re-balance event started')

        # Stop all tasks on revoked partitions
        revoked_offsets = {}
        revoked_delayed_jobs: dict[TopicPartition, set[DelayedJob]] = defaultdict(set)
        for partition in revoked:
            stored_offset = self.offsets_to_commit.pop(partition, None)
            task = self.active_tasks.pop(partition, None)
            if task:
                await task.stop()
                if task.current_offset and stored_offset:
                    revoked_offsets[partition] = max(task.current_offset, stored_offset)
                elif task.current_offset:
                    revoked_offsets[partition] = task.current_offset
                elif stored_offset:
                    revoked_offsets[partition] = stored_offset

                revoked_delayed_jobs[partition] = task.delayed_jobs
            elif stored_offset:
                revoked_offsets[partition] = stored_offset

        # Commit offsets + publish delayed jobs for revoked partitions
        if revoked_offsets:
            try:
                await self.consumer.commit(revoked_offsets)
                await self._publish_delayed_jobs(revoked_delayed_jobs)
            except KafkaError:
                self.logger.exception(
                    'Failed to commit revoked offsets or publish '
                    'delayed_jobs from revoked partitions'
                )

    def on_partitions_assigned(self, assigned: list[TopicPartition]) -> None:
        self.consumer.resume(*assigned)


@contextmanager
def _run_job_tracking(topic: str) -> Iterator[None]:
    """
    Track the total number of started and failed job and calculate the job time
    for each topic
    """

    tracking.worker_job_started_count.labels(topic=topic).inc()

    with tracking.worker_job_duration.time(topic=topic):
        try:
            yield
        except JobTimeoutError:
            tracking.worker_job_timed_out_count.labels(topic=topic).inc()
        except Exception:
            tracking.worker_job_failed_count.labels(topic=topic).inc()
            raise

import logging

from aiohttp import web

from app.analytics.google.types import GoogleAnalyticsEvent
from app.analytics.types import GoogleAnalyticsMeta
from app.lib import types as core_types
from app.services import services


async def send_google_analytics_event_for_bill_payment(
    app: web.Application,
    data: core_types.DataDict,
    logger: logging.Logger,
) -> None:
    """
    Send google analytics event by using measurement protocol
    """
    if not services.google_analytics_client:
        logger.info('Google analytics client is not initialised.')
        return

    await services.google_analytics_client.publish_event(
        event=GoogleAnalyticsEvent.bill_payment,
        analytics=GoogleAnalyticsMeta(
            client_id=data['client_id'],
            session_id=data['session_id'],
            session_number=data['session_number'],
        ),
    )

#!/usr/bin/env python3
# flake8: noqa

from babel.messages import frontend

from scripts.translations.constants import SUPPORTED_LOCALES


def compile_catalog(
    input_file: str,
    directory: str,
    locale: str,
    domain: str,
    use_fuzzy: bool,
) -> None:
    """
    Compile catalog, by converting from .po files to .mo files
    """
    command = frontend.compile_catalog()
    command.initialize_options()
    command.use_fuzzy = use_fuzzy
    command.input_file = input_file
    command.directory = directory
    command.locale = locale
    command.domain = domain
    command.finalize_options()
    command.run()
    print(f'✅ Catalog was compiled: {locale}')


def main() -> None:
    """Compile translation catalog .po to binary catalog .mo"""
    print('⌛ Compile catalogs ...')

    for locale in SUPPORTED_LOCALES:
        compile_catalog(
            input_file=f'./translations/{locale}/LC_MESSAGES/messages.po',
            directory=f'./translations',
            domain='messages',
            locale=locale,
            use_fuzzy=True,
        )


if __name__ == '__main__':
    main()

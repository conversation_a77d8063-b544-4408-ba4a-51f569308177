#!/bin/bash

set -euf -o pipefail

# Define list of supported languages
# WARN: do not put commas between list items
supportedLocales=("en" "pl" "ro")

for locale in ${supportedLocales[@]}; do
    echo "Updating catalog: ${locale}"

    catalogFilename="cs/i18n/${locale}.po"

    # Initialize catalog for new languages
    if [[ ! -f $catalogFilename ]]
    then
        echo "Creating catalog for new language: ${locale}"
        ./node_modules/ttag-cli/bin/ttag init \
            $locale \
            $catalogFilename
    fi

    # Extract messages and update catalog
    ./node_modules/ttag-cli/bin/ttag update \
        $catalogFilename \
        cs/pages \
        cs/components \
        cs/services/navigation-structure.js \
        cs/services/documents/ts/constants.ts \
        cs/services/documentFields \
        cs/services/documents/utils.js \
        cs/lib/utilsLocalisation.js \
        cs/lib/date_old.js \
        cs/lib/helpers.js \
        cs/lib/i18n/utils.ts \
        cs/lib/commonOptions.ts \
        --lang=uk \
        --extractLocation=file
done

echo "Done!"

#!/usr/bin/env python3
# flake8: noqa

from pathlib import Path

from scripts.translations.update_catalog import get_updated_catalog, extract_messages
from scripts.translations.utils import read_catalog, delete_on_exit, write_catalog
from scripts.translations.constants import SUPPORTED_LOCALES


def check_catalog(
    template_file: str,
    catalog_file: str,
    locale: str,
    domain: str,
) -> None:
    """Check if catalog is updated"""

    catalog_filename = Path(catalog_file)
    template_filename = Path(template_file)

    catalog = get_updated_catalog(
        template_filename=template_filename,
        catalog_filename=catalog_filename,
        locale=locale,
        domain=domain,
    )

    temp_filename = catalog_filename.parent / f'temp_{catalog_filename.name}'

    with delete_on_exit(temp_filename):
        # write and read catalog to temporary filename
        write_catalog(filename=temp_filename, catalog=catalog)
        catalog = read_catalog(
            filename=temp_filename,
            locale=locale,
            domain=domain,
        )

        original_catalog = read_catalog(
            filename=catalog_filename,
            locale=locale,
            domain=domain,
        )

        is_identical = catalog.is_identical(original_catalog)
        if not is_identical:
            print(f'⛔ The catalog should be updated ({catalog_filename})')
            exit(1)

        print(f'✅ The catalog is up to date ({catalog_filename})')
        exit(0)


def main() -> None:
    """
    Extract messages from .py files and check if catalog is updated to extracted
    template
    """
    print('⌛ Check catalogs ...')

    extract_messages(
        mapping_file='./translations/mapping.ini',
        output_file='./translations/messages.pot',
        input_dirs='.',
    )

    for locale in SUPPORTED_LOCALES:
        check_catalog(
            template_file='./translations/messages.pot',
            catalog_file=f'./translations/{locale}/LC_MESSAGES/messages.po',
            domain='messages',
            locale=locale,
        )


if __name__ == '__main__':
    main()

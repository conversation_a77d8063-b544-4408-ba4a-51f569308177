#!/usr/bin/env python3
# flake8: noqa

from __future__ import annotations
from dataclasses import dataclass, field
from pathlib import Path

from babel.messages import Message

from scripts.translations.constants import SUPPORTED_LOCALES
from scripts.translations.utils import read_catalog


@dataclass
class TranslationStat:
    translated: int = 0
    not_translated: int = 0
    total: int = 0

    @property
    def is_all_translated(self) -> bool:
        return self.total == self.translated

    @property
    def translated_percent(self) -> float:
        return (self.translated / self.total) * 100

    def add(self, message: Message) -> None:
        self.total += 1

        if message.string:
            self.translated += 1
        else:
            self.not_translated += 1

    def merge(self, stat: TranslationStat) -> None:
        self.total += stat.total
        self.translated += stat.translated
        self.not_translated += stat.not_translated

    def report(self, filename: Path) -> None:
        if self.is_all_translated:
            print(f'✅ All messages has translation ({filename})')
        else:
            print(f"⚠️ Some messages doesn't have translation ({filename}):")
            print(f' - total: {self.total}')
            print(f' - translated: {self.translated} ({self.translated_percent:.2f}%)')
            print(f' - not translated: {self.not_translated}')


@dataclass
class TranslationReport:
    stat: TranslationStat = field(default_factory=TranslationStat)

    def check(
        self,
        catalog_file: str,
        locale: str,
        domain: str,
    ) -> None:
        """Check if catalog is updated"""
        catalog_filename = Path(catalog_file)

        catalog = read_catalog(
            filename=catalog_filename,
            locale=locale,
            domain=domain,
        )

        stat = TranslationStat()
        message: Message
        for message in catalog:
            # skip header
            if message.id == '':
                continue

            stat.add(message=message)

        stat.report(filename=catalog_filename)
        self.stat.merge(stat)

    @property
    def is_all_translated(self) -> bool:
        return self.stat.is_all_translated


def main() -> None:
    """Check how much messages of the catalog have translations"""
    print('⌛ Check translation coverage ...')

    report = TranslationReport()
    for locale in SUPPORTED_LOCALES:
        report.check(
            catalog_file=f'./translations/{locale}/LC_MESSAGES/messages.po',
            domain='messages',
            locale=locale,
        )
        report.check(
            catalog_file=f'./cs/i18n/{locale}.po',
            domain=locale,
            locale=locale,
        )
    if not report.is_all_translated:
        exit(1)


if __name__ == '__main__':
    main()

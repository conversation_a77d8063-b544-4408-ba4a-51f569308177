#!/usr/bin/env python3
# flake8: noqa

from datetime import datetime
from pathlib import Path
from typing import Optional

from babel.messages import frontend, Catalog

from app.lib.datetime_utils import (
    optional_parse_raw_iso_datetime,
    parse_raw_iso_datetime,
    parse_utc_datetime,
)
from scripts.translations.utils import (
    read_template,
    read_catalog,
    write_catalog,
)
from scripts.translations.constants import SUPPORTED_LOCALES


def extract_messages(
    mapping_file: str,
    output_file: str,
    input_dirs: str,
    sort_output: bool = True,
    add_location: str = 'file',
) -> None:
    """
    Extract messages from .py, .jinja2 files and save to catalog template '.pot'
    """
    command = frontend.extract_messages()
    command.initialize_options()
    command.sort_output = sort_output
    command.add_location = add_location
    command.mapping_file = mapping_file
    command.output_file = output_file
    command.input_dirs = input_dirs
    command.finalize_options()
    command.run()
    print(f'✅ Messages ware extracted')


def init_catalog(
    template_file: str,
    catalog_file: str,
    locale: str,
    domain: str,
    width: Optional[int] = None,
    no_wrap: bool = True,
) -> None:
    """
    Initialize new catalog by reading template file
    """
    command = frontend.init_catalog()
    command.initialize_options()
    command.domain = domain
    command.input_file = template_file
    command.output_file = catalog_file
    command.locale = locale
    command.width = width
    command.no_wrap = no_wrap
    command.finalize_options()
    command.run()
    print(f'✅ New catalog was initialized: {locale}')


def get_updated_catalog(
    template_filename: Path,
    catalog_filename: Path,
    locale: str,
    domain: str,
    no_fuzzy_matching: bool = True,
    update_header_comment: bool = False,
) -> Catalog:
    """Read catalog and template and created updated version of catalog"""
    template = read_template(filename=template_filename)
    catalog = read_catalog(filename=catalog_filename, locale=locale, domain=domain)

    catalog.update(
        template=template,
        no_fuzzy_matching=no_fuzzy_matching,
        update_header_comment=update_header_comment,
    )

    # set creation date to some fixed time, to avoid rewrite for every update
    catalog.creation_date = parse_utc_datetime('2000-01-01 00:00')

    return catalog


def update_catalog(
    template_file: str,
    catalog_file: str,
    locale: str,
    domain: str,
) -> None:
    """Update catalog from template"""

    catalog_filename = Path(catalog_file)
    template_filename = Path(template_file)

    # Create catalog if not exists for new languages
    if not catalog_filename.exists():
        init_catalog(
            template_file=template_file,
            catalog_file=catalog_file,
            domain=domain,
            locale=locale,
        )

    catalog = get_updated_catalog(
        template_filename=template_filename,
        catalog_filename=catalog_filename,
        locale=locale,
        domain=domain,
    )

    write_catalog(filename=catalog_filename, catalog=catalog)
    print(f'✅ The catalog was updated! ({catalog_filename})')


def main() -> None:
    print('⌛ Updating catalogs ...')

    extract_messages(
        mapping_file='./translations/mapping.ini',
        output_file='./translations/messages.pot',
        input_dirs='.',
    )

    for locale in SUPPORTED_LOCALES:
        update_catalog(
            template_file='./translations/messages.pot',
            catalog_file=f'./translations/{locale}/LC_MESSAGES/messages.po',
            domain='messages',
            locale=locale,
        )


if __name__ == '__main__':
    main()

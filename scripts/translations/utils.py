# flake8: noqa

from contextlib import contextmanager
from pathlib import Path
from typing import Iterator

from babel.messages import Catalog
from babel.messages.pofile import read_po, write_po


def read_template(filename: Path) -> Catalog:
    with filename.open(mode='rb') as file:
        return read_po(file)


def read_catalog(filename: Path, locale: str, domain: str) -> Catalog:
    with filename.open(mode='rb') as file:
        return read_po(file, locale=locale, domain=domain)


def write_catalog(filename: Path, catalog: Catalog) -> None:
    with filename.open(mode='wb') as temp:
        write_po(fileobj=temp, catalog=catalog, width=-1)


@contextmanager
def delete_on_exit(filename: Path) -> Iterator[None]:
    try:
        yield
    finally:
        filename.unlink(missing_ok=True)

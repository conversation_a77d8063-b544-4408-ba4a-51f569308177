#!/usr/bin/env bash

# This tells bash that it should exit the script if any statement returns a
# non-true return value. The benefit of using -e is that it prevents errors
# snowballing into serious issues when they could have been caught earlier.
set -euf -o pipefail

setup_test_db_copies () {
  # Setup database copies for testing in parallel

  local workers_count=$1
  # Postgres connection parameters
  local postgres_host=$2
  local postgres_port=$3
  local postgres_user=$4
  local postgres_password=$5
  # Name of the database to copy. This database should be prepared, e.g. have
  # migrations applied
  local source_db=$6
  # Name of the target database
  local target_db_name=$7

  # Each pytest worker gets its own database
  for PYTEST_WORKER_ID in `seq 1 $(($workers_count - 1))`; do
    local target_db="${target_db_name}${PYTEST_WORKER_ID}"

    echo "Copying ${source_db} to ${target_db}..."
    PGPASSWORD=$postgres_password \
      createdb -h $postgres_host -p $postgres_port -U $postgres_user -T $source_db $target_db &
  done
}

# Main function for setup test env
init () {
    local processing_units_number=$1

    /work/scripts/wait-for-it.sh -t 0 postgres-test:5432 -- echo "Postgres started"

    echo "--- Create and migrate databases"
    start_migrations=`date +%s`

    alembic -n alembic:vchasno-docker-test -x db_num=0 upgrade head
    echo "Migrated main app DB"

    echo "Migrating Events DB"
    alembic --config ./app/events/alembic.ini -n alembic:vchasno-docker-test -x db_num=0 upgrade head
    echo "Migrated Events DB"

    if [[ $processing_units_number > 1 ]];
    then
        POSTGRES_HOST=postgres-test

        # The source DB has number 0
        source_db="${POSTGRES_DB}0"

        # Setup copies for the Main DB
        setup_test_db_copies $processing_units_number $POSTGRES_HOST 5432 $POSTGRES_USER $POSTGRES_PASSWORD $source_db $POSTGRES_DB

        # Setup copies for the Events DB
        events_source_db="${EVENTS_PG_DB}0"
        setup_test_db_copies $processing_units_number $POSTGRES_HOST 5432 $POSTGRES_USER $POSTGRES_PASSWORD $events_source_db $EVENTS_PG_DB
    fi

    wait

    end_migrations=`date +%s`
    echo Migrate DBs time: $((end_migrations-start_migrations))s

    /work/scripts/wait-for-it.sh -t 0 elasticsearch-test:9200 -- echo "Elastic started"

    echo "--- ES. create test indexes"
    python -m scripts.elastic index create --num=$processing_units_number --no-reindex --refresh-interval=100

    echo "--- mock static files"
    mkdir -p /work/static/html
    for name in agreement app auth bill_generation canceled_subscription download_new_browser error image_viewer invalid_ip invalid_token landing offer pdf_viewer privacy rates txt_xml_viewer use_of_bonuses; do
      echo -n > /work/static/html/$name.html
    done

    echo "--- clean pycache"
    find . -name __pycache__ -execdir rm -rf {} +

    echo "--- compile translations"
    /work/scripts/translations/compile_catalog.py
}

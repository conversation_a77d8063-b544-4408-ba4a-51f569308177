#!/bin/bash
# Original Copyright <PERSON> 2023
set -e

S3CMD_RE='.*\.(js|css|html|json|xml|rss|ico|svg|txt|map)$'


_s3cmd_for_sync_to_host() {
  START=$(date +%s.%N)
  echo ">> Sync with <PERSON><PERSON> started..."

  s3cmd \
    -v \
    --add-header=Cache-Control:public,max-age=31536000,immutable \
    --default-mime-type=binary/octet-stream \
    --no-delete-removed \
    --no-encrypt \
    --guess-mime-type \
    --no-mime-magic \
    --access_key="$ACCESS_KEY" \
    --secret_key="$SECRET_KEY" \
    --signature-v2 \
    --acl-public \
    --host="https://s3.amazonaws.com" \
    --host-bucket="https://%(bucket)s.s3.amazonaws.com" $@
  END=$(date +%s.%N)
  DIFF=$(echo "$END - $START" | bc)
  echo ">> Sync with AWS executed in $DIFF"
}

sync_brotli() {
  DIR=$1
  BUCKET=$2

  echo "> sync brotli ..."
  START=$(date +%s.%N)
  _s3cmd_for_sync_to_host \
    --exclude='*' \
    --rinclude='.*br-encoded-.*' \
    --add-header=content-encoding:br \
    --no-check-md5 \
    sync "$DIR/" "s3://$BUCKET"
  END=$(date +%s.%N)
  DIFF=$(echo "$END - $START" | bc)
  echo "> sync brotli executed in $DIFF"
}

sync_gzip() {
  DIR=$1
  BUCKET=$2

  echo "> sync gzip ..."
  START=$(date +%s.%N)
  _s3cmd_for_sync_to_host \
    --exclude='*' \
    --rinclude="$S3CMD_RE" \
    --add-header=content-encoding:gzip \
    --no-check-md5 \
    sync "$DIR/" "s3://$BUCKET"
  END=$(date +%s.%N)
  DIFF=$(echo "$END - $START" | bc)
  echo "> sync gzip executed in $DIFF"
}

sync_others() {
  DIR=$1
  BUCKET=$2

  SKIP_EXISTING="--skip-existing"
  if [ ! -z "$FORCE_SYNC" ]; then
    echo ">> use force sync"
    SKIP_EXISTING=""
  fi

  echo "> sync others ..."
  START=$(date +%s.%N)
  _s3cmd_for_sync_to_host \
    --rexclude="$S3CMD_RE" \
    $SKIP_EXISTING \
    sync "$DIR/" "s3://$BUCKET"
  END=$(date +%s.%N)
  DIFF=$(echo "$END - $START" | bc)
  echo "> sync others executed in $DIFF"
}

sync_all() {
  sync_brotli_code=0
  exit_code=0

  if [[ -z "${NO_BROTLI}" ]]
  then
    sync_brotli "$BROTLI_TMP" "$BUCKET"
    sync_brotli_code=$?
  fi

  sync_gzip "$TMP" "$BUCKET"
  sync_gzip_code=$?

  sync_others "$TMP" "$BUCKET"
  sync_others_code=$?

  if [[ $((sync_brotli_code + sync_gzip_code + sync_others_code)) -ne 0 ]]
  then
    echo "> sync error"
    exit_code=1
  fi

  return $exit_code
}

sync_all_parallel() {
  if [[ -z "${NO_BROTLI}" ]]
    then
      sync_brotli "$BROTLI_TMP" "$BUCKET" \
      > >(sed "s/^/[SYNC_BROTLI] /") \
      2> >(sed "s/^/[SYNC_BROTLI] /" >&2) \
      & sync_brotli_id=$!
    fi

    sync_gzip "$TMP" "$BUCKET" \
    > >(sed "s/^/[SYNC_GZIP]   /") \
    2> >(sed "s/^/[SYNC_GZIP]   /" >&2) \
    & sync_gzip_id=$!

    sync_others "$TMP" "$BUCKET" \
    > >(sed "s/^/[SYNC_OTHERS] /") \
    2> >(sed "s/^/[SYNC_OTHERS] /" >&2) \
    & sync_others_id=$!

    exit_code=0

    sync_brotli_code=0
    if [[ -z "${NO_BROTLI}" ]]
    then
      wait $sync_brotli_id
      sync_brotli_code=$?
    fi

    wait $sync_gzip_id
    sync_gzip_code=$?

    wait $sync_others_id
    sync_others_code=$?

    if [[ $((sync_brotli_code + sync_gzip_code + sync_others_code)) -ne 0 ]]
    then
      echo "> sync error"
      exit_code=1
    fi

    return $exit_code
}

compress_and_sync() {
  BUCKET=$1
  DIR=$2

  FIND_RE='.*\.\(js\|css\|html\|json\|xml\|rss\|ico\|svg\|txt\|map\)'

  if [[ -z "${NO_BROTLI}" ]]
  then
    # brotli
    PARALLEL_NUM=$(( ($(nproc) - 1) > 0 ? ($(nproc) - 1) : 1 ))
    echo "> brotli compression in ${PARALLEL_NUM} parallel processes ..."
    START=$(date +%s.%N)
    BROTLI_TMP=$(mktemp -d)
    cp -r "$DIR/". "$BROTLI_TMP"
    find "$BROTLI_TMP" -type f -regex "$FIND_RE" -print0 | xargs -0 -P "${PARALLEL_NUM}" -n 1 brotli -j; # brotli compress for all FIND_RE extensions

    # change filenames to leave origin extension (for s3cmd to guess mime type)
    # this will change /file/name.js.br to /file/br-encoded-name.js
    find "$BROTLI_TMP" -type f -regex '.*br$' -exec rename 's/^(.+\/)(.+)(.br)$/$1br-encoded-$2/' {} \;

    END=$(date +%s.%N)
    DIFF=$(echo "$END - $START" | bc)
    echo "executed in $DIFF"
  fi

  # gzip
  echo "> gzip compression ..."
  START=$(date +%s.%N)
  TMP=$(mktemp -d)
  cp -r "$DIR/." "$TMP/"
  find "$TMP" -type f -regex "$FIND_RE" -exec gzip -9fn {} \; -exec mv {}.gz {} \; # inplace compression
  END=$(date +%s.%N)
  DIFF=$(echo "$END - $START" | bc)
  echo "executed in $DIFF"

  # sync compressed files
  echo "> sync files ..."
  exit_code=0
  START=$(date +%s.%N)
  if [[ -z "${PARALLEL_SYNC}" ]]
  then
    sync_all
    exit_code=$?
  else
    sync_all_parallel
    exit_code=$?
  fi

  END=$(date +%s.%N)
  DIFF=$(echo "$END - $START" | bc)
  echo "> sync done. executed in $DIFF"
  exit $exit_code
}

# Main function to run the script
main() {
  if [ "$1" = "" ]; then
    echo "Missing BUCKET argument"
    exit 1
  fi

  if [ "$2" = "" ]; then
    echo "Missing DIR argument"
    exit 1
  fi

  BUCKET=$1
  DIR=$2

  compress_and_sync "$BUCKET" "$DIR"
}

main "$@"

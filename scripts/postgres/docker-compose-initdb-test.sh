#!/usr/bin/env bash

start=`date +%s`

DBNUM=0
db=$POSTGRES_DB$DBNUM
db_events=$POSTGRES_DB_EVENTS$DBNUM

echo create $db
createdb -U $POSTGRES_USER $db
createdb -U $POSTGRES_USER $db_events

psql -U $POSTGRES_USER -d $db -c 'CREATE EXTENSION "citext";'
psql -U $POSTGRES_USER -d $db -c 'CREATE EXTENSION "uuid-ossp";'
psql -U $POSTGRES_USER -d $db -c 'CREATE EXTENSION "pg_trgm";'

psql -U $POSTGRES_USER -d $db_events -c 'CREATE EXTENSION "citext";'
psql -U $POSTGRES_USER -d $db_events -c 'CREATE EXTENSION "uuid-ossp";'
psql -U $POSTGRES_USER -d $db_events -c 'CREATE EXTENSION "pg_trgm";'

end=`date +%s`
echo Init DB time: $((end-start))s

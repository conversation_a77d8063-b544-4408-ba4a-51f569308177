from app import signals
from app.config import read_app_config
from app.services import services


async def setup_config() -> None:
    config = read_app_config()
    services.ctx_config.set(config)


async def connect_elasticsearch() -> None:
    if not services.config.es:
        raise ValueError('Elasticsearch config is not set')

    es = signals.init_elasticsearch_client(
        es_conf=services.config.es,
        request_timeout=services.config.app.request_timeout,
    )
    services.ctx_es.set(es)


async def connect_kafka() -> None:
    if not services.config.kafka:
        raise ValueError('Kafka config is not set')

    kafka = await signals.init_kafka_client(kafka_config=services.config.kafka)
    services.ctx_kafka.set(kafka)  # type: ignore


async def connect_feature_flags() -> None:
    client, manager = await signals.connect_feature_flags(config=services.config.feature_flags_v2)
    services.ctx_ff_client.set(client)
    services.ctx_ff_manager.set(manager)


async def disconnect_feature_flags() -> None:
    manager = services.ff_manager.get(None)
    if manager:
        manager.close()
        await manager.wait_closed()


async def disconnect_elasticsearch() -> None:
    es = services.ctx_es.get(None)
    if es:
        await es.cluster.client.close()


async def disconnect_kafka() -> None:
    kafka = services.ctx_kafka.get(None)
    if kafka:
        await kafka.producer.stop()
        await kafka.consumer.stop()

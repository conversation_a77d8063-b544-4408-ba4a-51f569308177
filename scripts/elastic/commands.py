import logging

from app.config import get_level
from app.es.models.comment import Comment
from app.es.models.contact_recipient import ContactRecipient
from app.es.models.document import Document
from app.flags.managers import FileFlagsManager
from app.flags.utils import get_flag
from app.services import services
from scripts.elastic import utils
from scripts.elastic.constants import (
    CLUSTER_CONFIG,
    COMMENT_INDEX_CONFIG,
    CONTACT_RECIPIENT_INDEX_CONFIG,
    DOCUMENT_INDEX_CONFIG,
    EXPECTED_FEATURE_FLAGS,
    Indexes,
)

logger = logging.getLogger(__name__)

APP_LEVEL = get_level()


def _render_test_index_name(index_name: str, num: int) -> str:
    """
    Render index name for tests. By default, test config has index name like "documents_0",
    and we should replace it with "documents_1", "documents_2", etc.
    """
    index_name = index_name.removesuffix('_0')
    return f'{index_name}_{num}'


async def _recreate_documents_index(
    num: int | None = None, refresh_interval: int | None = None
) -> None:
    if num is not None:
        # for tests, we can create multiple indexes by adding suffix
        # e.g. documents_0, documents_1, documents_2
        for i in range(num):
            index_name = _render_test_index_name(services.es.documents.name, num=i)
            await utils.recreate_index(
                index_name=index_name,
                index_config_path=DOCUMENT_INDEX_CONFIG[APP_LEVEL],
                model=Document,
                refresh_interval=refresh_interval,
            )
        return

    await utils.recreate_index(
        index_name=services.es.documents.name,
        index_config_path=DOCUMENT_INDEX_CONFIG[APP_LEVEL],
        model=Document,
        refresh_interval=refresh_interval,
    )


async def _recreate_comments_index(
    num: int | None = None, refresh_interval: int | None = None
) -> None:
    if num is not None:
        # for tests, we can create multiple indexes by adding suffix
        # e.g. comments_0, comments_1, comments_2
        for i in range(num):
            index_name = _render_test_index_name(services.es.comments.name, num=i)
            await utils.recreate_index(
                index_name=index_name,
                index_config_path=COMMENT_INDEX_CONFIG[APP_LEVEL],
                model=Comment,
                refresh_interval=refresh_interval,
            )
        return

    await utils.recreate_index(
        index_name=services.es.comments.name,
        index_config_path=COMMENT_INDEX_CONFIG[APP_LEVEL],
        model=Comment,
        refresh_interval=refresh_interval,
    )


async def _recreate_contact_recipients_index(
    num: int | None = None, refresh_interval: int | None = None
) -> None:
    if num is not None:
        # for tests, we can create multiple indexes by adding suffix
        # e.g. contact_recipients_0, contact_recipients_1, contact_recipients_2
        for i in range(num):
            index_name = _render_test_index_name(services.es.contact_recipients.name, num=i)
            await utils.recreate_index(
                index_name=index_name,
                index_config_path=CONTACT_RECIPIENT_INDEX_CONFIG[APP_LEVEL],
                model=ContactRecipient,
                refresh_interval=refresh_interval,
            )
        return

    await utils.recreate_index(
        index_name=services.es.contact_recipients.name,
        index_config_path=CONTACT_RECIPIENT_INDEX_CONFIG[APP_LEVEL],
        model=ContactRecipient,
        refresh_interval=refresh_interval,
    )


async def create_index_command(
    indexes: list[Indexes],
    num: int | None,
    refresh_interval: int | None = None,
) -> None:
    """
    Create index with predefined settings
    """
    level = get_level()

    cluster_config_path = CLUSTER_CONFIG[level]
    await utils.update_cluster_settings(path=cluster_config_path)

    if 'documents' in indexes:
        await _recreate_documents_index(num=num, refresh_interval=refresh_interval)

    if 'comments' in indexes:
        await _recreate_comments_index(num=num, refresh_interval=refresh_interval)

    if 'contact_recipients' in indexes:
        await _recreate_contact_recipients_index(num=num, refresh_interval=refresh_interval)


async def reindex_command(indexes: list[Indexes]) -> None:
    """
    Reindex whole index
    """

    if 'documents' in indexes:
        await utils.reindex_documents()

    if 'comments' in indexes:
        await utils.reindex_comments()

    if 'contact_recipients' in indexes:
        await utils.reindex_contact_recipients()


async def delete_index_command(indexes: list[Indexes]) -> None:
    """
    Delete index
    """
    if 'documents' in indexes:
        await utils.delete_index(services.es.documents.name)

    if 'comments' in indexes:
        await utils.delete_index(services.es.comments.name)

    if 'contact_recipients' in indexes:
        await utils.delete_index(services.es.contact_recipients.name)


async def check_expected_flags_command() -> None:
    """
    Check feature flags
    """
    is_invalid = False
    for flag, expected in EXPECTED_FEATURE_FLAGS.items():
        actual = get_flag(flag)
        if actual != expected:
            is_invalid = True
            logger.warning(
                'Some feature flags are not set as expected',
                extra={'flag': flag, 'expected': expected, 'actual': actual},
            )

    if not is_invalid:
        logger.info('Feature flags are set as expected')


async def set_expected_flags_command() -> None:
    """
    Set feature flags
    """
    manager = services.ff_manager
    if not isinstance(manager, FileFlagsManager):
        raise ValueError('Only FileFlagsManager is supported for this operation')

    expected = {flag.value: value for flag, value in EXPECTED_FEATURE_FLAGS.items()}
    manager.update(expected)
    logger.info('Feature flags have been set to expected values')

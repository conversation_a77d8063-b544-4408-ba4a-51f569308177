import argparse
import asyncio
import logging

import elasticsearch

# elasticmagic refers to BaseException, but it's changed in recent lib version
if not getattr(elasticsearch, 'ElasticsearchException', None):
    setattr(elasticsearch, 'ElasticsearchException', elasticsearch.ApiError)  # noqa

import logevo

from scripts.elastic import commands, connect, constants

logger = logging.getLogger(__name__)


EPILOG = """
Examples:

    # Create all from scratch & reindex all
    python -m scripts.elastic index create

    # Create only specific index & reindex it
    python -m scripts.elastic index create --index=documents

    # Create only specific index & don't reindex it
    python -m scripts.elastic index create --index=documents --no-reindex

    # Reindex all indexes
    python -m scripts.elastic index reindex

    # Reindex only specific index
    python -m scripts.elastic index reindex --index=documents

    # Delete all indexes
    python -m scripts.elastic index delete

    # Delete only specific index
    python -m scripts.elastic index delete --index=documents

    # To create 10 indexes (for example, for tests)
    python -m scripts.elastic index create --num=10

Happy hacking!

"""


async def cli() -> None:
    # Create the top-level parser
    parser = argparse.ArgumentParser(
        description='Index management utility.',
        epilog=EPILOG,
        formatter_class=argparse.RawTextHelpFormatter,
    )
    subparsers = parser.add_subparsers(dest='command', required=True)

    # Create the parser for the "index" command
    index_parser = subparsers.add_parser(
        name='index',
        help='Index operations',
    )
    index_subparsers = index_parser.add_subparsers(
        dest='index_command',
        required=True,
    )

    create_parser = index_subparsers.add_parser(
        name='create',
        help='Create index',
    )
    create_parser.add_argument(
        '--no-reindex',
        action='store_true',
        help='Whether to reindex',
    )
    create_parser.add_argument(
        '--index',
        type=str,
        required=False,
        choices=[*constants.INDEXES, None],
        help='Index name',
    )
    create_parser.add_argument(
        '--num',
        type=int,
        required=False,
        default=None,
        help='Number of indexes to create',
    )
    create_parser.add_argument(
        '--refresh-interval',
        type=int,
        required=False,
        default=None,
        help='Index refresh interval in milliseconds',
    )

    reindex_parser = index_subparsers.add_parser(
        name='reindex',
        help='Reindex data',
    )
    reindex_parser.add_argument(
        '--index',
        type=str,
        required=False,
        choices=[*constants.INDEXES, None],
        help='Index name',
    )

    delete_parser = index_subparsers.add_parser(
        name='delete',
        help='Delete index',
    )
    delete_parser.add_argument(
        '--index',
        type=str,
        required=False,
        choices=[*constants.INDEXES, None],
        help='Index name',
    )

    # Create the parser for the "flags" command
    flags_parser = subparsers.add_parser(
        name='flags',
        help='Feature flags operations',
    )
    flags_subparsers = flags_parser.add_subparsers(
        dest='flags_command',
        required=True,
    )

    flags_subparsers.add_parser(
        name='set-expected',
        help='Set expected flags',
    )
    flags_subparsers.add_parser(
        name='check-expected',
        help='Check expected flags',
    )

    # Parse the arguments
    args = parser.parse_args()

    # required resources for all commands
    try:
        await connect.setup_config()
        await connect.connect_elasticsearch()
        await connect.connect_feature_flags()

        # Implement your command handling logic here
        if args.command == 'index':
            indexes = [args.index] if args.index else constants.INDEXES

            if args.index_command == 'create':
                await commands.create_index_command(
                    indexes=indexes, num=args.num, refresh_interval=args.refresh_interval
                )

                reindex = args.no_reindex is not True
                if reindex:
                    await connect.connect_kafka()
                    await commands.reindex_command(indexes=indexes)

            elif args.index_command == 'reindex':
                await connect.connect_kafka()
                await commands.reindex_command(indexes=indexes)

            elif args.index_command == 'delete':
                await commands.delete_index_command(indexes=indexes)

        elif args.command == 'flags':
            if args.flags_command == 'set-expected':
                await commands.set_expected_flags_command()
            if args.flags_command == 'check-expected':
                await commands.check_expected_flags_command()

    finally:
        await connect.disconnect_feature_flags()
        await connect.disconnect_elasticsearch()
        await connect.disconnect_kafka()


if __name__ == '__main__':
    with logevo.main_context():
        logging.getLogger('elasticsearch').setLevel('ERROR')
        asyncio.run(cli())

from typing import Literal

from app.flags import FeatureFlags
from app.lib.enums import AppLevel

Indexes = Literal['documents', 'comments', 'contact_recipients']
INDEXES: list[Indexes] = ['comments', 'documents', 'contact_recipients']

CLUSTER_CONFIG: dict[AppLevel, str] = {
    AppLevel.test: 'config/elastic/cluster_settings.local.json',
    AppLevel.local: 'config/elastic/cluster_settings.local.json',
}
DOCUMENT_INDEX_CONFIG: dict[AppLevel, str] = {
    AppLevel.test: 'config/elastic/indexes/documents.local.json',
    AppLevel.local: 'config/elastic/indexes/documents.local.json',
}
COMMENT_INDEX_CONFIG: dict[AppLevel, str] = {
    AppLevel.test: 'config/elastic/indexes/comments.local.json',
    AppLevel.local: 'config/elastic/indexes/comments.local.json',
}
CONTACT_RECIPIENT_INDEX_CONFIG: dict[AppLevel, str] = {
    AppLevel.test: 'config/elastic/indexes/contact_recipients.local.json',
    AppLevel.local: 'config/elastic/indexes/contact_recipients.local.json',
}


EXPECTED_FEATURE_FLAGS: dict[FeatureFlags, bool] = {
    # Documents
    FeatureFlags.ES_SEARCH: True,
    FeatureFlags.ES_SEARCH_API: True,
    FeatureFlags.ES_DISABLE_REINDEXATION_JOB: False,
    # Contacts
    FeatureFlags.DISABLE_CONTACTS_INDEXATION: False,
    # Contact recipients
    FeatureFlags.ENABLE_CONTACT_RECIPIENT_INDEX: True,
    FeatureFlags.DISABLE_CONTACT_RECIPIENT_REINDEX: False,
}

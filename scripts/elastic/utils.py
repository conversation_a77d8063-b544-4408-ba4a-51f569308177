import json
import logging
import pathlib

from elasticmagic import Document as Model

from app.services import services
from worker import topics

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


async def delete_index(index_name: str) -> None:
    """
    Delete index if exists
    """
    await services.es.cluster.delete_index(index_name, ignore_unavailable=True)
    logger.info('Index has been deleted', extra={'index': index_name})


async def create_index(
    index_name: str,
    index_config_path: str,
    model: type[Model],
    refresh_interval: int | None,
) -> None:
    """
    Create index with predefined settings
    """
    cluster = services.es.cluster

    config_path = pathlib.Path(index_config_path)
    config_raw = config_path.read_text()
    config_json = json.loads(config_raw)

    # Crete index with settings + mapping in one request,
    # it is necessary to apply index.sort.* settings
    settings = config_json['settings']
    if refresh_interval:
        settings['index']['refresh_interval'] = f'{refresh_interval}ms'
    await cluster.create_index(
        index=index_name,
        settings=config_json['settings'],
        mappings=model,
    )
    logger.info('Index with mapping has been created', extra={'index': index_name})


async def recreate_index(
    index_name: str,
    index_config_path: str,
    model: type[Model],
    refresh_interval: int | None,
) -> None:
    """
    Prepare index from scratch
    """
    await delete_index(index_name=index_name)
    await create_index(
        index_name=index_name,
        index_config_path=index_config_path,
        model=model,
        refresh_interval=refresh_interval,
    )


async def reindex_comments() -> None:
    """
    Reindex whole comments index
    """
    await services.kafka.send_record(
        topics.SEND_COMMENTS_TO_INDEX,
        value={
            'date_gte': '2010-01-01T00:00:00',
            'date_lte': '2052-01-01T00:00:00',
        },
    )
    logger.info('Comments reindexation started')


async def reindex_documents() -> None:
    """
    Reindex whole documents index
    """
    await services.kafka.send_record(
        topic=topics.SEND_DOCUMENTS_TO_INDEXATOR,
        value={'excluded_edrpous': ['']},
    )
    logger.info('Documents reindexation started')


async def reindex_contact_recipients() -> None:
    """
    Reindex whole contact_recipients index
    """
    await services.kafka.send_record(
        topic=topics.REINDEX_CONTACTS_RECIPIENTS_BY_CONTACTS,
        value={'limit': 1000},
    )

    await services.kafka.send_record(
        topic=topics.REINDEX_CONTACTS_RECIPIENTS_BY_COMPANIES,
        value={'limit': 1000},
    )

    await services.kafka.send_record(
        topic=topics.REINDEX_CONTACTS_RECIPIENTS_BY_ROLES,
        value={'limit': 1000},
    )

    logger.info('Contact recipients reindexation started')


async def update_cluster_settings(path: str) -> None:
    """
    Update cluster settings
    """
    cluster_settings_raw = pathlib.Path(path).read_text()
    cluster_settings_json = json.loads(cluster_settings_raw)
    await services.es.cluster.update_cluster_settings(cluster_settings_json)
    logger.info('Cluster settings updated')

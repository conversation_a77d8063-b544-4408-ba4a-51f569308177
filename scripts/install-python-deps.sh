#!/bin/bash

# IMPORTANT: This script is to be used only for dev purposes.

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'  # No Color

# Function to ensure the container is removed on exit
cleanup() {
    echo
    echo "${BLUE}Cleaning up the container...${NC}"
    docker stop "$CONTAINER_ID" > /dev/null 2>&1
    docker rm "$CONTAINER_ID" > /dev/null 2>&1
}

trap cleanup EXIT

# 1. Spin up a container from the evodoc-tools image with bash entrypoint, keeping it running
echo "${BLUE}Spinning up a container from evodoc-tools image...${NC}"
CONTAINER_ID=$(docker run -d --rm --entrypoint /bin/bash evodoc-tools:latest -c "while true; do sleep 1000; done")

# 2. Ensure the container was successfully started
if [ -z "$CONTAINER_ID" ]; then
    echo "${RED}Error: Failed to spin up container from evodoc-tools image.${NC}"
    exit 1
fi

# 3. Get the installed Python libraries before installing new ones
INSTALLED_BEFORE=$(docker exec "$CONTAINER_ID" bash -c "pip3 freeze" | sort)

echo

# 4. Install the new Python dependencies inside the container
echo "${YELLOW}Installing python dependencies... This might take some time.${NC}"
docker exec "$CONTAINER_ID" bash -c "pip3 install --root-user-action=ignore --quiet --disable-pip-version-check -r /work/requirements/dev.txt"
if [ $? -ne 0 ]; then
    echo "${RED}Error: Failed to install python dependencies.${NC}"
    exit 1
fi

# 5. Get the installed Python libraries after installation
INSTALLED_AFTER=$(docker exec "$CONTAINER_ID" bash -c "pip3 freeze" | sort)

# Use temporary files to find newly installed packages
TEMP_BEFORE=$(mktemp)
TEMP_AFTER=$(mktemp)

# Save installed packages to temporary files
echo "$INSTALLED_BEFORE" > "$TEMP_BEFORE"
echo "$INSTALLED_AFTER" > "$TEMP_AFTER"

# Find newly installed packages
NEWLY_INSTALLED=$(comm -13 "$TEMP_BEFORE" "$TEMP_AFTER")

echo

# 6. Print the newly installed packages
if [ -n "$NEWLY_INSTALLED" ]; then
    echo "${GREEN}Freshly Installed Libraries:${NC}"
    echo "$NEWLY_INSTALLED" | while read -r pkg; do
        echo "  - $pkg"
    done
else
    echo "${RED}You're up-to-date, no new libraries were installed.${NC}"
    exit 0
fi

echo

# 7. Clean up temporary files
rm "$TEMP_BEFORE" "$TEMP_AFTER"

# 8. Commit changes to the evodoc-tools:latest image
echo "${YELLOW}Committing changes to evodoc-tools:latest...${NC}"
docker commit "$CONTAINER_ID" evodoc-tools:latest

# 9. Spin up containers from new image
echo "${BLUE}Spinning up containers...${NC}"
docker-compose up -d web

# 10. Process completed
echo "${GREEN}Process completed successfully!${NC}"

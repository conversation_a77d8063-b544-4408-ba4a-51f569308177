import lzma
import sys
import time

from contextlib import contextmanager
from typing import Iterator

from api.uploads.utils import blake2b_hash


def main(*args: str) -> int:
    args = args or sys.argv[1:]  # type: ignore
    if not args:
        print('Usage: {0} FILE ...')

    code = 0
    for filename in args:
        try:
            with open(filename, 'rb') as archive_buffer:
                with time_context(filename):
                    print(f'{filename} => {blake2b_hash(archive_buffer)}')

                if not filename.endswith('.xz'):
                    continue

                archive_buffer.seek(0)
                with time_context('content'):
                    with lzma.open(archive_buffer, 'rb') as content_buffer:
                        content = blake2b_hash(content_buffer)  # type: ignore
                        print(f'content => {content}')
        except FileNotFoundError:
            print(f'WARNING: File not found at {filename}', file=sys.stderr)
            code = 1

    return code


@contextmanager
def time_context(name: str) -> Iterator[None]:
    timer = time.time()
    yield

    delta = time.time() - timer
    print(f'{name}: {delta:.4f}s')


if __name__ == '__main__':
    sys.exit(main())

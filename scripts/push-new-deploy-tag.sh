#!/bin/bash

# Fail on errors, https://sipb.mit.edu/doc/safe-shell/
set -euf -o pipefail

# Function to get today's date in the format: YYYY.MM.DD
get_today_date() {
  date +"%Y.%m.%d"
}

# Function to get the latest tag number for today's date and increment it
get_latest_tag_number() {
  latest_tag=$(git describe --tags --match "v$(get_today_date).*" --abbrev=0 2>/dev/null || echo "v$(get_today_date).00")

  tag_number=${latest_tag##*.}
  tag_number=$((10#$tag_number + 1)) # Increment the tag number (handling leading zeros)

  printf "%02d" "$tag_number" # Pad the tag number with leading zeros if needed
}

# Fetch the latest tags from the remote repository
git fetch --tags

# Get the latest tag
latest_tag=$(git describe --tags --abbrev=0)
echo "Latest tag: $latest_tag"

# Create the new tag
new_tag="v$(get_today_date).$(get_latest_tag_number)"
echo "New tag:    $new_tag"

# Get the log of commits between the latest tag and HEAD
commit_log=$(git log --oneline --pretty=format:"%h %s | %an" "$latest_tag..HEAD")

# TODO: create one multiline message
tag_message_header="Deploy new version by $(git config user.name)"
tag_message_changelog_header="Changelog from $latest_tag to $new_tag:"

echo "Tag message:"
echo "$tag_message_header"
echo "$tag_message_changelog_header"
echo
echo "$commit_log"

echo

default_branch="master"
current_branch=$(git rev-parse --abbrev-ref HEAD)
if [ "$current_branch" != "$default_branch" ]; then
  echo "ERROR. You are not on the default branch ('$default_branch') of the current repository. Please switch to the default branch to proceed."
  exit 1
fi

# Confirm that the user wants to create and push the new tag
read -p "Do you want to create and push the new tag '$new_tag' to the remote repository? [y/N] " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  echo "Exiting without creating and pushing the new tag."
  exit 0
fi

git tag -a "$new_tag" -m "$tag_message_header
$tag_message_changelog_header

$commit_log"

git push origin "$new_tag"

echo "Created and pushed new tag: $new_tag"

# Open the Gitlab's tags page in the default browser
url="https://gitlab.vchasno.com.ua/vchasno/edo/edo/-/tags"
echo
echo "Gitlab tags page: $url"

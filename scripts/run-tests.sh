#!/usr/bin/env bash

# This tells bash that it should exit the script if any statement returns a
# non-true return value. The benefit of using -e is that it prevents errors
# snowballing into serious issues when they could have been caught earlier.
set -euf -o pipefail

source ./scripts/init-tests.sh

# Parse command-line arguments
if [ "$#" -eq 0 ]; then
  # Without parameters -> run and prepare on all processors
  PROCESSING_UNITS_NUMBER=$(nproc)
elif [ "$1" = "--ci" ]; then
  # With --ci -> run and prepare on almost all processors (1 less) to avoid utilize all resources
  PROCESSING_UNITS_NUMBER=$(( ($(nproc) - 1) > 0 ? ($(nproc) - 1) : 1 ))
elif [ "$1" = "--shell" ]; then
  # With --shell -> run and prepare on all processors
  PROCESSING_UNITS_NUMBER=$(nproc)
else
  # Check for -n option
  params="$@"
  # Use regex to find '-n {n}' or '-n auto'
  regex_n='-n[ ]+([0-9]+)'
  regex_auto='-n[ ]+auto'
  if [[ $params =~ $regex_auto ]]; then
    # With -n auto -> run and prepare on all processors
    PROCESSING_UNITS_NUMBER=$(nproc)
  elif [[ $params =~ $regex_n ]]; then
    # With -n {n} -> run and prepare on N all processors
    PROCESSING_UNITS_NUMBER="${BASH_REMATCH[1]}"
  else
    # No -n option, default to 1
    PROCESSING_UNITS_NUMBER=1
  fi
fi

echo "--- PROCESSING_UNITS_NUMBER: ${PROCESSING_UNITS_NUMBER}"

# Init test env with PROCESSING_UNITS_NUMBER parallel workers
init "$PROCESSING_UNITS_NUMBER"

run_all() {
  pytest \
    -n "$PROCESSING_UNITS_NUMBER" \
    --dist=worksteal \
    --max-worker-restart=0 \
    --cov=api \
    --cov=app \
    --cov=cron \
    --cov=worker \
    --no-cov-on-fail \
    "$@" \
    ./api ./app ./cron ./worker ./indexator
}

if [ "$#" -eq 0 ]; then
  # For running tests locally
  echo "--- run all tests locally"
  run_all
elif [ "$1" = "--ci" ]; then
  # For running tests in CI (run without coverage)
  echo "--- run all tests (CI mode)"
  pytest \
  -n "$PROCESSING_UNITS_NUMBER" \
  --dist=worksteal \
  --max-worker-restart=0 \
  -m "not slow" \
  --maxfail=1 \
  ./api ./app ./cron ./worker
elif [ "$1" = "--shell" ]; then
  # Start a shell with the test environment
  echo "--- start test shell"
  PROCESSING_UNITS_NUMBER=$PROCESSING_UNITS_NUMBER bash --rcfile /work/.bashrc
else
  # Execute anything passed as args (useful for running single tests)
  echo "--- run custom command"
  pytest -n "$PROCESSING_UNITS_NUMBER" "$@"
fi

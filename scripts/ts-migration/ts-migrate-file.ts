#!/usr/bin/env ts-node
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

type MigrationLogItem = {
    originalPath: string;
    backupPath: string | null;
    newPath: string;
    date: string;
    backupDeleted?: boolean;
    renamed?: boolean;
};

const LOG_FILE = path.resolve(
    process.cwd(),
    'scripts/ts-migration/.ts-migration-log.json',
);
const LEGACY_LOG_FILE = path.resolve(
    process.cwd(),
    'scripts/.ts-migration-log.json',
);

function ensureDir(dirPath: string) {
    if (fs.existsSync(dirPath)) return;
    const parent = path.dirname(dirPath);
    if (parent && parent !== dirPath) {
        ensureDir(parent);
    }
    try {
        fs.mkdirSync(dirPath);
    } catch {
        // ignore if created by race
    }
}

function readJson<T>(filePath: string, fallback: T): T {
    if (!fs.existsSync(filePath)) return fallback;
    try {
        const raw = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(raw) as T;
    } catch {
        return fallback;
    }
}

function writeJson(filePath: string, data: unknown) {
    ensureDir(path.dirname(filePath));
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2) + '\n', 'utf8');
}

function inferTsExt(srcExt: string): '.ts' | '.tsx' {
    return srcExt === '.jsx' ? '.tsx' : '.ts';
}

function buildHeaderComment(originalRelative: string): string {
    const lines = [
        '/**',
        ` * Migrated from: ${originalRelative}`,
        ' * Notes:',
        ' * - Replace any/unknown with precise types',
        ' * - Define exports API types first',
        ' * - Run: yarn check-types && yarn js:lint',
        ' */',
        '',
    ];
    return lines.join('\n');
}

function buildMigrationBanner(): string {
    return [
        '/* MIGRATION_BANNER_START',
        ' * УВАГА: файл перебуває у процесі міграції на TypeScript.',
        ' * Будь ласка, не додавайте нового коду сюди. Усі зміни — у відповідному .ts/.tsx файлі.',
        ' * Після завершення міграції банер буде знятий.',
        ' * (керується скриптом ts:migrate:file / ts:migrate:cleanup)',
        ' MIGRATION_BANNER_END */',
        '',
    ].join('\n');
}

function convertPropTypesToInterfacePlaceholder(content: string): string {
    // Heuristic parser for simple PropTypes assignments.
    // Supports: string, number, bool, array, object, func, node, element, any
    // oneOf([...]), oneOfType([...]), arrayOf(Type), shape({ ... }), exact({ ... })
    // Falls back to unknown with TODO when uncertain.

    const assignRegex = /(\b([A-Za-z0-9_]+)\s*\.\s*propTypes\s*=\s*)(\{[\s\S]*?});?/m;
    const match = content.match(assignRegex);
    if (!match) return '';

    const componentName = match[2] || 'Component';
    const propsBody = match[3];

    // Extract key: value pairs from the object literal in a conservative way
    // Split top-level by commas, ignoring nested braces/brackets
    const body = propsBody.slice(1, -1); // remove outer { }
    const entries: Array<[string, string]> = [];
    let depth = 0;
    let current = '';
    const pushEntry = (chunk: string) => {
        const parts = chunk.split(/:\s*/);
        if (parts.length < 2) return;
        const key = parts[0].trim().replace(/^['"]|['"]$/g, '');
        const value = parts.slice(1).join(':').trim();
        entries.push([key, value]);
    };
    for (let i = 0; i < body.length; i++) {
        const ch = body[i];
        if (ch === '{' || ch === '[' || ch === '(') depth++;
        if (ch === '}' || ch === ']' || ch === ')') depth--;
        if (ch === ',' && depth === 0) {
            pushEntry(current);
            current = '';
        } else {
            current += ch;
        }
    }
    if (current.trim()) pushEntry(current);

    const toTs = (
        expr: string,
    ): { type: string; optional: boolean; notes?: string } => {
        const optional = !/\.isRequired\b/.test(expr);
        const e = expr.replace(/\.isRequired\b/g, '').trim();

        const mapSimple: Array<[RegExp, string]> = [
            [/\bPropTypes\.string\b/, 'string'],
            [/\bPropTypes\.number\b/, 'number'],
            [/\bPropTypes\.bool\b/, 'boolean'],
            [/\bPropTypes\.array\b/, 'unknown[]'],
            [/\bPropTypes\.object\b/, 'Record<string, unknown>'],
            [/\bPropTypes\.func\b/, '(...args: unknown[]) => unknown'],
            [/\bPropTypes\.node\b/, 'React.ReactNode'],
            [/\bPropTypes\.element\b/, 'React.ReactElement'],
            [/\bPropTypes\.any\b/, 'unknown'],
        ];
        for (const [re, ts] of mapSimple) {
            if (re.test(e)) return { type: ts, optional };
        }

        // PropTypes.oneOf([...]) → union of literals
        const oneOf = e.match(/PropTypes\.oneOf\((\[[\s\S]*?])\)/);
        if (oneOf) {
            const arr = oneOf[1]
                .slice(1, -1)
                .split(',')
                .map((s) => s.trim())
                .filter(Boolean)
                .map((s) =>
                    /(^['"]).*\1$/.test(s) ? s : '/* TODO: check */ ' + s,
                )
                .join(' | ');
            return { type: arr || 'unknown', optional };
        }

        // PropTypes.oneOfType([...]) → union of mapped types (fallback unknown)
        const oneOfType = e.match(/PropTypes\.oneOfType\((\[[\s\S]*?])\)/);
        if (oneOfType) {
            const inner = oneOfType[1].slice(1, -1);
            const parts = inner.split(',').map((s) => s.trim());
            const mapped = parts
                .map((p) => toTs(p).type || 'unknown')
                .join(' | ');
            return { type: mapped || 'unknown', optional };
        }

        // PropTypes.arrayOf(Type)
        const arrayOf = e.match(/PropTypes\.arrayOf\(([^)]+)\)/);
        if (arrayOf) {
            const inner = toTs(arrayOf[1]).type || 'unknown';
            return { type: `${inner}[]`, optional };
        }

        // PropTypes.shape({ ... }) / exact({ ... }) → Record<string, unknown> with TODO
        if (/PropTypes\.(shape|exact)\(/.test(e)) {
            return {
                type: 'Record<string, unknown>',
                optional,
                notes: '/* TODO: refine from PropTypes.shape */',
            };
        }

        return {
            type: 'unknown',
            optional,
            notes: '/* TODO: review unmapped propType */',
        };
    };

    const lines: string[] = [];
    lines.push(`// Derived cautiously from ${componentName}.propTypes`);
    lines.push('interface Props {');
    for (const [key, exprRaw] of entries) {
        const { type, optional, notes } = toTs(exprRaw);
        const q = optional ? '?' : '';
        lines.push(`  ${key}${q}: ${type};${notes ? ' ' + notes : ''}`);
    }
    lines.push('}');
    lines.push('');
    return lines.join('\n');
}

// eslint-disable-next-line complexity
function main() {
    // Migrate legacy log file location if needed
    try {
        if (fs.existsSync(LEGACY_LOG_FILE) && !fs.existsSync(LOG_FILE)) {
            ensureDir(path.dirname(LOG_FILE));
            fs.renameSync(LEGACY_LOG_FILE, LOG_FILE);
            console.log(
                'Info: migrated legacy log to scripts/ts-migration/.ts-migration-log.json',
            );
        }
    } catch (err) {
        console.warn(
            'Warning: failed to migrate legacy log file:',
            String(err),
        );
    }
    // Args:
    //   <file> [--convert-prop-types|-p] [--no-copy] [--rename] [--git-add]
    //   --cleanup|-c   (standalone: delete originals/backups listed in log)
    const arg = process.argv[2];
    const flags = new Set(process.argv.slice(2));
    const shouldConvertPropTypes =
        flags.has('--convert-prop-types') || flags.has('-p');
    const shouldCleanup = flags.has('--cleanup') || flags.has('-c');
    const shouldCopy = !flags.has('--no-copy');
    const shouldRename = flags.has('--rename');
    const shouldGitAdd = flags.has('--git-add');

    // Standalone cleanup mode: no file arg provided, only --cleanup flag
    if ((!arg || arg.startsWith('--')) && shouldCleanup) {
        const log: MigrationLogItem[] = readJson(
            LOG_FILE,
            [] as MigrationLogItem[],
        );
        let deletedBackups = 0;
        let deletedOriginals = 0;
        for (const item of log) {
            if (item.backupPath) {
                const absBackup = path.resolve(process.cwd(), item.backupPath);
                if (fs.existsSync(absBackup)) {
                    try {
                        fs.unlinkSync(absBackup);
                        item.backupDeleted = true;
                        deletedBackups++;
                    } catch (err) {
                        console.warn(
                            `Warning: failed to delete backup ${absBackup}: ${String(
                                err,
                            )}`,
                        );
                    }
                }
            }
            if (item.originalPath) {
                const absOriginal = path.resolve(
                    process.cwd(),
                    item.originalPath,
                );
                if (fs.existsSync(absOriginal)) {
                    try {
                        fs.unlinkSync(absOriginal);
                        deletedOriginals++;
                    } catch (err) {
                        console.warn(
                            `Warning: failed to delete original ${absOriginal}: ${String(
                                err,
                            )}`,
                        );
                    }
                }
            }
        }
        writeJson(LOG_FILE, log);

        // Auto-add all changes to git index
        try {
            execSync('git add -A', { stdio: 'pipe' });
            console.log('  Git: added all changes to index');
        } catch (err) {
            console.warn('Warning: git add -A failed:', String(err));
        }

        console.log(
            `Cleanup complete. Deleted backups: ${deletedBackups}. Deleted originals: ${deletedOriginals}.`,
        );
        return;
    }
    if (!arg) {
        console.error(
            'Usage: ts-node scripts/ts-migrate-file.ts path/to/file.{js,jsx}',
        );
        process.exit(1);
    }

    const absoluteSrc = path.resolve(process.cwd(), arg);
    if (!fs.existsSync(absoluteSrc)) {
        console.error(`File not found: ${absoluteSrc}`);
        process.exit(1);
    }

    const ext = path.extname(absoluteSrc);
    if (ext !== '.js' && ext !== '.jsx') {
        console.error('Source must be .js or .jsx');
        process.exit(1);
    }

    const srcDir = path.dirname(absoluteSrc);
    const srcBase = path.basename(absoluteSrc, ext);
    const newPath = path.join(srcDir, `${srcBase}${inferTsExt(ext)}`);

    if (fs.existsSync(newPath)) {
        console.error(`Target TS file already exists: ${newPath}`);
        process.exit(1);
    }

    const originalContent = fs.readFileSync(absoluteSrc, 'utf8');
    const relativeOriginal = path.relative(process.cwd(), absoluteSrc);

    // Create TS file with header + minimal scaffolding
    const header = buildHeaderComment(relativeOriginal);
    const propTypesPlaceholder = shouldConvertPropTypes
        ? convertPropTypesToInterfacePlaceholder(originalContent)
        : convertPropTypesToInterfacePlaceholder(originalContent) // even if off, add only if present but keep minimal
        ? '// NOTE: PropTypes detected; run with --convert-prop-types for auto draft\n'
        : '';

    const parts: string[] = [header];
    if (propTypesPlaceholder) parts.push(propTypesPlaceholder);
    if (shouldCopy) {
        parts.push('// ---- original content (auto-copied) ----');
        parts.push(originalContent);
    }
    fs.writeFileSync(newPath, parts.join('\n'), 'utf8');

    // Optionally add new TS/TSX to git index
    if (shouldGitAdd) {
        try {
            execSync(
                `git add ${JSON.stringify(
                    path.relative(process.cwd(), newPath),
                )}`,
            );
            console.log('  Git: added new TS file to index');
        } catch (err) {
            console.warn('Warning: git add failed:', String(err));
        }
    }

    // Default: keep original JS/JSX and inject banner; optional: rename to .migration
    let backupPath: string | null = null;
    if (shouldRename) {
        backupPath = path.join(srcDir, `${srcBase}.migration${ext}`);
        if (fs.existsSync(backupPath)) {
            console.error(`Backup already exists: ${backupPath}`);
            process.exit(1);
        }
        fs.renameSync(absoluteSrc, backupPath);
    } else {
        const banner = buildMigrationBanner();
        if (!originalContent.includes('MIGRATION_BANNER_START')) {
            fs.writeFileSync(absoluteSrc, banner + originalContent, 'utf8');
        }
    }

    // Append to log
    const log: MigrationLogItem[] = readJson(
        LOG_FILE,
        [] as MigrationLogItem[],
    );
    const logItem: MigrationLogItem = {
        originalPath: relativeOriginal,
        backupPath: backupPath
            ? path.relative(process.cwd(), backupPath)
            : null,
        newPath: path.relative(process.cwd(), newPath),
        date: new Date().toISOString(),
        renamed: shouldRename,
    };

    if (shouldCleanup && backupPath) {
        try {
            fs.unlinkSync(backupPath);
            logItem.backupDeleted = true;
        } catch (err) {
            console.warn(
                `Warning: failed to delete backup ${backupPath}: ${String(
                    err,
                )}`,
            );
        }
    }

    log.push(logItem);
    writeJson(LOG_FILE, log);

    console.log('Migration scaffold created:');
    console.log(`  New TS file: ${newPath}`);
    console.log(
        `  Backup:      ${backupPath ?? 'N/A (banner mode)'}${
            shouldCleanup && backupPath ? ' (deleted)' : ''
        }`,
    );
    console.log('Next steps:');
    console.log('  1) Implement types in the TS/TSX file');
    console.log('  2) yarn check-types && yarn js:lint');
    if (!shouldConvertPropTypes) {
        console.log(
            '  Tip: re-run with --convert-prop-types to draft Props from propTypes',
        );
    }
    if (!shouldCleanup) {
        console.log(
            '  Tip: use --cleanup to remove the .migration backup immediately',
        );
    }
    if (!shouldCopy) {
        console.log('  Note: source content was not copied (--no-copy)');
    }
}

main();

#!/usr/bin/env ts-node
import fs from 'fs';
import path from 'path';

type Counters = {
    total: number;
    js: number;
    ts: number;
    jsx: number;
    tsx: number;
    ignored: number;
};

const CS_DIR = path.resolve(process.cwd(), 'cs');

const DEFAULT_IGNORE = [
    '**/node_modules/**',
    '**/.git/**',
    '**/dist/**',
    '**/build/**',
    '**/.next/**',
    '**/.cache/**',
    '**/lib/vendor/**',
];

function isIgnored(filePath: string): boolean {
    const normalized = filePath.split(path.sep).join('/');
    return DEFAULT_IGNORE.some((pattern) => {
        if (pattern.endsWith('/**')) {
            const dir = pattern.replace('/**', '');
            return normalized.includes(dir.replace('**/', ''));
        }
        return normalized.includes(pattern.replace('**/', ''));
    });
}

function walk(dir: string, acc: string[] = []): string[] {
    const entries = fs.readdirSync(dir);
    for (const name of entries) {
        const full = path.join(dir, name);
        if (isIgnored(full)) continue;
        const stat = fs.statSync(full);
        if (stat.isDirectory()) {
            walk(full, acc);
        } else {
            acc.push(full);
        }
    }
    return acc;
}

function formatPercent(numerator: number, denominator: number): string {
    if (denominator === 0) return '0.00%';
    return `${((numerator / denominator) * 100).toFixed(2)}%`;
}

function main() {
    if (!fs.existsSync(CS_DIR)) {
        console.error(`Directory not found: ${CS_DIR}`);
        process.exit(1);
    }

    const files = walk(CS_DIR);

    const counters: Counters = {
        total: 0,
        js: 0,
        ts: 0,
        jsx: 0,
        tsx: 0,
        ignored: 0,
    };

    const candidatesToMigrate: string[] = [];

    for (const file of files) {
        const ext = path.extname(file);
        switch (ext) {
            case '.js':
                counters.js += 1;
                counters.total += 1;
                candidatesToMigrate.push(file);
                break;
            case '.jsx':
                counters.jsx += 1;
                counters.total += 1;
                candidatesToMigrate.push(file);
                break;
            case '.ts':
                counters.ts += 1;
                counters.total += 1;
                break;
            case '.tsx':
                counters.tsx += 1;
                counters.total += 1;
                break;
            default:
                // ignore other file types for totals
                break;
        }
    }

    const migrated = counters.ts + counters.tsx;
    const legacy = counters.js + counters.jsx;
    const percent = formatPercent(migrated, migrated + legacy);

    console.log('TS Migration Report for ./cs');
    console.log('--------------------------------');
    console.log(`JS:  ${counters.js}`);
    console.log(`JSX: ${counters.jsx}`);
    console.log(`TS:  ${counters.ts}`);
    console.log(`TSX: ${counters.tsx}`);
    console.log('--------------------------------');
    console.log(`Total considered: ${counters.total}`);
    console.log(`Migrated (TS/TSX): ${migrated}`);
    console.log(`Legacy (JS/JSX):   ${legacy}`);
    console.log(`Progress: ${percent}`);

    if (candidatesToMigrate.length) {
        console.log('\nCandidates to migrate (sorted by path):');
        for (const f of candidatesToMigrate.sort()) {
            console.log(` - ${path.relative(process.cwd(), f)}`);
        }
    }
}

main();

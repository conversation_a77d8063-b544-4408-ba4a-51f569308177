[{"originalPath": "cs/tmp-mig-test/test.js", "backupPath": "cs/tmp-mig-test/test.migration.js", "newPath": "cs/tmp-mig-test/test.ts", "date": "2025-09-12T07:38:26.777Z", "backupDeleted": true}, {"originalPath": "cs/lib/numbers.js", "backupPath": null, "newPath": "cs/lib/numbers.ts", "date": "2025-09-12T08:17:37.643Z", "renamed": false}, {"originalPath": "cs/lib/helpers.js", "backupPath": null, "newPath": "cs/lib/helpers.ts", "date": "2025-09-16T15:32:16.531Z", "renamed": false}, {"originalPath": "cs/lib/navigation.js", "backupPath": null, "newPath": "cs/lib/navigation.ts", "date": "2025-09-18T15:23:19.306Z", "renamed": false}, {"originalPath": "cs/lib/xss.js", "backupPath": null, "newPath": "cs/lib/xss.ts", "date": "2025-09-18T15:36:28.485Z", "renamed": false}, {"originalPath": "cs/lib/immutableHelpers.js", "backupPath": null, "newPath": "cs/lib/immutableHelpers.ts", "date": "2025-09-18T15:40:16.737Z", "renamed": false}, {"originalPath": "cs/lib/loadScript.js", "backupPath": null, "newPath": "cs/lib/loadScript.ts", "date": "2025-09-18T15:45:54.226Z", "renamed": false}, {"originalPath": "cs/lib/utilsLocalisation.js", "backupPath": null, "newPath": "cs/lib/utilsLocalisation.ts", "date": "2025-09-18T15:49:53.046Z", "renamed": false}]
import argparse
import base64
import uuid

import argon2

ap = argparse.ArgumentParser(description='Generate a secure token and its hash.')
ap.add_argument('--token', type=str, help='Token to hash (optional)')
args = ap.parse_args()

token = args.token or str(uuid.uuid4())
hasher = argon2.PasswordHasher()
token_hash = hasher.hash(token)
token_hash_b64 = base64.b64encode(token_hash.encode()).decode()
print('=====================================') 
print(f'Token: {token}')
print(f'Token Hash: {token_hash_b64}')

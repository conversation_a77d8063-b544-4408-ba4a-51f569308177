import datetime
import io
import logging
from http import HTTPStatus
from typing import assert_never

import sqlalchemy as sa
from aiohttp import web
from aiohttp.web_request import <PERSON><PERSON>ield
from elasticmagic import Bool
from elasticmagic.compiler import Compiler_7_0

from api.downloads.archives import (
    generate_documents_archive,
    prepare_archive_files,
)
from api.downloads.enums import ArchiveFilenamesMode
from api.downloads.types import to_document
from api.downloads.utils import stream_file_buffer
from api.errors import (
    AlreadyExists,
    DoesNotExist,
    InvalidRequest,
    Object,
    ServerError,
)
from api.private.super_admin import utils
from api.private.super_admin.constants import SURVEY_TO_REDIS_KEY
from api.private.super_admin.db import (
    insert_super_admin_action,
)
from api.private.super_admin.utils import send_email_about_blocked_user_account, write_user_log
from api.private.super_admin.validators import (
    ActivateTrialCompanyRatesSchema,
    BanUserSchema,
    BulkTokenGenerationSchema,
    ChangeEmailForUserSchema,
    ConfirmTechnicalEmailSchema,
    CreateUserSchema,
    CrmSyncRatesFiltersSchema,
    FetchBankTransactionsSchema,
    LinkCompaniesBalanceSchema,
    ManageAwsSesBlacklistSchema,
    ManageCaptchaExcludedSchema,
    Reset2FASchema,
    ResetUserPasswordSchema,
    RestoreDocumentSchema,
    SendDocumentSchema,
    SendPushNotificationToMobileAppSchema,
    SoftDeleteUserSchema,
    SurveyUserListSchema,
    UltimateUpdatePricePerUserSchema,
    UpdateAuthPhoneSchema,
    UpdatePricePerDocumentSchema,
    validate_activate_trial_companies_rates,
    validate_create_user,
    validate_delete_role,
    validate_feature_flag,
    validate_get_es_document,
    validate_manage_aws_ses_blacklist,
    validate_send_document,
    validate_update_auth_phone,
)
from api.public.decorators import api_super_admin_permission_required
from api.utils import api_response
from app.auth import concierge
from app.auth import utils as auth
from app.auth.db import (
    SELECT_ROLE_QUERY,
    batch_update_company_config,
    delete_tokens,
    insert_tokens,
    select_base_user,
    select_companies_by_edrpou,
    select_role_by_id,
    select_roles,
    update_role,
)
from app.auth.db import reset_2fa as db_reset_2fa
from app.auth.decorators import (
    super_admin_permission_required as sa_login_permission_required,
)
from app.auth.enums import RoleActivationSource, RoleStatus
from app.auth.tables import (
    company_table,
    is_active_filter,
    role_table,
    user_table,
)
from app.auth.types import (
    BaseUser,
    User,
    to_role,
)
from app.auth.utils import (
    block_account,
    update_company_by_edrpou,
    update_user,
    update_user_email_confirmed,
)
from app.auth.validators import ChangeCompanyPermissionsSchema, ChangeCorporateDomainSchema
from app.billing.db import (
    update_price_per_user_in_company_rate,
)
from app.billing.validators import (
    validate_active_ultimate_rate,
)
from app.crm.utils import send_user_to_crm
from app.document_versions.utils import get_latest_document_version_available_for_company
from app.documents import utils as documents
from app.documents.db import (
    select_documents_by_ids,
    select_listings_with_user_email,
)
from app.documents.tables import company_listing_table
from app.documents.validators import validate_document_exists
from app.es.models.document import Document
from app.events import document_actions
from app.events.types import DeleteTokenData
from app.events.user_actions import utils as user_actions_utils
from app.events.utils import (
    build_report_xls,
)
from app.flags import FeatureFlags
from app.flags.utils import get_flag
from app.i18n import _
from app.lib import (
    captcha,
    emailing,
    s3_utils,
    validators,
)
from app.lib import validators_pydantic as pv
from app.lib.datetime_utils import optional_parse_raw_iso_datetime
from app.lib.emailing import Attachment
from app.lib.enums import (
    Source,
    SuperAdminActionType,
    UserRole,
)
from app.lib.helpers import (
    csv_writer,
    get_file_extension,
    json_response,
)
from app.lib.s3_utils import DownloadFile, UploadFile
from app.lib.validators import (
    validate_json_request,
    validate_post_request,
    validate_pydantic_adapter,
)
from app.mobile.notifications import db as mobile_notifications_db
from app.models import (
    select_all,
)
from app.openapi.decorators import openapi_docs
from app.openapi.types import OpenApiParam
from app.profile.validators import (
    UpdateBillingCompanyConfigSchema,
    validate_company_by_edrpou_exists,
    validate_update_billing_company_config,
)
from app.profile.views import sa_update_company_config_common
from app.reviews.utils import update_review_statuses_in_db
from app.services import services
from app.tags.db import select_tags_by_documents
from app.templates.constants import TEMPLATE_PREVIEW_PREFIX
from app.templates.db import update_template
from app.templates.utils import (
    add_template,
    generate_template_preview,
    get_templates_s3_key,
)
from app.templates.validators import (
    UpdateTemplateSchema,
    validate_template_add,
    validate_template_delete,
    validate_template_exists,
    validate_template_update,
)
from app.youcontrol.utils import send_sync_company_info_from_youcontrol
from worker import topics

logger = logging.getLogger(__name__)


API_BILLING_TAG = 'Billing'

ALLOWED_TOPICS = [
    topics.CREATE_DOCUMENT_ACCESS_ON_ROLE_TAG,
    topics.FILL_COMPANIES_STATS,
    topics.REINDEX_DOCUMENTS,
    topics.ANTIVIRUS_CHECK,
    topics.ANTIVIRUS_CHECK_DRAFT,
    topics.SEND_COMMENTS_TO_INDEX,
    topics.REINDEX_CONTACTS_RECIPIENTS_BY_CONTACTS,
    topics.REINDEX_CONTACTS_RECIPIENTS_BY_COMPANIES,
    topics.REINDEX_CONTACTS_RECIPIENTS_BY_ROLES,
    topics.INDEX_CONTACT_RECIPIENTS,
    topics.SEND_PENDING_REVIEW_REMINDER,
    topics.SYNC_COMPANY_INFO_FROM_YOUCONTROL,
    topics.SA_ACTIVATE_COMPANIES_RATES,
    topics.SEND_REMINDERS_TO_UNREGISTERED_USERS,
    topics.SEND_DOCUMENTS_TO_INDEXATOR,
    topics.CRM_SYNC_RATES,
    topics.REINDEX_REVIEWED_DOCUMENTS,
    topics.MIGRATION_ENABLE_PHONE_AUTH,
    topics.SEND_VCHASNO_PROFILE_SYNC,
    topics.REMOVE_OLD_S3_FILES,
    topics.MIGRATE_LATEST_RECIPIENTS,
    topics.MIGRATE_SIGN_REVIEW_PERMISSION,
    topics.DELETE_REVIEW_REQUESTS_DUPLICATES,
    topics.SEND_NOTIFICATION_ABOUT_CHANGE_BASIC_FREE_RATE,
    topics.SEND_NOTIFICATION_FREE_TRIAL_IS_ENDING,
]


@openapi_docs(
    summary=_('Оновити ціну за документ для компанії'),
    request_json=UpdatePricePerDocumentSchema,
    tags=[API_BILLING_TAG],
)
@sa_login_permission_required(required_permissions={'can_edit_client_data'})
async def update_price_per_document(request: web.Request, admin: User) -> web.Response:
    """Interface to update company config for price per document"""
    from app.auth.db import update_company_config

    raw_data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(UpdatePricePerDocumentSchema, raw_data)

    company_id = valid_data.company_id
    price_per_document = valid_data.price_per_document
    async with services.db.acquire() as conn:
        await update_company_config(
            conn=conn,
            company_id=company_id,
            admin_config={
                'price_per_document': price_per_document,  # TODO: move to the billing config
            },
        )
        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.set_custom_price_for_documents,
            user=admin,
            extra_details=[{'company_id': company_id}],
        )
    return web.HTTPOk()


@openapi_docs(
    summary=_('Активувати роль в компанії'),
    params_path={
        'role_id': OpenApiParam(required=True, schema=pv.UUID),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def activate_role(request: web.Request, admin: User) -> web.Response:
    """
    Superadmin API for activating roles in any company.

    WARNING: This API doesn't have any async triggers that usually are executed on role activation.
    """

    try:
        role_id = request.match_info['role_id']
    except KeyError:
        raise InvalidRequest()

    async with services.db.acquire() as conn:
        role = await select_role_by_id(conn, role_id)
        if not role:
            raise DoesNotExist(Object.role)
        if role.status == RoleStatus.active:
            raise InvalidRequest(reason=_('Користувач вже є активним у компанії'))

        # TAG: role_activation
        await update_role(
            conn=conn,
            role_id=role_id,
            data={
                'status': RoleStatus.active,
                'activation_source': RoleActivationSource.super_admin,
                'activated_by': admin.role_id,
                'date_activated': sa.func.now(),
            },
        )

        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.activate_role,
            user=admin,
            extra_details=[{}],
        )

    return web.HTTPOk()


@openapi_docs(
    summary=_('Поставити в чергу задачу, яка синхронізує транзакції з Приватбанку'),
    request_json=FetchBankTransactionsSchema,
    tags=[API_BILLING_TAG],
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def fetch_privatbank_transactions(request: web.Request, admin: User) -> web.Response:
    """Superadmin API for fetching privatbank transactions"""

    if not get_flag(FeatureFlags.ENABLE_PRIVATBANK_PAYMENT):
        raise ServerError(reason=_('Інтеграція з Приватбанком недоступна'))

    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(FetchBankTransactionsSchema, data)

    await services.kafka.send_record(
        topic=topics.FETCH_PRIVATBANK_TRANSACTIONS,
        value=valid_data.model_dump(),
    )

    async with services.db.acquire() as conn:
        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.fetch_privatbank_transactions,
            user=admin,
            extra_details=[{}],
        )
    return web.HTTPOk()


@openapi_docs(
    summary=_('Поставити в чергу задачу, яка синхронізує транзакції з ПУМБ'),
    request_json=FetchBankTransactionsSchema,
    tags=[API_BILLING_TAG],
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def fetch_pumb_transactions(request: web.Request, admin: User) -> web.Response:
    """Superadmin API for fetching PUMB transactions"""

    if not get_flag(FeatureFlags.ENABLE_PUMB_PAYMENT):
        raise ServerError(reason=_('Інтеграція з ПУМБ недоступна'))

    data = await validators.validate_json_request(request)
    valid_data = validators.validate_pydantic(FetchBankTransactionsSchema, data)

    await services.kafka.send_record(
        topic=topics.FETCH_PUMB_TRANSACTIONS,
        value=valid_data.model_dump(),
    )

    async with services.db.acquire() as conn:
        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.fetch_pumb_transactions,
            user=admin,
            extra_details=[{}],
        )
    return web.HTTPOk()


@openapi_docs(
    summary=_('Обʼєднати баланси компанії'),
    description=_(
        'Компанія, яка задається в шляху, стає батьківською для компаній, які задаються '
        'в тілі запиту, і з її балансу будуть списуватися документи за дочірні компанії'
    ),
    params_path={
        'edrpou': OpenApiParam(required=True, schema=pv.EDRPOU),
    },
    request_json=LinkCompaniesBalanceSchema,
    tags=[API_BILLING_TAG],
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def link_companies_balance(request: web.Request, _: User) -> web.Response:
    """Api for linking companies balance"""
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(LinkCompaniesBalanceSchema, raw_data)
    parent_edrpou, edrpous = request.match_info['edrpou'], set(data.edrpous)
    async with request.app['db'].acquire() as conn:
        await validate_company_by_edrpou_exists(conn, parent_edrpou)
        companies = await select_companies_by_edrpou(
            conn=conn,
            edrpous=list(edrpous),
            selectable=[company_table.c.id, company_table.c.edrpou],
        )
        company_ids = [c.id for c in companies]
        company_edrpous = {c.edrpou for c in companies}

        if company_ids:
            await batch_update_company_config(
                conn=conn,
                company_ids=company_ids,
                admin_config={
                    'parent_company': parent_edrpou,
                    'allow_parent_company_pay_for_documents': True,
                },
            )

    response = {'data': list(company_edrpous)}
    not_found_edrpous = edrpous - company_edrpous
    if not_found_edrpous:
        response['not_found'] = list(not_found_edrpous)

    return web.json_response(response)


@openapi_docs(
    summary=_('Активувати перемикач функціоналу'),
    description=_(
        'Функціонал перемикачів не доступний, поки хтось не перевірить його статус хоча б один '
        'раз. Цей API якраз це й робить — перевіряє статус перемикача функціоналу'
    ),
    params_path={
        'flag_name': OpenApiParam(required=True, schema=str),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def activate_feature_flag(request: web.Request, user: User) -> web.Response:
    """Api for activating feature flag"""
    write_user_log(user, 'Feature flag was requested')

    flag_name = request.match_info['flag_name']
    flag = validate_feature_flag(flag_name)

    flag_value = get_flag(flag)

    return web.json_response({'name': flag_name, 'value': flag_value})


@openapi_docs(
    summary=_(
        "Змінити email користувача і оновити всі пов'язані дані, які мають посилання на цей email"
    ),
    request_json=ChangeEmailForUserSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def change_email(request: web.Request, admin: User) -> web.Response:
    raw_json = await validate_json_request(request)
    data = validators.validate_pydantic(ChangeEmailForUserSchema, raw_json)
    new_email = data.new_email

    async with services.db.acquire() as conn:
        if data.email:
            user = await select_base_user(conn=conn, email=data.email, exclude_placeholder=False)
            if not user:
                raise DoesNotExist(Object.user, email=data.email)
        elif data.user_id:
            user = await select_base_user(
                conn=conn,
                user_id=data.user_id,
                exclude_placeholder=False,
                only_with_email=False,
            )
            if not user:
                raise DoesNotExist(Object.user, user_id=data.user_id)
        else:
            raise InvalidRequest(raw_reason='Не вказано email або user_id')

        old_email: str | None = user.email

        new_email_already_exists = await select_base_user(
            conn=conn,
            email=new_email,
            exclude_placeholder=False,
        )
        if new_email_already_exists:
            raise AlreadyExists(Object.user, email=new_email)

        async with conn.begin():
            if data.update_documents and old_email:
                await utils.change_email_for_documents(
                    conn=conn,
                    old_email=old_email,
                    new_email=new_email,
                )
            if data.update_recipients and old_email:
                await utils.change_email_for_recipients(
                    conn=conn,
                    old_email=old_email,
                    new_email=new_email,
                )
            if data.update_automations and old_email:
                await utils.change_email_for_automations(
                    conn=conn,
                    old_email=old_email,
                    new_email=new_email,
                )
            if data.update_user:
                await update_user(
                    conn=conn,
                    user_id=user.id,
                    data={'email': new_email},
                )

            await insert_super_admin_action(
                conn=conn,
                action=SuperAdminActionType.change_email,
                user=admin,
                extra_details=[
                    {
                        'email': old_email,
                        'new_email': new_email,
                        'update_documents': data.update_documents,
                        'update_recipients': data.update_recipients,
                        'update_automations': data.update_automations,
                        'update_user': data.update_user,
                    }
                ],
            )

            if data.update_concierge:
                await concierge.update_user_profile(conn=conn, user_id=user.id)

        await send_user_to_crm(user_id=user.id)

    return web.Response(status=HTTPStatus.OK)


@openapi_docs(
    summary=_('Поставити задачу в чергу'),
    description=_('Дозволені топіки дивись в коді на бекенді, змінна ALLOWED_TOPICS'),
    params_path={
        'topic': OpenApiParam(required=True, schema=str),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def run_job(request: web.Request, _: User) -> web.Response:
    data = await validate_json_request(request, allow_blank=True)
    topic = request.match_info.get('topic')
    if topic not in ALLOWED_TOPICS:
        raise InvalidRequest(raw_reason='Потрібно додати топік до ALLOWED_TOPICS')

    await request.app['kafka'].send_record(topic, data or {})
    return web.json_response()


@openapi_docs(
    summary=_('Активувати тріал для компаній'),
    request_json=ActivateTrialCompanyRatesSchema,
    tags=[API_BILLING_TAG],
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def activate_trial_companies_rates(request: web.Request, user: User) -> web.Response:
    data = await validate_activate_trial_companies_rates(request)

    for edrpou in data.edrpous:
        await services.kafka.send_record(
            topic=topics.SA_ACTIVATE_COMPANIES_RATES,
            value={'edrpou': edrpou, 'days': data.days, 'role_id': user.role_id},
        )
    return web.Response(status=HTTPStatus.OK)


@openapi_docs(
    summary=_('Повернути стан документа для налагодження'),
    params_path={
        'document_id': OpenApiParam(required=True, schema=pv.UUID),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def get_document_state(request: web.Request, user: User) -> web.Response:
    document_id = request.match_info['document_id']
    async with request.app['db'].acquire() as conn:
        await insert_super_admin_action(
            conn,
            SuperAdminActionType.document_request_state,
            user,
            [{'document_id': document_id}],
        )
        documents = await select_documents_by_ids(conn, ids=[document_id])
        listings = await select_listings_with_user_email(conn, [document_id])
        tags = await select_tags_by_documents(conn, [document_id])

    if not documents:
        raise DoesNotExist(Object.document, id=document_id)

    doc = documents[0]
    await document_actions.add_document_action(
        document_action=document_actions.DocumentAction(
            action=document_actions.Action.state_request,
            document_id=document_id,
            company_id=user.company_id,
            email=user.email,
            role_id=user.role_id,
            document_edrpou_owner=doc.edrpou_owner,
            company_edrpou=user.company_edrpou,
            document_title=doc.title,
        ),
    )

    document_state = {
        'title': doc.title,
        'number': doc.number,
        'type': doc.type,
        'status': doc.status_id,
        'edrpou_owner': doc.edrpou_owner,
        'edrpou_recipient': doc.edrpou_recipient,
        'vendor': doc.vendor,
        'uploaded_role_id': doc.uploaded_by,
        'first_sign_by': doc.first_sign_by.value,
        'source': doc.source.value,
        'expected_owner_signatures': doc.expected_owner_signatures,
        'expected_recipient_signatures': doc.expected_recipient_signatures,
        'is_internal': doc.is_internal,
        'is_multilateral': doc.is_multilateral,
        'category': doc.category,
        'date_created': doc.date_created.isoformat(),
        'date_updated': doc.date_updated.isoformat(),
        'listings': [
            {
                'edrpou': access.access_edrpou,
                'role': access.role_id,
                'user_id': access.user_id,
                'user_email': access.user_email,
                'date_created': access.date_created.isoformat(),
            }
            for access in listings
        ],
        'tags': [{'id': tag.id, 'name': tag.name} for tag in tags],
    }
    return web.json_response(document_state)


@openapi_docs(
    summary=_('Згенерувати токени для всіх компаній користувача'),
    request_json=BulkTokenGenerationSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def bulk_token_generation(request: web.Request, user: User) -> web.Response:
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(BulkTokenGenerationSchema, raw_data)

    logger.info(
        '[SA] Bulk token generation',
        extra={'current_user': user.email, 'target_user': data.email},
    )

    roles_query = SELECT_ROLE_QUERY.where(
        sa.and_(
            user_table.c.email == data.email,
            is_active_filter,
            role_table.c.user_role == UserRole.admin.value,
        )
    )

    async with request.app['db'].acquire() as conn:
        roles = await select_all(conn, roles_query)
        if not roles:
            return web.Response(text='No roles found')

        deleted_tokens = await delete_tokens(conn, [role.id for role in roles])
        roles_tokens_mapping = await insert_tokens(
            conn,
            [to_role(item) for item in roles],  # type: ignore
            date_expired=None,
        )
    if not get_flag(FeatureFlags.DISABLE_EVENT_COLLECTION):
        delete_actions = await user_actions_utils.build_token_delete_user_actions(
            actor_user=user,
            tokens=[DeleteTokenData.from_row(token) for token in deleted_tokens],
            request=request,
        )
        create_actions = await user_actions_utils.build_token_create_user_actions(
            actor_user=user,
            role_ids=[role.id for role in roles],
            expire_date=None,
            request=request,
        )
        await user_actions_utils.add_user_actions([*delete_actions, *create_actions])

    csv_body = csv_writer(
        headers=['edrpou', 'token'],
        rows=[
            {'edrpou': _role.company_edrpou, 'token': roles_tokens_mapping[_role.id]}
            for _role in roles
        ],
    )

    attachment = Attachment(body=csv_body, content_type='application/csv', name='Токени.csv')

    await emailing.send_email(
        recipient_mixed=data.email,
        subject=_('Ми згенерували токени для ваших компаній'),
        template_name='bulk_token_generation',
        context={},
        reply_to=False,
        attachments=[attachment],
    )

    return web.json_response()


@openapi_docs(
    summary=_('Отримати документ з ElasticSearch для налагодження'),
    params_path={
        'document_id': OpenApiParam(required=True, schema=pv.UUID),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def get_es_document(request: web.Request, _: User) -> web.Response:
    document_id = validate_get_es_document(request)
    logger.info('Get ES document', extra={'document_id': document_id})

    query = services.es.documents.search_query().query(
        Bool.must(
            Document.document_id.term(document_id),
        )
    )
    result = await query.get_result()
    data = {}
    for doc_ in result.hits:
        data[doc_.access_edrpou] = doc_.to_source(Compiler_7_0)
    return json_response(data)


@openapi_docs(
    summary=_('Створити користувача з автогенерованим паролем'),
    request_json=CreateUserSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def create_user(request: web.Request, user: User) -> web.Response:
    """Create autogenerated user and send email if necessary"""

    async with services.db.acquire() as conn:
        ctx = await validate_create_user(request, conn)
        new_user = await utils.create_user(conn, ctx=ctx, user=user)

    return json_response(new_user.to_dict(), status=HTTPStatus.CREATED)


@openapi_docs(
    summary=_('Скинути 2FA для користувача'),
    description=_(
        'УВАГА: Перш ніж робити цю дію потрібно отримати підтвердження від користувача підписане '
        'ключем компанії'
    ),
    request_json=Reset2FASchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def reset_2fa(request: web.Request, admin: User) -> web.Response:
    """
    Reset 2fa for given user
    """
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(Reset2FASchema, raw_data)

    async with services.db.acquire() as conn:
        user: BaseUser | None = None
        if data.email:
            user = await select_base_user(conn=conn, email=data.email)
        elif data.user_id:
            user = await select_base_user(conn=conn, user_id=data.user_id, only_with_email=False)
        else:
            raise InvalidRequest(raw_reason='Не вказано email або user_id')

        if not user:
            raise DoesNotExist(Object.user, email=data.email, user_id=data.user_id)

        await db_reset_2fa(conn, user_id=user.id)

        await insert_super_admin_action(
            conn,
            SuperAdminActionType.reset_2fa,
            admin,
            [data.model_dump(mode='json')],
        )

    logger.info(
        '2fa is reset',
        extra={'data': data.model_dump(mode='json'), 'performer': admin.email},
    )

    return web.json_response()


@openapi_docs(
    summary='Включити/вимкнути номер телефону для аутентифікації',
    request_json=UpdateAuthPhoneSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def update_auth_phone(request: web.Request, admin: User) -> web.Response:
    """
    Disable or enable phone authentication for given user

    Created specially for release of auth by phone to mitigate potential issues with customers.
    You can remove this function if everything works as expected.
    """

    async with services.db.acquire() as conn:
        user, auth_phone = await validate_update_auth_phone(conn, request)

        await update_user(conn, user_id=user.id, data={'auth_phone': auth_phone})

        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.update_auth_phone,
            user=admin,
            extra_details=[
                {
                    'auth_phone': auth_phone,
                    'prev_auth_phone': user.auth_phone,
                    'user_id': user.id,
                }
            ],
        )

    logger.info(
        msg='Phone auth is updated',
        extra={
            'updated_user_id': user.id,
            'performer': admin.email,
            'new_auth_phone': auth_phone,
            'prev_auth_phone': user.auth_phone,
        },
    )

    return web.json_response()


@openapi_docs(
    summary=_('Видалити користувача, замінивши його email на випадковий'),
    description=_(
        'Новий email користувача буде виглядати наступним чином: {uuid}@deleted.vchasno.ua'
    ),
    request_json=SoftDeleteUserSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def soft_delete_user(request: web.Request, admin: User) -> web.Response:
    """
    Soft delete user from the system.
    """

    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(SoftDeleteUserSchema, raw_data)

    async with request.app['db'].acquire() as conn:
        user = await select_base_user(conn=conn, email=data.email)
        if not user:
            raise DoesNotExist(Object.user, email=data.email)

        roles = await select_roles(conn=conn, user_id=user.id)

        await auth.logout_all_sessions(
            conn=conn,
            user_id=user.id,
            with_web=True,
            with_mobile=True,
            with_concierge=True,
        )
        await utils.update_user_on_soft_user_delete(conn, user_id=user.id)
        await utils.delete_roles_on_soft_user_delete(conn, admin=admin, roles=roles)

        # Update concierge user with new email to properly handle user
        # deletion on other services
        await concierge.update_user_profile(conn=conn, user_id=user.id)
        await concierge.sign_out_user_sessions(user_id=user.id)

        await send_user_to_crm(user_id=user.id)

    return web.Response()


@openapi_docs(
    summary=_('Дістати корпоративні домени компанії'),
    params_path={
        'edrpou': OpenApiParam(required=True, schema=pv.EDRPOU),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def get_corporate_domains(request: web.Request, _: User) -> web.Response:
    """Get corporate domains from company"""

    company_edrpou = request.match_info['edrpou']
    async with services.db_readonly.acquire() as conn:
        rows = await select_companies_by_edrpou(
            conn=conn,
            edrpous=[company_edrpou],
            selectable=[company_table.c.email_domains],
        )
    if len(rows) != 1:
        raise DoesNotExist(Object.company, edrpou=company_edrpou)

    return web.json_response(
        {
            'email_domains': rows[0].email_domains,
        }
    )


@openapi_docs(
    summary=_('Оновити корпоративні домени компанії'),
    params_path={
        'edrpou': OpenApiParam(required=True, schema=pv.EDRPOU),
    },
    request_json=ChangeCorporateDomainSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def modify_corporate_domains(request: web.Request, admin: User) -> web.Response:
    """Update company corporate domains"""

    # TODO: remove after frontend implements `update_company_secure_permissions`
    #  because users can config it on their own

    company_edrpou = request.match_info['edrpou']
    raw_json = await validate_json_request(request)
    data = validators.validate_pydantic(ChangeCorporateDomainSchema, raw_json)
    async with services.db.acquire() as conn:
        await validate_company_by_edrpou_exists(conn, company_edrpou)
        await update_company_by_edrpou(
            conn=conn,
            edrpou=company_edrpou,
            data={'email_domains': data.email_domains},
        )

        await insert_super_admin_action(
            conn,
            SuperAdminActionType.change_corporate_email,
            admin,
            [
                {
                    'email': data,
                    'edrpou': company_edrpou,
                }
            ],
        )

    return web.json_response({'status': 'ok'})


@openapi_docs(
    summary=_('Оновити налаштування компанії (CompanyConfig)'),
    params_path={
        'company_id': OpenApiParam(required=True, schema=pv.EDRPOU),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def upsert_company_config(request: web.Request, admin: User) -> web.Response:
    """
    Update company config by super admins using API
    """
    return await sa_update_company_config_common(request, admin)


@openapi_docs(
    summary=_('Оновити налаштування білінгу в компанії (BillingComapnyConfig)'),
    request_json=UpdateBillingCompanyConfigSchema,
    tags=[API_BILLING_TAG],
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def update_billing_company_config_handler(request: web.Request, admin: User) -> web.Response:
    """
    Update billing company config by super admins: permissions or limits for billing
    """
    from app.billing.db import update_billing_company_config

    app = request.app
    async with app['db'].acquire() as conn:
        ctx = await validate_update_billing_company_config(conn, request)
        update_dict = ctx.config.to_update_dict()

        async with conn.begin():
            await update_billing_company_config(
                conn=conn,
                company_id=ctx.company_id,
                config=update_dict,
                is_super_admin_query=True,
            )

            extra = {'company_id': ctx.company_id, 'config': update_dict}
            await insert_super_admin_action(
                conn=conn,
                action=SuperAdminActionType.update_billing_config,
                user=admin,
                extra_details=[extra],
            )

    logger.info(
        msg='Billing company config was updated by super admin',
        extra={
            'company_id': ctx.company_id,
            'config': extra,
        },
    )
    return web.json_response(extra)


@openapi_docs(
    summary=_('Синхронізувати інформацію про компанію з YouControl'),
    params_path={
        'edrpou': OpenApiParam(required=True, schema=pv.EDRPOU),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def sync_company_info_from_youcontrol_data(request: web.Request, admin: User) -> web.Response:
    """
    Updates company.employees_number
    """
    edrpou = request.match_info['edrpou']

    async with services.db.acquire() as conn:
        await validate_company_by_edrpou_exists(conn, edrpou)
        await send_sync_company_info_from_youcontrol(conn, edrpou=edrpou)
        await insert_super_admin_action(
            conn,
            SuperAdminActionType.update_company_employees_number,
            admin,
            [
                {
                    'edrpou': edrpou,
                }
            ],
        )

    return web.json_response({'status': 'ok'})


@openapi_docs(
    summary=_('Завантажити підписи документа для налагодження'),
    params_path={
        'document_id': OpenApiParam(required=True, schema=pv.UUID),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def download_signatures(request: web.Request, user: User) -> web.StreamResponse:
    document_id = request.match_info['document_id']
    async with services.db.acquire() as conn:
        document = await validate_document_exists(conn, {'document_id': document_id})
        document_version = await get_latest_document_version_available_for_company(
            conn=conn,
            document_id=document_id,
            company_edrpou=user.company_edrpou,
        )

        options = await prepare_archive_files(
            user=user,
            document=to_document(document, version=document_version),
            with_signatures_preview=True,
            with_xml_preview=False,
            with_signatures=True,
            with_xml_original=False,
            with_original=False,
            signature_format=None,
            with_revoke_original=False,
            with_revoke_signatures=True,
        )

        await insert_super_admin_action(
            conn,
            SuperAdminActionType.document_signature_download,
            user,
            [{'document_id': document_id}],
        )

    output = await generate_documents_archive(
        archive_filename=options.archive_filename,
        archives=[options],
        in_one_folder=True,
        with_instruction=False,
        filenames_mode=ArchiveFilenamesMode.default,
        filenames_max_length=None,
    )
    return await stream_file_buffer(
        request=request,
        file_name=output.filename,
        raw_buffer=output.buffer,
    )


@openapi_docs(
    summary=_('Завантажити дії користувачів для налагодження'),
    params_path={
        'edrpou': OpenApiParam(required=True, schema=pv.EDRPOU),
    },
    params_query={
        'date_from': OpenApiParam(required=False, schema=datetime.datetime),
        'date_to': OpenApiParam(required=False, schema=datetime.datetime),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def download_document_actions(request: web.Request, user: User) -> web.StreamResponse:
    """
    Download document actions
    with optional 'date_from' and 'date_to' query params
    These params are ISO datetime strings. Example: "2017-08-01"
    """

    edrpou = request.match_info['edrpou']

    date_from = None
    date_to = None
    try:
        if date_from_raw := request.rel_url.query.get('date_from', None):
            date_from = optional_parse_raw_iso_datetime(date_from_raw)
        if date_to_raw := request.rel_url.query.get('date_to', None):
            date_to = optional_parse_raw_iso_datetime(date_to_raw)
    except ValueError:
        logger.error('Invalid date', exc_info=True)
        return web.json_response(
            {'error': 'Invalid date. Format: "2017-08-01"'},
            status=400,
        )
    async with services.events_db.acquire() as db_conn:
        report = await build_report_xls(db_conn, edrpou, date_from=date_from, date_to=date_to)

    async with services.db.acquire() as conn:
        await insert_super_admin_action(
            conn,
            SuperAdminActionType.user_actions_download,
            user,
            [
                {
                    'edrpou': edrpou,
                    'date_from': date_from.isoformat() if date_from else None,
                    'date_to': date_to.isoformat() if date_to else None,
                }
            ],
        )

    return await stream_file_buffer(request, 'user_actions.xls', io.BytesIO(report))


@openapi_docs(
    summary=_('Виконати дію /send для документа від імені даного користувача'),
    description=_(
        'Ця дія може бути корисною в тих випадках, коли документ не був відправлений автоматично '
        'після підписання через якісь технічні причини'
    ),
    request_json=SendDocumentSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def sa_send_document(request: web.Request, admin: User) -> web.Response:
    """
    Call send document by super admin
    """
    async with services.db.acquire() as conn:
        ctx = await validate_send_document(conn, request)
        logging.info(
            'Sending document by super admin',
            extra={
                'ctx_document_id': ctx.document_id,
                'ctx_user_email': ctx.user.email,
                'ctx_company_edrpou': ctx.user.company_edrpou,
                'sa_email': admin.email,
            },
        )

        await documents.send_document(
            conn=conn,
            company_edrpou=ctx.user.company_edrpou,
            user=ctx.user,
            raw_data={'document_id': ctx.document_id},
            request_source=Source.api_internal,
        )

        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.send_document,
            user=admin,
            extra_details=[
                {
                    'role_id': ctx.user.role_id,
                    'document_id': ctx.document_id,
                }
            ],
        )

    return web.json_response({'status': 'ok'})


@openapi_docs(
    summary=_('Відправити push-сповіщення в додатоки для конкретного користувача'),
    request_json=SendPushNotificationToMobileAppSchema,
)
@api_super_admin_permission_required(required_permissions={'can_view_client_data'})
async def send_push_notification_to_mobile_app(
    request: web.Request,
    admin: User,
) -> web.Response:
    """
    Send push notification to the app (or apps) for specific user
    """

    raw_data = await validators.validate_json_request(request)

    valid_data = validators.validate_pydantic(
        schema=SendPushNotificationToMobileAppSchema,
        data=raw_data,
    )

    async with services.db.acquire() as conn:
        firebase_ids = await mobile_notifications_db.select_firebase_ids(
            conn=conn, user_id=valid_data.user_id
        )
        if not firebase_ids:
            raise InvalidRequest(reason=_('Користувач не має жодної активної мобільної сесії'))

        await services.fcm_client.send_push_notifications(
            role_id=admin.role_id,
            firebase_ids=firebase_ids,
            title=valid_data.title,
            description=valid_data.description,
            payload=valid_data.payload,
        )

        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.send_mobile_push_notificaiton,
            user=admin,
            extra_details=[
                {
                    'user_id': valid_data.user_id,
                    'title': valid_data.title,
                    'description': valid_data.description,
                    'payload': valid_data.payload,
                }
            ],
        )

    return web.HTTPOk()


@openapi_docs(
    summary=_('Видалити роль користувача'),
    params_path={
        'role_id': OpenApiParam(required=True, schema=pv.UUID),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def sa_delete_role(request: web.Request, admin: User) -> web.Response:
    """Delete role by super admin"""

    async with services.db.acquire() as conn:
        role = await validate_delete_role(conn, request)

        if role.is_deleted:
            return web.json_response({'status': 'ignored'})

        await auth.delete_role(
            conn=conn,
            role=role,
            by_user=admin,
            status=RoleStatus.deleted,
        )

        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.delete_user,
            user=admin,
            extra_details=[
                {
                    'email': role.user_email,
                    'edrpou': role.company_edrpou,
                    'role_id': role.id,
                }
            ],
        )

    return web.json_response({'status': 'ok'})


@openapi_docs(
    summary=_('Відновити видалений документ'),
    description=_(
        'Відновлюється лише вміст документу, інші метадані не будуть відновлені. '
        'УВАГА: Перш ніж робити цю дію потрібно отримати підтвердження від користувача підписане '
        'ключем компанії'
    ),
    request_json=RestoreDocumentSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def restore_document(request: web.Request, admin: User) -> web.Response:
    """Restore document by super admin"""
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(RestoreDocumentSchema, raw_data)

    async with services.db.acquire() as conn:
        await utils.restore_document(conn, data=data, admin=admin)

    return web.json_response({'status': 'ok'})


@openapi_docs(
    summary=_('Оновити інформацію про тариф в CRM'),
    description=_(
        'Оновлюється інформація про тариф і рахунок тарифу в CRM'
        'Варто використовувати, якщо сталася помилка/збій '
        'і потрібно надіслати у CRM коректні дані про тарифи'
    ),
    request_json=CrmSyncRatesFiltersSchema,
)
@api_super_admin_permission_required(required_permissions={'can_view_client_data'})
async def crm_sync_rates_handler(request: web.Request, admin: User) -> web.Response:
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(CrmSyncRatesFiltersSchema, raw_data)

    await services.kafka.send_record(
        topic=topics.CRM_SYNC_RATES,
        value=data.model_dump(mode='json'),
    )

    async with services.db.acquire() as conn:
        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.sync_rates_in_crm,
            user=admin,
            extra_details=[{**data.model_dump(mode='json'), 'initiator_user_id': admin.id}],
        )

    return web.json_response({'status': 'ok'})


@openapi_docs(
    summary=_('Оновити ціну за користувача для компанії на тарифі "Максимальний"'),
    description=_(
        'Очікується ціна типу int в копійках. '
        'В разі, якщо немає активного Максимального або '
        'ціна за користувача на тарифі вже існує - дані не оновлюються'
    ),
    request_json=UltimateUpdatePricePerUserSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def update_price_per_user_sa_handler(request: web.Request, admin: User) -> web.Response:
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(UltimateUpdatePricePerUserSchema, raw_data)

    async with services.db.acquire() as conn:
        ultimate_rate = await validate_active_ultimate_rate(conn=conn, edrpou=data.edrpou)

        # Update price per user for the current Ultimate rate.
        # In the database, we store this price as subunit integer (копійки)
        if ultimate_rate.price_per_user and ultimate_rate.price_per_user > data.price_per_user:
            return web.json_response(
                {
                    'status': 'Company has a higher price_per_user. '
                    'To change it, contact crm managers'
                },
                status=202,
            )

        await update_price_per_user_in_company_rate(conn, ultimate_rate.id_, data.price_per_user)

        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.update_billing_config,
            user=admin,
            extra_details=[
                {
                    'edrpou': data.edrpou,
                    'price_per_user': data.price_per_user,
                    'rate_id': ultimate_rate.id_,
                }
            ],
        )

    return web.json_response({'status': 'ok'})


@openapi_docs(
    summary=_('Оновити дозволи для компанії'),
    request_json=ChangeCompanyPermissionsSchema,
    params_path={
        'edrpou': OpenApiParam(required=True, schema=pv.EDRPOU),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def sa_update_company_secure_permissions(request: web.Request, admin: User) -> web.Response:
    """
    Update company security permissions by admin.
    """

    company_edrpou = request.match_info['edrpou']

    raw_json = await validate_json_request(request)
    data = validators.validate_pydantic(ChangeCompanyPermissionsSchema, raw_json)

    async with services.db.acquire() as conn:
        await update_company_by_edrpou(
            conn=conn,
            edrpou=company_edrpou,
            data=data.to_dict(),
        )

        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.update_company_security_settings,
            user=admin,
            extra_details=[data.to_dict()],
        )

    return web.json_response()


@openapi_docs(
    summary=_('Створити новий публічний шаблон'),
)
@api_super_admin_permission_required(required_permissions={'can_edit_special_features'})
async def sa_add_template(request: web.Request, user: User) -> web.Response:
    data = dict(await validate_post_request(request))
    file = data.get('file')
    if not isinstance(file, FileField):
        raise InvalidRequest(reason=_('Use multipart/form-data content-type'))
    content = file.file.read()

    async with services.db.acquire() as conn:
        async with conn.begin():
            validated_data = await validate_template_add(
                conn=conn,
                user=user,
                data_raw={
                    'title': data.get('title') or file.filename,
                    'category_id': data.get('category_id'),
                    'extension': get_file_extension(file.filename),
                    'content': content,
                },
            )
            template = await add_template(
                conn=conn,
                user=user,
                validated_data=validated_data,
                is_private=False,
            )

            await insert_super_admin_action(
                conn=conn,
                action=SuperAdminActionType.template_create,
                user=user,
                extra_details=[{'template_id': template.id}],
            )

    return json_response(template.to_dict())


@openapi_docs(
    summary=_('Видалити публічний шаблон'),
    params_path={
        'template_id': OpenApiParam(required=True, schema=pv.UUID),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_special_features'})
async def sa_template_delete(request: web.Request, user: User) -> web.Response:
    """
    Delete template from user's company
    """

    async with services.db.acquire() as conn:
        template = await validate_template_delete(
            conn=conn,
            user=user,
            template_id=request.match_info['template_id'],
            is_public=True,
        )
        await update_template(
            conn=conn,
            filter_company_id=None,
            filter_template_id=template.id,
            is_deleted=True,
        )
        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.template_delete,
            user=user,
            extra_details=[{'template_id': template.id}],
        )

    template_preview_keys = await s3_utils.get_keys_with_prefix(
        f'{TEMPLATE_PREVIEW_PREFIX}/{template.id}/'
    )

    # remove all related data
    await s3_utils.delete_batch(
        [
            get_templates_s3_key(template.id),
            *template_preview_keys,
        ],
    )
    return web.json_response(status=HTTPStatus.NO_CONTENT)


@openapi_docs(
    summary=_('Видалити публічний шаблон'),
    request_json=UpdateTemplateSchema,
    params_path={
        'template_id': OpenApiParam(required=True, schema=pv.UUID),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_special_features'})
async def sa_template_update(request: web.Request, user: User) -> web.Response:
    """
    Update template from user's company
    """

    raw_data = await validate_json_request(request)

    async with services.db.acquire() as conn:
        validated_data = await validate_template_update(
            template_id=request.match_info['template_id'],
            conn=conn,
            user=user,
            data_raw=raw_data,
            is_public=True,
        )
        template = await update_template(
            conn=conn,
            filter_template_id=validated_data.template_id,
            title=validated_data.title,
            extension=validated_data.extension,
            category_id=validated_data.category_id,
            filter_company_id=None,
        )
        if template:
            await insert_super_admin_action(
                conn=conn,
                action=SuperAdminActionType.template_update,
                user=user,
                extra_details=[{'template_id': template.id}],
            )
        else:
            # may be none if template was deleted between validation and update
            raise DoesNotExist(obj=Object.document_template)

    return json_response(template.to_dict())


@openapi_docs(
    summary=_('Оновити статус погодження для документа'),
    params_path={
        'document_id': OpenApiParam(required=True, schema=pv.UUID),
    },
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def recalculate_reviews_status(request: web.Request, admin: User) -> web.Response:
    """
    Recalculate status of reviews for a document.
    Updates only status. Does not send any notifications.
    """
    async with services.db.acquire() as conn:
        document_id = validate_pydantic_adapter(
            pv.UUIDAdapter, value=request.match_info['document_id']
        )
        await validate_document_exists(conn, document_id=document_id)

        # update for all companies that have access to the document
        rows = await select_all(
            conn,
            query=(
                sa.select(
                    [
                        company_listing_table.c.id,
                        company_listing_table.c.edrpou,
                        company_table.c.id.label('company_id'),
                    ]
                )
                .select_from(
                    company_listing_table.join(
                        company_table,
                        company_table.c.edrpou == company_listing_table.c.edrpou,
                    )
                )
                .where(company_listing_table.c.document_id == document_id)
            ),
        )

        for company_access in rows:
            await update_review_statuses_in_db(
                conn=conn,
                documents_ids=[document_id],
                company_edrpou=company_access.edrpou,
                company_id=company_access.company_id,
            )

        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.document_review_recalculation,
            user=admin,
            extra_details=[{'document_id': document_id}],
        )

    return web.json_response(status=HTTPStatus.OK)


@openapi_docs(
    summary=_('Перевірити та видалити email з чорного списку AWS SES'),
    request_json=ManageAwsSesBlacklistSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def manage_aws_ses_blacklist(request: web.Request, admin: User) -> web.Response:
    """
    Check and remove email from SES suppression list
    """
    ctx = await validate_manage_aws_ses_blacklist(request)
    output = await utils.manage_aws_ses_blacklist(ctx, admin=admin)
    return api_response(request, data=output)


@openapi_docs(
    summary=_('Temp template migration'),
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def migrate_template(request: web.Request, _: User) -> web.Response:
    """
    Temp view.
    Download and decrypt template - then upload encrypted with general_key instead of company_one
    """
    template_id = request.match_info['template_id']
    async with services.db.acquire() as conn:
        template = await validate_template_exists(
            conn=conn,
            template_id=template_id,
        )

    # remove all preview data
    template_preview_keys = await s3_utils.get_keys_with_prefix(
        f'{TEMPLATE_PREVIEW_PREFIX}/{template.id}/'
    )
    await s3_utils.delete_batch(template_preview_keys)

    # Download and decrypt actual template content
    download_file = DownloadFile(key=get_templates_s3_key(template_id))
    content, __ = await s3_utils.download(download_file)
    if not content:
        logger.info('empty content')
        raise DoesNotExist(obj=Object.document_template)

    item = UploadFile(
        key=get_templates_s3_key(template.id),
        body=content,
    )
    await s3_utils.upload(item)

    # Generate new preview
    try:
        await generate_template_preview(template=template, content=content)
    except Exception:
        logger.exception('Failed to generate template preview')

    return web.json_response(status=HTTPStatus.OK)


@openapi_docs(summary='Зберегти список email для опитування користувачів')
@api_super_admin_permission_required(required_permissions={'can_edit_special_features'})
async def sa_store_survey_user_list(request: web.Request, __: User) -> web.Response:
    """
    Store a survey user list.
    Some popup should be shown on the frontend to inform users about this.
    Client uses graphql to retrieve this data.
    """
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(SurveyUserListSchema, raw_data)

    redis_key = SURVEY_TO_REDIS_KEY.get(data.survey_id)
    if not redis_key:
        raise InvalidRequest(
            reason=_('Не валідний survey_id'), details={'allowed': list(SURVEY_TO_REDIS_KEY.keys())}
        )

    if not data.emails:
        await services.redis.delete(redis_key)
        return web.json_response({'status': 'ok'})

    async with services.redis.pipeline() as pipe:
        pipe.delete(redis_key)
        pipe.sadd(redis_key, *data.emails)
        pipe.expire(redis_key, data.ttl)
        await pipe.execute()

    return web.json_response({'status': 'ok'})


@openapi_docs(summary='Заблокувати користувача в сервісі та надіслати лист із оновленням паролю')
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def block_user(request: web.Request, admin: User) -> web.Response:
    """
    View for blocking a user and resetting their password.

    This view:
        - logs out all active user sessions
        - revokes API tokens
        - sets a random password
        - sends an email with instructions for resetting the password
    """
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(ResetUserPasswordSchema, raw_data)

    async with services.db.acquire() as conn:
        user = await select_base_user(conn, email=data.email)
        if not user:
            raise InvalidRequest(reason=_('Користувача не знайдено'))

        async with conn.begin():
            await block_account(conn, user)
            await insert_super_admin_action(
                conn=conn,
                action=SuperAdminActionType.block_user,
                user=admin,
                extra_details=[{'email': data.email}],
            )
            await send_email_about_blocked_user_account(user=user)

    return web.json_response(status=HTTPStatus.OK)


@openapi_docs(
    summary='Заблокувати користувача',
    description=(
        'Ця дія дозволяє заблокувати користувача, щоб запобігти його входу в систему. '
        'Використовуйте це для блокування користувача у випадку зловживання системою.'
    ),
    request_json=BanUserSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def ban_user(request: web.Request, admin: User) -> web.Response:
    """
    Mark a user as banned to prevent them from logging in.

    Use this to block a user from accessing the system in case of system abuse or so.
    """
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(BanUserSchema, raw_data)

    async with services.db.acquire() as conn:
        if data.user_id:
            user = await select_base_user(conn, user_id=data.user_id)
        elif data.email:
            user = await select_base_user(conn, email=data.email)
        else:
            raise InvalidRequest(raw_reason='Не вказано користувача для блокування')

        if not user:
            raise InvalidRequest(raw_reason='Користувача не знайдено')

        async with conn.begin():
            await auth.ban_user(conn, user_id=user.id, is_banned=data.is_banned, reason=data.reason)
            await insert_super_admin_action(
                conn=conn,
                action=SuperAdminActionType.ban_user,
                user=admin,
                extra_details=[
                    {
                        'user_id': user.id,
                        'email': user.email,
                        'is_banned': data.is_banned,
                        'reason': data.reason,
                    }
                ],
            )

        await auth.logout_all_sessions(
            conn=conn,
            user_id=user.id,
            with_web=True,
            with_mobile=True,
            with_concierge=True,
        )

    return web.json_response(status=HTTPStatus.OK)


@openapi_docs(
    summary='Керувати виключеннями для капчі',
    request_json=ManageCaptchaExcludedSchema,
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def manage_captcha_excluded(request: web.Request, admin: User) -> web.Response:
    """
    Add or remove user from captcha excluded list
    """
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(ManageCaptchaExcludedSchema, raw_data)

    is_excluded: bool
    if data.action == 'add':
        await captcha.add_captcha_excluded(
            action=data.captcha_action,
            identifier=data.captcha_identifier,
            hours=data.hours,
        )
        is_excluded = True
    elif data.action == 'remove':
        await captcha.remove_captcha_excluded(
            action=data.captcha_action,
            identifier=data.captcha_identifier,
        )
        is_excluded = False
    elif data.action == 'check':
        is_excluded = await captcha.is_captcha_excluded(
            action=data.captcha_action,
            identifier=data.captcha_identifier,
        )
    else:
        assert_never(data.action)

    async with services.db.acquire() as conn:
        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.manage_captcha_excluded,
            user=admin,
            extra_details=[
                {
                    'action': data.action,
                    'captcha_action': data.captcha_action,
                    'identifier': data.captcha_identifier,
                    'hours': data.hours,
                }
            ],
        )

    return web.json_response({'status': 'ok', 'is_excluded': is_excluded})


@openapi_docs(
    summary='Підтвердити email користувача, якщо це технічний email з неможливістю підтвердження'
)
@api_super_admin_permission_required(required_permissions={'can_edit_client_data'})
async def confirm_technical_email(request: web.Request, admin: User) -> web.Response:
    """
    Confirm technical email with inability to confirm.
    """
    raw_data = await validate_json_request(request)
    data = validators.validate_pydantic(ConfirmTechnicalEmailSchema, raw_data)

    async with services.db.acquire() as conn:
        user = await select_base_user(conn, email=data.email)
        if not user:
            raise InvalidRequest(reason=_('Користувача не знайдено'))

        await update_user_email_confirmed(conn, user_id=user.id, is_email_confirmed=True)

        await insert_super_admin_action(
            conn=conn,
            action=SuperAdminActionType.confirm_technical_email,
            user=admin,
            extra_details=[{'email': data.email, 'reason': data.reason}],
        )

    return web.json_response(status=HTTPStatus.OK)

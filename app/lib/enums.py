from __future__ import annotations

import inspect
import logging
from collections import Counter
from collections.abc import ValuesView
from enum import (
    Enum,
    IntFlag,
    StrEnum,
    auto,
    unique,
)
from typing import Any, Self

from .types import AnyList

logger = logging.getLogger(__name__)


class NamedEnum(StrEnum):
    """
    Replaces auto() with field name.

    login = auto() => login = 'login'

    >>> from enum import auto
    >>> class Actions(NamedEnum):
    ...     login = auto()
    ...     logout = auto()
    >>> Actions.logout == 'logout'
    True
    >>> Actions.logout
    <Actions.logout: 'logout'>
    """

    @staticmethod
    def _generate_next_value_(
        name: str,
        start: int,
        count: int,
        last_values: AnyList,
    ) -> str:
        return name.lower()


@unique
class CommentStatus(Enum):
    ordinary = 9000
    reject = 9001


@unique
class DocumentFolder(Enum):
    unknown = 6000
    inbox = 6001
    outgoing = 6002
    contragent_missing = 6003
    has_comments = 6004
    imported = 6005
    wait_my_sign = 6006
    internal = 6007
    not_internal = 6008


@unique
class ListDirection(StrEnum):
    asc = 'asc'
    desc = 'desc'


@unique
class DocumentListOrder(Enum):
    company_email = 'company_email'
    company_name = 'company_name'
    date = 'date'
    edrpou = 'edrpou'
    number = 'number'
    title = 'title'
    seqnum = 'seqnum'
    amount = 'amount'


@unique
class DocumentListSortDate(Enum):
    date_listing = 'date_listing'
    date_document = 'date_document'
    date_created = 'date_created'
    date_finished = 'date_finished'


@unique
class DocumentStatus(Enum):
    """
    Document flow: https://tabula-rasa.atlassian.net/wiki/spaces/vchasno/pages/4952001
    """

    uploaded = 7000
    ready_to_be_signed = 7001
    sent = 7002
    signed = 7003
    signed_and_sent = 7004
    seen = 7005  # not used yet
    reject = 7006
    approved = 7007
    finished = 7008
    deleted = 7009
    flow = 7010
    revoked = 7011

    histored = 7020

    @property
    def is_final(self) -> bool:
        return self in (
            self.finished,
            self.reject,
            self.deleted,
            self.revoked,
            self.histored,
        )

    @property
    def is_document_update_allowed(self) -> bool:
        if self == DocumentStatus.histored:
            return True
        return not self.is_final

    @property
    def is_rejected(self) -> bool:
        return self == self.reject

    @property
    def is_revoked(self) -> bool:
        return self == self.revoked


@unique
class SignatureType(Enum):
    signature = 'signature'
    stamp = 'stamp'


@unique
class SignatureFormat(StrEnum):
    """
    How the signature was signed:
     - External - file and signature are separate files.
     - Internal - file and signatures stored in p7s/asic container.

     - Separated - signature of document original
     - Wrapped - signature of previous signature or document original
     - Appended - add new signature to .p7s container (supported only for
     internal signature)

     # The following formats are not used anymore, but are kept for backward compatibility
     # because we have old documents that were signed in this way:
     - TTN wrapped - signature and original XML stored in new XML
    """

    # (doc + sign1), (doc + sign2), ...
    external_separated = 'external_separated'

    # (((doc + sign1) + sign2) + ...)
    external_wrapped = 'external_wrapped'

    # (doc + .p7s), (doc + .p7s), ...
    internal_separated = 'internal_separated'

    # (((doc + .p7s) + .p7s) + ...)
    internal_wrapped = 'internal_wrapped'

    # (doc + .p7s + .p7s + ...)
    internal_appended = 'internal_appended'

    # ASiC-e container with file + signatures
    internal_asic = 'internal_asic'

    # (((xml + sign1) + sign1) + ...)
    ttn_wrapped = 'ttn_wrapped'  # REMOVED

    @property
    def is_eusign(self) -> bool:
        """Signature format that should be verified by eusign"""
        return self in (
            self.external_separated,
            self.external_wrapped,
            self.internal_separated,
            self.internal_wrapped,
            self.internal_appended,
            self.internal_asic,
        )

    @property
    def is_internal(self) -> bool:
        return self in (
            self.internal_separated,
            self.internal_wrapped,
            self.internal_appended,
            self.internal_asic,
        )

    @property
    def is_external(self) -> bool:
        return self in (self.external_separated, self.external_wrapped)


@unique
class Source(Enum):
    api_blackbox = 'api_blackbox'
    api_internal = 'api_internal'
    api_public = 'api_public'
    api_mobile = 'api_mobile'
    plugin_1c = 'plugin_1c'


@unique
class SignersSource(Enum):
    api = 'api'
    web = 'web'
    template = 'template'


@unique
class UserRole(int, Enum):
    user = 8000
    admin = 8001


@unique
class AppLevel(NamedEnum):
    """
    Application level. It is used to separate different environments. Be careful when
    changing names of the levels, because they some configs (like s3 encryption keys)
    use level names as a part of the config key.
    """

    prod = auto()
    dev = auto()
    local = auto()
    test = auto()

    @property
    def is_dev(self) -> bool:
        return self == AppLevel.dev

    @property
    def is_prod(self) -> bool:
        return self == AppLevel.prod


class NotificationType(Enum):
    inbox = 'can_receive_inbox'
    comment = 'can_receive_comments'
    reject = 'can_receive_rejects'
    reminder = 'can_receive_reminders'
    review = 'can_receive_reviews'
    new_documents = 'can_receive_access_to_doc'
    delete_requests = 'can_receive_delete_requests'
    can_receive_finished_docs = 'can_receive_finished_docs'
    other = 'can_receive_notifications'


@unique
class SuperAdminActionType(NamedEnum):
    document_request_state = 'doc_req_state'

    document_signature_download = auto()
    document_actions_download = auto()
    user_actions_download = auto()
    document_review_recalculation = auto()

    reset_2fa = auto()
    delete_user = auto()
    change_email = auto()
    activate_role = auto()

    change_corporate_email = auto()

    update_config = auto()
    update_company_employees_number = auto()
    update_billing_config = auto()
    update_company_security_settings = auto()

    add_rate = auto()
    update_rate = auto()
    delete_rate = auto()
    set_custom_price_for_documents = auto()

    activate_bill = auto()
    cancel_bill = auto()  # cancel / refund
    activate_debit = auto()
    cancel_debit = auto()
    activate_bonus = auto()
    activate_custom_bonus = auto()
    cancel_bonus = auto()
    delete_bonus = auto()

    fetch_privatbank_transactions = auto()
    fetch_pumb_transactions = auto()
    update_sa_permissions = auto()

    send_document = auto()

    setup_marketing_campaign_audience = auto()
    delete_marketing_campaign_audience = auto()

    send_mobile_push_notificaiton = auto()

    create_public_document_category = auto()
    update_public_document_category = auto()
    delete_public_document_category = auto()

    restore_document = auto()

    sync_rates_in_crm = auto()

    document_ai_suggest = auto()

    template_create = auto()
    template_update = auto()
    template_delete = auto()

    manage_aws_ses_blacklist = auto()

    import_zvit_users = auto()
    update_banner_promo_kasa_list = auto()

    block_user = auto()
    ban_user = auto()
    update_phone_auth_whitelist = auto()
    update_auth_phone = auto()
    manage_captcha_excluded = auto()
    confirm_technical_email = auto()


@unique
class BitMaskOperator(StrEnum):
    append = '|'
    remove = '& ~'
    get = '&'


class BitMask(IntFlag):
    def remove(self: Self, source: Self) -> Self:
        return self.__class__(self & ~source)

    def contains(self: Self, flag: Self) -> bool:
        return self.__class__(self & flag) == flag

    def add(self: Self, source: Self) -> Self:
        return self.__class__(self | source)


@unique
class Language(Enum):
    # Supported language codes in ISO 639-1
    uk = 'uk'  # default language
    en = 'en'


class RenderSignatureAtPage(Enum):
    first = 'first'
    last = 'last'
    all = 'all'


def enum_items(native_enum: type[Enum]) -> ValuesView[Any]:
    """
    Returns dict_values of Enum members:
    { SomeEnum.key1.name: SomeEnum.key1.value, ... }
    """
    return native_enum.__members__.values()


def enum_values(native_enum: type[Enum]) -> AnyList:
    """
    Returns list of Enum values:
    [ SomeEnum.key1.value, SomeEnum.key2.value, ...]
    """
    return [item.value for item in enum_items(native_enum)]


def validate_enums() -> None:
    values = Counter(
        value
        for item in globals().values()
        if inspect.isclass(item) and issubclass(item, Enum)
        for value in enum_values(item)
    )
    duplicates = [value for value, count in values.items() if count > 1]
    if duplicates:
        logger.error(
            'Duplicated values in Enums are not allowed. Duplicated '
            f'values: {duplicates}. Please make all values unique and restart '
            'app.'
        )
        raise SystemExit(1)


validate_enums()

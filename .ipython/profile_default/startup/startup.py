import asyncio
import os
from enum import Enum
from getpass import getpass

import uvloop
from aiohttp import web
from app.lib.database import create_database_engine
from termcolor import cprint

from app.app import create_app
from app.services import services

asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

app = create_app()
loop = asyncio.new_event_loop()

ANALYTICS_HOST = '**********'
ANALYTICS_PORT = '5432'
ANALYTICS_DATABASE = 'evodoc'

class DatabaseOption(Enum):
    """Options for database inisitalisation"""

    local = 1
    analytics = 2

    @property
    def is_local(self) -> bool:
        return self == DatabaseOption.local

    @property
    def is_analytics(self) -> bool:
        return self == DatabaseOption.analytics


async def connect_db(app: web.Application) -> None:
    try:
        _ = int(
            input(
                'Enter database option you want to connect to\n'
                '- 1 - Local database (docker)\n'
                '- 2 - Production analytics\n'
                'Option: '
            )
        )
        database_option = DatabaseOption(_)
    except ValueError:
        cprint('Unknown database option.', 'red')
        os.kill(os.getpid(), 1)

    dsn = 'postgres://{username}:{password}@{host}:{port}/{database}?client_encoding=utf-8'
    if database_option.is_analytics:
        cprint('For analytics database you need to specify connection parameters', 'yellow')

        username = input('Enter username (example - k.nassar): ')
        password = getpass('Enter LDAP: ')

        if not username or not password:
            cprint('You HAVE to specify username and password', 'red')
            os.kill(os.getpid(), 1)

        host = input('Enter host ip address (leave blank for default): ') or ANALYTICS_HOST
        port = input('Enter port (leave blank for default): ') or ANALYTICS_PORT
        database = input('Enter database name (leave blank for evodoc): ') or ANALYTICS_DATABASE

    elif database_option.is_local:
        username, password, host, port, database = 'evodoc', 'evodoc', 'postgres', '5432', 'evodoc'

    else:
        cprint('Database option not implemented', 'red')
        os.kill(os.getpid(), 1)

    engine = await create_database_engine(
        dsn=dsn.format(
            username=username,
            password=password,
            host=host,
            port=port,
            database=database,
        ),
        timeout=60,
        minsize=1,
        maxsize=10,
    )
    app['db'] = engine


cprint('- Initializing database connection -', 'yellow')
loop.run_until_complete(connect_db(app))
cprint('- Database connected -', 'green')

services.ctx_db.set(app['db'])

cprint(
    '\nWelcome to EDO shell!\n'
    'Please note: first database call could fail because of unconfigured asyncio loops '
    'but its not blocking any futher calls', 'green'
)

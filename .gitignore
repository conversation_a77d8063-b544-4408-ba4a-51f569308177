*.DS_Store*
*.pyc
*.swp

__pycache__/
.*cache/
.vagga/
.vagrant/
coverage-html-report/
env/
logs/
node_modules/
src/

cron/*.log

static/*/*
!static/fonts/*
!static/css/lib/
!static/favicons/**
!static/manifest.json
!static/files/**
!static/images/**
!static/robots.txt
!static/sitemap.xml

static/mf-manifest.json
static/mf-stats.json
static/remoteEntry*.js
static/*.mjs

coverage
.babel.json
.coverage
.coverage.*
.idea
.vscode
.nvimlog
.python-version
.unison*
.~lock*

appendonly.aof
package-lock.json
yarn-error.log


# Allow to override local configuration in the project
config/vchasno/local/config.override.yaml

.vim
/allure-report/
/allure.report/
.ropeproject
.flags.json
.flags.yaml

# Ignore all certificates, except a few ones
eusign/prod/certificates/*
eusign/dev/certificates/*
!eusign/prod/certificates/CACertificates.p7b
!eusign/dev/certificates/CACertificates.p7b
!eusign/prod/certificates/CA-45C189E53CF0DC4804000000E08408004F871500.cer
!eusign/prod/certificates/CA-45C189E53CF0DC4804000000E184080057871500.cer

# translations
translations/**/*.mo
translations/*.pot

# coverage reports
/coverage.xml
/coverage.coverage*

# FCM configuration file for local development
fcm_configuration.json

# nodenv version file
.node-version

# collabora stuff
config/vchasno/local/certs/*

# tmp db dump file while coping from dev doesn't work
scripts/trunk.dump
scripts/trunk.sql
scripts/trunk.zip
scripts/trunk.zip.enc

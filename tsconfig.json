{
    "compilerOptions": {
        "allowSyntheticDefaultImports": true,
        "baseUrl": "cs",
        "target": "ESNext",
        "module": "ESNext",
        "lib": ["ESNext", "dom"],
        "jsx": "react",
        "noEmit": true,
        "skipLibCheck": true,
        "resolveJsonModule": true,
        "isolatedModules": true,
        "allowJs": true,
        /* Strict Type-Checking Options */
        "noImplicitAny": true,
        "strictNullChecks": true,

        /* Module Resolution Options */
        "moduleResolution": "Bundler",
        "forceConsistentCasingInFileNames": true,
        "esModuleInterop": true,
        "paths": {
            "gql-types": ["gql/graphql.ts"],
            "icons/*": ["svg/*"],
            "lib/*": ["lib/*"],
            "store/*": ["store/*"],
            "contexts/*": ["contexts/*"],
            "selectors/*": ["selectors/*"],
            "components/*": ["components/*"],
            "hooks/*": ["hooks/*"],
            "services/*": ["services/*"],
            "ui/*": ["components/ui/*"],
            "records/*": ["records/*"],
            "mf": ["mf"]
        }
    },
    "include": ["cs"],
    "exclude": ["cs/lib/vendor/*"]
}

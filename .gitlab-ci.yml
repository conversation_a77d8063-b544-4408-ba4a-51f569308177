stages:
    - test
    - build
    - build_step2
    - deploy


# Backend specific changes for triggering backend jobs
.backend-changes: &backend-changes
    changes:
        # always compare to "master" branch to avoid running frontend jobs
        # after rebase with "master" that contains some frontend changes
        compare_to: "master"
        paths:
            - "api/**/*"
            - "app/**/*"
            - "cron/**/*"
            - "indexator/**/*"
            - "requirements/**/*"
            - "translations/**/*"
            - "worker/**/*"
            - "alembic.ini"
            - "conftest.py"
            - "pyproject.toml"

# Frontend specific changes for triggering frontend jobs
.frontend-changes: &frontend-changes
    changes:
        compare_to: "master"
        paths:
            - "cs/**/*"
            - "jest/**/*"
            - ".editorconfig"
            - ".eslintignore"
            - ".eslintrc"
            - ".prettierignore"
            - ".prettierrc.json"
            - ".stylelintignore"
            - ".stylelintrc.json"
            - ".swcrc"
            - ".yarnrc"
            - "package.json"
            - "tsconfig.json"
            - "webpack.config.js"
            - "yarn.lock"

# Common changes for triggering both backend and frontend jobs
.common-changes: &common-changes
    changes:
        compare_to: "master"
        paths:
            - ".gitlab/**/*"
            - "config/**/*"
            - "docs/**/*"
            - "eusign/**/*"
            - "scripts/**/*"
            - "static/**/*"
            - ".coveragerc"
            - ".dockerignore"
            - ".gitignore"
            - ".gitlab-ci.yml"
            - "docker-compose.yaml"
            - "justfile"
            - "ospcu.ini"
            - "osplm.ini"
            - "swagger.yaml"


# Rules when to run backend tests and lints
.check-backend-rules: &check-backend-rules
    rules:
        # Skip lint and tests for tags to speed up deployment. We expect that developer before requesting
        # deployment has checked that pipeline on last commit to "master" branch passed successfully.
        - if: '$CI_COMMIT_TAG'
          when: never
        # on "master" branch always run tests and lints
        - if: '$CI_COMMIT_BRANCH == "master"'
          when: on_success
        # on other branches run tests and lints only if backend or common files were changed
        - <<: *backend-changes
          when: on_success
        - <<: *common-changes
          when: on_success
        - when: never

# Rules when to run frontend tests and lints
.check-frontend-rules: &check-frontend-rules
    rules:
        # Skip lint and tests for tags to speed up deployment. We expect that developer before requesting
        # deployment has checked that pipeline on last commit to "master" branch passed successfully.
        - if: '$CI_COMMIT_TAG'
          when: never
        # on "master" branch always run tests and lints
        - if: '$CI_COMMIT_BRANCH == "master"'
          when: on_success
        # on other branches run tests and lints only if frontend or common files were changed
        - <<: *frontend-changes
          when: on_success
        - <<: *common-changes
          when: on_success
        - when: never

include:
    - template: Security/SAST.gitlab-ci.yml
    - template: Security/Secret-Detection.gitlab-ci.yml

    # To prevent creating duplicate merge request pipelines
    # See: https://stackoverflow.com/a/69860485
    - template: 'Workflows/Branch-Pipelines.gitlab-ci.yml'

    # This include is necessary for deploy by ArgoCD to Vchasno
    # infrastructure: https://gitlab.vchasno.com.ua/libs/base_ci/ci
    - project: 'libs/base_ci/ci'
      ref: master
      file: '.aws-argo-helm-deploy.gitlab-ci.yaml'

image:
    name: ${DOCKER_REGISTRY_AWS}/platform/k8s-deploy:v1.0.0


# TODO: make default section for runner after ARM will be enabled on K8S runners
# default K8s runner for all jobs
.default_runner: &default_runner
  tags:
    - edo-gl-runner-cpu
  services:
    - name: docker:28.0-dind
      variables:
        HEALTHCHECK_TCP_PORT: "2375"
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""

# Temp ARM runner for specific jobs
.arm_runner: &arm_runner
    image: docker:25.0.5
    tags:
      # ARM runner!
      # Do not use for build deploy images, or rewrite build images for a specific platform
      - Muad-Dib


variables:
    EDO_REGISTRY: ${DOCKER_REGISTRY_AWS}/vchasno/edo/edo
    COMPOSE: docker-compose -p ${CI_PROJECT_NAME}_${CI_JOB_ID}_${CI_COMMIT_SHORT_SHA}
    # Local images
    IMAGE_TOOLS: ${EDO_REGISTRY}:tools-latest
    IMAGE_ASSETS: ${EDO_REGISTRY}:assets-latest
    # Deploy images (project, type)
    IMAGE_EDO_FRONTEND: ${EDO_REGISTRY}/frontend
    IMAGE_EDO_BACKEND: ${EDO_REGISTRY}/backend
    IMAGE_EDO_APP: ${EDO_REGISTRY}
    # Deploy tags (environments)
    IMAGE_TAG_DEV: ${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHORT_SHA}
    IMAGE_TAG_PRD: ${CI_COMMIT_REF_NAME} # tag or branch name
    # Enable buildkit
    COMPOSE_DOCKER_CLI_BUILD: 1
    DOCKER_BUILDKIT: 1
    # Runner Default Resources
    KUBERNETES_CPU_REQUEST: "1"


### === Checks ===

check-frontend:
    stage: test
    <<: *arm_runner
    <<: *check-frontend-rules
    interruptible: true
    before_script:
        - ${COMPOSE} pull --ignore-pull-failures cstest
        - ${COMPOSE} build yarn
    script:
        - ${COMPOSE} run yarn run lint
        - ${COMPOSE} run yarn run check-types
        - ${COMPOSE} run yarn run audit:code
        - ${COMPOSE} run yarn test
    after_script:
        - ${COMPOSE} push yarn
        - ${COMPOSE} down

check-backend:
    stage: test
    <<: *arm_runner
    <<: *check-backend-rules
    interruptible: true
    # Only one job from this group will run at a time
    resource_group: backend-tests
    before_script:
        - ${COMPOSE} pull --ignore-pull-failures python-dev
        - ${COMPOSE} build python-dev
    script:
        - ${COMPOSE} run ruff-formatter --check
        - ${COMPOSE} run ruff-linter
        - ${COMPOSE} run mypy
        - ${COMPOSE} run python-dev ./scripts/translations/check_catalog.py
        - ${COMPOSE} run pytest --ci
    after_script:
        - ${COMPOSE} push python-dev
        - ${COMPOSE} down

test-backend-slow:
    stage: test
    <<: *arm_runner
    <<: *check-backend-rules
    interruptible: true
    # only one job from this group will run at a time
    resource_group: backend-tests-slow
    before_script:
        - ${COMPOSE} pull --ignore-pull-failures python-dev
        - ${COMPOSE} build python-dev
    script:
        - ${COMPOSE} run pytest-slow
    after_script:
        - ${COMPOSE} down

check-translation-coverage:
    stage: test
    <<: *arm_runner
    allow_failure: true
    rules:
        - when: manual
    interruptible: true
    before_script:
        - ${COMPOSE} pull --ignore-pull-failures python-dev
        - ${COMPOSE} build python-dev
    script:
        - ${COMPOSE} run python-dev ./scripts/translations/check_coverage.py
    after_script:
        - ${COMPOSE} down

### === Template overrides ===
# Here, we're adding interruptible: true to all SAST jobs to allow them to be interrupted.
# This change prevents the pipeline from becoming uninterruptible when at least one SAST job is run.
# - https://docs.gitlab.com/ee/ci/yaml/#interruptible
# - https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Jobs/SAST.gitlab-ci.yml
.scan-defaults: &scan-defaults
    interruptible: true
    tags:
        - edo-gl-runner-cpu
    variables:
        KUBERNETES_CPU_REQUEST: "0.1"

brakeman-sast:
    <<: *scan-defaults

flawfinder-sast:
    <<: *scan-defaults

nodejs-scan-sast:
    <<: *scan-defaults

secret_detection:
    <<: *scan-defaults

security-code-scan-sast:
    <<: *scan-defaults

semgrep-sast:
    <<: *scan-defaults

spotbugs-sast:
    <<: *scan-defaults


### === Build anchors ===
# Common build scripts for backend, frontend and app for different environments

.build-backend-scripts: &build-backend-scripts
    # pull cache for a faster build
    - docker pull ${CACHE_IMAGE} || true

    # build and push backend image
    - |
      docker build \
        --file config/docker/deploy/backend.dockerfile \
        --cache-from ${CACHE_IMAGE} \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --build-arg VCHASNO_LEVEL=${APP_LEVEL} \
        --build-arg OSPLM_CONFIG=${OSPLM_CONFIG} \
        --build-arg VCHASNO_SECRETS=${APP_SECRETS} \
        --build-arg STATIC_MAIN_HASH="$(find cs -type f -print0 | sort -z | xargs -0 sha1sum | sha1sum)" \
        --build-arg APP_VERSION=$(git describe) \
        --target release \
        --tag ${TARGET_IMAGE} .
    - docker push ${TARGET_IMAGE}

    # build and push cache image
    - |
      docker build \
        --file config/docker/deploy/backend.dockerfile \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --target cache \
        --tag ${CACHE_IMAGE} .
    - docker push ${CACHE_IMAGE}


.build-frontend-scripts: &build-frontend-scripts
    # pull cache for a faster build
    - docker pull ${CACHE_IMAGE} || true

    # build, push image and upload assets to s3
    - |
      docker build \
        --file config/docker/deploy/frontend.dockerfile \
        --cache-from ${CACHE_IMAGE} \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --build-arg VCHASNO_LEVEL=${APP_LEVEL} \
        --build-arg VCHASNO_CONFIG=${APP_CONFIG} \
        --build-arg ACCESS_KEY_S3=${ACCESS_KEY_S3} \
        --build-arg SECRET_KEY_S3=${SECRET_KEY_S3} \
        --build-arg STATIC_BUCKET=${STATIC_BUCKET} \
        --build-arg SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN} \
        --build-arg TAG=${CI_COMMIT_SHORT_SHA} \
        --target assets_upload \
        --tag ${TARGET_IMAGE} .
    - docker push ${TARGET_IMAGE}

    # build and push cache image
    - |
      docker build \
        --file config/docker/deploy/frontend.dockerfile \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --target cache \
        --tag ${CACHE_IMAGE} .
    - docker push ${CACHE_IMAGE}


.build-app-scripts: &build-app-scripts
    # build and push app image
    - |
      docker build \
        --file config/docker/deploy/app.dockerfile \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --build-arg FRONTEND_IMAGE=${FRONTEND_IMAGE} \
        --build-arg BACKEND_IMAGE=${BACKEND_IMAGE} \
        --target app \
        --tag ${TARGET_IMAGE} .
    - docker push ${TARGET_IMAGE}

### === BUILD ===

## ==> Build images for dev env
build-backend-dev:
    stage: build
    <<: *default_runner
    only: [master]
    variables:
        # We are storing images for dev and aws (prod) environments in the same registry
        CACHE_IMAGE: ${IMAGE_EDO_BACKEND}:cache
        TARGET_IMAGE: ${IMAGE_EDO_BACKEND}:${IMAGE_TAG_DEV}
        APP_LEVEL: dev
        APP_SECRETS: ""
        OSPLM_CONFIG: /work/config/vchasno/dev/eusign
    script:
        - *build-backend-scripts

build-frontend-dev:
    stage: build
    <<: *arm_runner
    only: [master]
    variables:
        CACHE_IMAGE: ${IMAGE_EDO_FRONTEND}:cache
        TARGET_IMAGE: ${IMAGE_EDO_FRONTEND}:${IMAGE_TAG_DEV}
        ACCESS_KEY_S3: ${ACCESS_KEY_S3_DEV}
        SECRET_KEY_S3: ${SECRET_KEY_S3_DEV}
        APP_CONFIG: config/vchasno/dev/config.yaml
        APP_LEVEL: dev
        STATIC_BUCKET: edo-static-files
    script:
        - *build-frontend-scripts

build-app-dev:
    stage: build_step2
    <<: *default_runner
    only: [master]
    needs: [build-backend-dev, build-frontend-dev]
    variables:
        FRONTEND_IMAGE: ${IMAGE_EDO_FRONTEND}:${IMAGE_TAG_DEV}
        BACKEND_IMAGE: ${IMAGE_EDO_BACKEND}:${IMAGE_TAG_DEV}
        TARGET_IMAGE: ${IMAGE_EDO_APP}:${IMAGE_TAG_DEV}
    script:
        - *build-app-scripts

## ==> Build images for prod env
build-backend-prd:
    stage: build
    <<: *default_runner
    only:
      - /^v\d+\.\d+\.\d+\.\d+$/
    variables:
        # We are storing images for dev and aws (prod) environments in the same registry
        CACHE_IMAGE: ${IMAGE_EDO_BACKEND}:cache
        TARGET_IMAGE: ${IMAGE_EDO_BACKEND}:${IMAGE_TAG_PRD}
        APP_LEVEL: prod
        APP_SECRETS: ""
        OSPLM_CONFIG: /work/config/vchasno/prod/eusign
    script:
        - *build-backend-scripts

build-frontend-prd:
    stage: build
    <<: *arm_runner
    only:
        - /^v\d+\.\d+\.\d+\.\d+$/
    variables:
        CACHE_IMAGE: ${IMAGE_EDO_FRONTEND}:cache
        TARGET_IMAGE: ${IMAGE_EDO_FRONTEND}:${IMAGE_TAG_PRD}
        ACCESS_KEY_S3: ${ACCESS_KEY_S3_PRD}
        SECRET_KEY_S3: ${SECRET_KEY_S3_PRD}
        APP_CONFIG: config/vchasno/prod/config.yaml
        APP_LEVEL: prod
        STATIC_BUCKET: edo-static-files-prd
    script:
        - *build-frontend-scripts

build-app-prd:
    stage: build_step2
    <<: *default_runner
    only:
        - /^v\d+\.\d+\.\d+\.\d+$/
    needs: [build-backend-prd, build-frontend-prd]
    variables:
        FRONTEND_IMAGE: ${IMAGE_EDO_FRONTEND}:${IMAGE_TAG_PRD}
        BACKEND_IMAGE: ${IMAGE_EDO_BACKEND}:${IMAGE_TAG_PRD}
        TARGET_IMAGE: ${IMAGE_EDO_APP}:${IMAGE_TAG_PRD}
    script:
        - *build-app-scripts


### === DEPLOY ===

# Deploy to dev environment
deploy-dev:
  # defined in included .aws-argo-helm-deploy.gitlab-ci.yaml
  extends: ['.deploy-vchasno']
  stage: deploy
  <<: *default_runner
  needs: [build-app-dev]
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  variables:
    APP_NAME: "edo-service"
    ARGO_ENV: "develop-aws"
    APP_RELEASE_VERSION: ${IMAGE_TAG_DEV}
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  tags:
    - edo-gl-runner

# Deploy to prod environment
deploy-prd:
  # defined in included .aws-argo-helm-deploy.gitlab-ci.yaml
  extends: ['.deploy-vchasno']
  stage: deploy
  <<: *default_runner
  needs: [build-app-prd]
  when: manual
  rules:
    - if: '$CI_COMMIT_REF_NAME =~ /^v\d+\.\d+\.\d+\.\d+$/'
  variables:
    APP_NAME: "edo-service"
    ARGO_ENV: "production-aws"
    APP_RELEASE_VERSION: ${IMAGE_TAG_PRD}
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  tags:
    - edo-gl-runner

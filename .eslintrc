{
    "env": {
        "browser": true,
        "mocha": true
    },
    "extends": [
        "prettier",
        "plugin:@typescript-eslint/recommended",
        "plugin:@tanstack/eslint-plugin-query/recommended"
    ],
    "globals": {
        "BUILD_UUID": true,
        "TOKENS_PUBLIC_KEY": true,
        "config": true,
        "TAG": true
    },
    "parser": "@typescript-eslint/parser",
    "plugins": ["prettier", "@typescript-eslint", "unused-imports"],
    "parserOptions": {
        "warnOnUnsupportedTypeScriptVersion": false
    },
    "rules": {
        "arrow-parens": [0, "as-needed"],
        "complexity": ["error", 25],
        "import/no-named-as-default-member": 0,
        "jsx-a11y/label-has-for": 0,
        "jsx-a11y/no-static-element-interactions": 0,
        "no-await-in-loop": 0,
        "no-plusplus": [
            0,
            {
                "allowForLoopAfterthoughts": true
            }
        ],
        "no-return-await": 0,
        "react/forbid-prop-types": 0,
        "react/jsx-filename-extension": 0,
        "react/no-unescaped-entities": 0,
        "react/no-unused-prop-types": 0,
        "react/require-default-props": 0,
        "value-keyword-case": 0,
        // TODO fix these rules after webpack update
        "camelcase": 0,
        "function-paren-newline": 0,
        "implicit-arrow-linebreak": 0,
        "import/no-cycle": 0,
        "import/no-useless-path-segments": 0,
        "indent": 0,
        "jsx-a11y/anchor-is-valid": 0,
        "jsx-a11y/click-events-have-key-events": 0,
        "jsx-a11y/control-has-associated-label": 0,
        "jsx-a11y/iframe-has-title": 0,
        "jsx-a11y/label-has-associated-control": 0,
        "jsx-a11y/no-autofocus": 0,
        "jsx-a11y/no-noninteractive-element-interactions": 0,
        "jsx-a11y/no-noninteractive-tabindex": 0,
        "lines-between-class-members": 0,
        "no-async-promise-executor": 0,
        "no-control-regex": 0,
        "no-else-return": 0,
        "no-multi-spaces": 0,
        "no-multiple-empty-lines": 0,
        "no-restricted-globals": 0,
        "no-self-assign": 0,
        "no-useless-rename": 0,
        "object-curly-newline": 0,
        "operator-linebreak": 0,
        "padded-blocks": 0,
        "prefer-destructuring": 0,
        "prefer-object-spread": 0,
        "prefer-promise-reject-errors": 0,
        "react/button-has-type": 0,
        "react/default-props-match-prop-types": 0,
        "react/destructuring-assignment": 0,
        "react/jsx-closing-tag-location": 0,
        "react/jsx-curly-brace-presence": 0,
        "react/jsx-curly-newline": 0,
        "react/jsx-fragments": 0,
        "react/jsx-indent": 0,
        "react/jsx-max-props-per-line": 0,
        "react/jsx-one-expression-per-line": 0,
        "react/jsx-props-no-spreading": 0,
        "react/jsx-tag-spacing": 0,
        "react/jsx-wrap-multilines": 0,
        "react/no-access-state-in-setstate": 0,
        "react/no-deprecated": 0,
        "react/no-unused-state": 0,
        "react/prefer-stateless-function": 0,
        "react/prop-types": 0,
        "react/sort-comp": 0,
        "react/state-in-constructor": 0,
        "react/static-property-placement": 0,
        "prettier/prettier": "error",
        "no-use-before-define": "off",
        "no-shadow": "off",
        "@typescript-eslint/ban-ts-comment": "off",
        "@typescript-eslint/no-shadow": ["error"],
        "@typescript-eslint/explicit-module-boundary-types": "off",
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": [
            "warn",
            {
                "vars": "all",
                "varsIgnorePattern": "^_",
                "args": "after-used",
                "argsIgnorePattern": "^_"
            }
        ]
    }
}

aiobotocore==2.21.1
elasticsearch==8.12.1
aiohttp[speedups]==3.12.12
aiohttp-middlewares~=2.4.0
aiohttp-session==2.12.1
aiokafka~=0.12.0
aiopg==v1.5.0a1
redis[hiredis]==5.0.6
aiosmtplib~=4.0.0
alembic==1.12.0
argon2-cffi~=23.1.0
cffi~=1.17.1
Babel~=2.14.0
bcrypt<4
certifi==2025.6.15
charset-normalizer==2.1.1
ciso8601<3
cryptography==41.0.4
dicttoxml2==2.1.0
elasticmagic==0.1.0b2
Hiku<1
inflection<1
itsdangerous<2
Jinja2
jsonrpcclient==4.0.2
lxml~=5.2.2
multidict~=6.1.0
phonenumbers<9
pebble~=5.1.1
pikepdf~=9.4.0
pymupdf~=1.26.0
Pillow~=10.4.0
prometheus_client<1
protobuf<4
psycopg2-binary~=2.9.10
pydantic~=2.9.2
email-validator==2.1.1  # used by pydantic for email validation
PyJWT~=2.8.0
python-mimeparse~=2.0.0
pytz
PyYAML<7
reportlab~=3.6.13
schedule<2
setuptools~=75.3.0
SQLAlchemy<1.4
sqlalchemy-citext
# trafaret<2310
https://github.com/oleksandr-kuzmenko/trafaret/archive/py310.zip
trafaret_validator<1
translitua<2
ua-parser<1
ujson~=5.8.0
uvloop~=0.21.0
wrapt<2
xlwt<2
xmltodict<1
yarl~=1.18.0
openpyxl<4
qrcode<7
httpx~=0.27.2
sentry-sdk==1.34.0
stream-unzip==0.0.91
stream-zip==0.0.83
# manipulations with docx files, used in template -> document upload
python-docx==1.1.2

# update carefully, because aiohttp integration for google-auth
# is experimental and could be broken between minor updates
google-auth[aiohttp]==v2.15.0

# EVO
evo-featureflags-client==0.7.0

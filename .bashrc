alias ls='ls --color=auto'
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'


# Run the watchfiles command with the provided arguments
function wt {
    if [ -z "$1" ]; then
        echo "Usage: wt <arguments>"
        echo "wt -k test_something"
        echo "wt app/auth/tests/test_auth_views.py -k test_modify_corporate_domains"
        return 1
    fi
    pytest_command="pytest $@"
    watchfiles --filter python "$pytest_command" app api cron worker conftest.py
}

# Run pytest with the -n auto flag to run tests in parallel
# or with the provided arguments
function pytest {
    if [ -z "$1" ]; then
        /usr/local/bin/pytest -n $PROCESSING_UNITS_NUMBER
    else
        /usr/local/bin/pytest $@
    fi
}

# Run pytest with logging enabled
function pytest_log {
    /usr/local/bin/pytest --log-cli-level=INFO $@
}

# Add color to the prompt
PS1='\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ '

# Print help for available commands
echo "Available helpers:"
echo "- $(echo -e '\033[1mwt <arguments>\033[0m')  # Run watchfiles with pytest"
echo "- $(echo -e '\033[1mpytest [arguments]\033[0m')  # Run pytest with arguments or in parallel"
echo "- $(echo -e '\033[1mpytest_log [arguments]\033[0m')  # Run pytest with logging enabled"

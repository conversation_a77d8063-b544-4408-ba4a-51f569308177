const fs = require('fs');
const path = require('path');

const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
const {
    ModuleFederationPlugin,
} = require('@module-federation/enhanced/webpack');
const deps = require('./package.json').dependencies;
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MomentLocalesPlugin = require('moment-locales-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const { sentryWebpackPlugin } = require('@sentry/webpack-plugin');
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');

const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const postcssCustomProperties = require('postcss-custom-properties');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
const webpack = require('webpack');
const yamljs = require('yamljs');
const merge = require('lodash/merge');

const pageConfigs = require('./cs/lib/pageConfigs');
const { baseHtml } = require('./cs/lib/html');
// const pdfViewerHtml = require('./cs/lib/pdfViewerHtml'); стабільна версія pdfViewer
const pdfViewerV4Html = require('./cs/lib/pdfViewerHtml.v4');

const getCSConfig = require('./cs/config/config');

const LEVEL = process.env.VCHASNO_LEVEL || 'local';

const CONFIG_FILE =
    process.env.VCHASNO_CONFIG || `./config/vchasno/${LEVEL}/config.yaml`;
const config = yamljs.load(CONFIG_FILE);

// Для локального середовища можна створити файл config.override.yaml, який не
// зберігається в git і в ньому можна переписати будь-які налаштування поверх
// config.yaml, щоб протестувати різні варіанти конфігів локально.
if (LEVEL === 'local') {
    const OVERRIDE_CONFIG_FILE = `./config/vchasno/local/config.override.yaml`;
    if (fs.existsSync(OVERRIDE_CONFIG_FILE)) {
        const localConfig = yamljs.load(OVERRIDE_CONFIG_FILE);
        merge(config, localConfig);
    }
}

const ANALYZE_BUNDLE = process.env.ANALYZE_BUNDLE;
const NODE_ENV = process.env.NODE_ENV || 'development';
const IS_PRODUCTION = NODE_ENV === 'production';
const USE_HMR = process.env.USE_HMR === '1'; // hot module replacement (hot reloading)
const NODE_MODULES_DIR = path.join(
    process.env.PROJECT_DEPS || __dirname,
    'node_modules',
);
const TAG = process.env.TAG || '';
const TOKENS_PUBLIC_KEY = fs.readFileSync(config.tokens.public_key).toString();
const SENTRY_AUTH_TOKEN = process.env.SENTRY_AUTH_TOKEN;

// Ось так так виглядають конфіги для локального та продакшн середовища:
// development.static_host: http://localhost:8000/static
// development.static_prefix: /static
// production.static_host: https://edo-dev.vchasno.com.ua/cloud-cgi/static/edo-static-files
// production.static_prefix: https://edo-dev.vchasno.com.ua/cloud-cgi/static/edo-static-files
const STATIC_HOST = config.app.static_host || '/static';
const STATIC_PREFIX = config.app.static_prefix || '/static';

// Для hot reloading (HMR) ми використовуємо STATIC_PREFIX, щоб webpack генерував відносні шляхи до
// статичних файлів і вони проходили через webpack-dev-server (localhost:8900), а не через бекенд
// сервер (localhost:8000).
const PUBLIC_PATH = USE_HMR ? `${STATIC_PREFIX}/` : `${STATIC_HOST}/`;

const smp = new SpeedMeasurePlugin({ disable: !ANALYZE_BUNDLE });

const entryPoint = (name, suffix = 'bootstrap.ts') => {
    return [`./${name}.${suffix}`];
};

const getHTMLTemplate = (entry, csConfig) => {
    switch (entry) {
        case 'pdfViewer':
            return pdfViewerV4Html(csConfig);
        default:
            return baseHtml({
                config: csConfig,
                htmlConfig: pageConfigs(csConfig)[entry] || {},
            });
    }
};

const htmlPlugin = (entry, fileName = '') => {
    const csConfig = getCSConfig(config, true);

    return new HtmlWebpackPlugin({
        filename: `html/${fileName || entry}.html`,
        inject: 'body',
        scriptLoading: 'defer',
        publicPath: PUBLIC_PATH,
        templateContent: getHTMLTemplate(entry, csConfig),
        chunks: ['runtime', entry],
        minify: IS_PRODUCTION,
    });
};

const ENTRIES = {
    app: {
        entry: entryPoint('app'),
        plugins: [htmlPlugin('app')],
    },
    auth: {
        entry: entryPoint('auth'),
        plugins: [htmlPlugin('auth')],
    },
    agreement: {
        entry: entryPoint('agreement'),
        plugins: [htmlPlugin('agreement')],
    },
    privacy: {
        entry: entryPoint('privacy'),
        plugins: [htmlPlugin('privacy')],
    },
    useOfBonuses: {
        entry: entryPoint('useOfBonuses'),
        plugins: [htmlPlugin('useOfBonuses', 'use_of_bonuses')],
    },
    pdfViewerHtml: {
        entry: entryPoint('pdfViewer'),
        plugins: [htmlPlugin('pdfViewer', 'pdf_viewer')],
    },
    imageViewer: {
        entry: entryPoint('imageViewer'),
        plugins: [htmlPlugin('imageViewer', 'image_viewer')],
    },
    txtXmlViewer: {
        entry: entryPoint('txtXmlViewer'),
        plugins: [htmlPlugin('txtXmlViewer', 'txt_xml_viewer')],
    },
    templateBuilder: {
        entry: entryPoint('templateBuilder'),
        plugins: [htmlPlugin('templateBuilder', 'template_builder')],
    },
    officeViewer: {
        entry: entryPoint('officeViewer'),
        plugins: [htmlPlugin('officeViewer', 'office_viewer')],
    },
    billGeneration: {
        entry: entryPoint('billGeneration'),
        plugins: [htmlPlugin('billGeneration', 'bill_generation')],
    },
    canceledSubscription: {
        entry: entryPoint('canceledSubscription'),
        plugins: [htmlPlugin('canceledSubscription', 'canceled_subscription')],
    },
    downloadNewBrowser: {
        entry: entryPoint('downloadNewBrowser'),
        plugins: [htmlPlugin('downloadNewBrowser', 'download_new_browser')],
    },
    error: {
        entry: entryPoint('error'),
        plugins: [htmlPlugin('error')],
    },
    invalidIp: {
        entry: entryPoint('invalidIp'),
        plugins: [htmlPlugin('invalidIp', 'invalid_ip')],
    },
    invalidToken: {
        entry: entryPoint('invalidToken'),
        plugins: [htmlPlugin('invalidToken', 'invalid_token')],
    },
    unsubscribe: {
        entry: entryPoint('unsubscribe'),
        plugins: [htmlPlugin('unsubscribe')],
    },
    offer: {
        entry: entryPoint('offer'),
        plugins: [htmlPlugin('offer')],
    },
};

const entry = Object.keys(ENTRIES).reduce((acc, key) => {
    acc[key] = ENTRIES[key].entry;
    return acc;
}, {});

/* eslint-disable no-console */
console.dir({
    CONFIG_FILE,
    LEVEL,
    NODE_ENV,
    TAG,
    NODE_MODULES_DIR,
    IS_PRODUCTION,
    SENTRY_AUTH_TOKEN: !!SENTRY_AUTH_TOKEN,
    CS_CONFIG: getCSConfig(config, true),
});
/* eslint-enable no-console */

module.exports = smp.wrap({
    mode: NODE_ENV,
    context: path.join(__dirname, 'cs', 'pages'),
    entry,
    output: {
        filename: IS_PRODUCTION
            ? 'js/[name].[contenthash].min.js'
            : 'js/[name].[contenthash].js',
        chunkFilename: IS_PRODUCTION
            ? 'js/[name].[contenthash].min.js'
            : 'js/[name].[contenthash].js',
        path: path.join(__dirname, 'static'),
        libraryTarget: 'umd',
        // Важливо, щоб publicPath мав слеш `/` в кінці, інакше вийде
        // щось типу /staticjs/app.js, а має бути /static/js/app.js
        publicPath: PUBLIC_PATH,
    },
    stats: {
        assetsSort: 'name',
        cached: false,
        cachedAssets: false,
        children: false,
        chunks: false,
        chunkModules: false,
        chunkOrigins: false,
        modules: false,
        publicPath: false,
        reasons: false,
        source: false,
    },
    resolve: {
        alias: {
            // https://github.com/ag-grid/ag-grid/issues/7803
            'react-dom/server': 'react-dom/server.js',
            'gql-types': path.resolve(__dirname, 'cs/gql/graphql.ts'),
            ui: path.resolve(__dirname, 'cs/components/ui/'),
            mf: path.resolve(__dirname, 'cs/mf/'),
            components: path.resolve(__dirname, 'cs/components/'),
            hooks: path.resolve(__dirname, 'cs/hooks/'),
            contexts: path.resolve(__dirname, 'cs/contexts/'),
            selectors: path.resolve(__dirname, 'cs/selectors/'),
            icons: path.resolve(__dirname, 'cs/icons/'),
            store: path.resolve(__dirname, 'cs/store/'),
            lib: path.resolve(__dirname, 'cs/lib/'),
            services: path.resolve(__dirname, 'cs/services/'),
            types: path.resolve(__dirname, 'cs/types/'),
            records: path.resolve(__dirname, 'cs/records/'),
        },
        modules: [path.join(__dirname, 'cs'), 'node_modules', NODE_MODULES_DIR],
        extensions: ['.ts', '.tsx', '.js', '.jsx', '.css'],
        fallback: {
            //https://github.com/facebook/react/issues/20235
            'react/jsx-runtime': 'react/jsx-runtime.js',
            'react/jsx-dev-runtime': 'react/jsx-dev-runtime.js',
            fs: false,
            tls: false,
            net: false,
            path: false,
            zlib: false,
            http: false,
            https: false,
            buffer: false,
            stream: require.resolve('stream-browserify'),
            crypto: false,
        },
    },
    module: {
        rules: [
            {
                test: /\.(js|mjs|jsx|ts|tsx)$/,
                exclude: /node_modules/,
                use: [
                    {
                        loader: 'swc-loader',
                        ...(USE_HMR
                            ? {
                                  // не впевнений чи можна ці налаштування використовувати
                                  // для інших випадків окрім як HMR. Їх взяв з документації
                                  // react-refresh-webpack-plugin
                                  options: {
                                      jsc: {
                                          transform: {
                                              react: {
                                                  development: true,
                                                  refresh: true,
                                              },
                                          },
                                      },
                                  },
                              }
                            : {}),
                    },
                ],
            },
            {
                test: /\.css$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    {
                        loader: 'css-loader',
                        options: {
                            import: true,
                            modules: {
                                mode: (resourcePath) => {
                                    if (
                                        /\/node_modules\//i.test(resourcePath)
                                    ) {
                                        return 'global';
                                    }

                                    if (/.global.css$/i.test(resourcePath)) {
                                        return 'global';
                                    }

                                    return 'local';
                                },
                                localIdentName:
                                    '[name]__[local]__[hash:base64:5]',
                            },
                            importLoaders: 2,
                        },
                    },
                    {
                        loader: 'postcss-loader',
                        options: {
                            postcssOptions: {
                                plugins: [
                                    ['postcss-preset-env'],
                                    postcssCustomProperties({
                                        preserve: true,
                                        importFrom: path.join(
                                            __dirname,
                                            'cs',
                                            'styles',
                                            'assets',
                                            '_var.css',
                                        ),
                                    }),
                                ],
                            },
                        },
                    },
                ],
            },
            {
                test: /\.svg$/,
                exclude: /node_modules/,
                use: [
                    {
                        loader: 'svg-sprite-loader',
                        options: {
                            symbolId: '[name]_[hash:base64:5]',
                            prefixize: true,
                        },
                    },
                ],
            },
            {
                test: /\.po$/,
                exclude: /node_modules/,
                use: ['json-loader', 'po-gettext-loader'],
            },
            {
                test: /\.(woff|woff2|eot|ttf|otf)$/,
                type: 'asset/resource',
                generator: {
                    filename: IS_PRODUCTION
                        ? 'assets/fonts/[name][contenthash][ext]'
                        : 'assets/fonts/[name][ext]',
                },
            },
            {
                test: /\.(png|jpg|jpeg|gif)$/,
                oneOf: [
                    {
                        resourceQuery: '',
                        type: 'asset/resource',
                        generator: {
                            filename: IS_PRODUCTION
                                ? 'assets/images/[name][contenthash][ext]'
                                : 'assets/images/[name][ext]',
                        },
                    },
                    {
                        resourceQuery: /base64/,
                        type: 'asset/inline',
                    },
                ],
            },
            {
                test: /\.(pdf)$/,
                oneOf: [
                    {
                        resourceQuery: '',
                        type: 'asset/resource',
                        generator: {
                            filename: IS_PRODUCTION
                                ? 'assets/pdf/[name][contenthash][ext]'
                                : 'assets/pdf/[name][ext]',
                        },
                    },
                ],
            },
        ],
    },
    optimization: {
        usedExports: true,
        minimize: IS_PRODUCTION,
        chunkIds: 'named',
        minimizer: [
            new TerserPlugin({
                minify: TerserPlugin.swcMinify,
            }),
            new CssMinimizerPlugin(),
        ],
    },
    resolveLoader: {
        modules: ['node_modules', NODE_MODULES_DIR],
    },
    // remove source maps in production after load to sentry
    devtool: IS_PRODUCTION ? 'source-map' : 'eval-cheap-module-source-map',
    plugins: [
        new ModuleFederationPlugin({
            name: 'edo',
            filename: 'remoteEntry.[contenthash].js',
            library: {
                type: 'window',
                name: 'edo',
            },
            shared: {
                react: {
                    singleton: true,
                    requiredVersion: deps.react,
                },
                'react-dom': {
                    singleton: true,
                    requiredVersion: deps['react-dom'],
                },
            },
        }),
        new MomentLocalesPlugin({
            localesToKeep: ['uk'],
        }),
        sentryWebpackPlugin({
            org: 'vchasno',
            url: 'https://sentry.vchasno.com.ua',
            project: 'edo-front',
            disable: LEVEL !== 'prod', // завантажуємо на sentry тільки prod версію
            authToken: SENTRY_AUTH_TOKEN,
            silent: true,
            errorHandler: (err) => {
                console.dir({ webpackPlugin: 'sentryWebpackPlugin', err });
            },
            release: {
                name: TAG,
                deploy: {
                    env: LEVEL,
                },
            },
            sourcemaps: {
                assets: 'static/js/**/*.js*',
                ignore: ['static/js/lib/**/*'],
                ...(LEVEL === 'prod' && {
                    filesToDeleteAfterUpload: [
                        'static/**/*.js.map',
                        'static/**/*.css.map',
                    ],
                }),
            },
        }),
        new CleanWebpackPlugin({
            cleanStaleWebpackAssets: false,
            cleanOnceBeforeBuildPatterns: [
                'css/*.css',
                'html/**/*',
                'js/**/*',
                'assets/**/*',
                './remoteEntry.*.js',
            ],
        }),
        new MiniCssExtractPlugin({
            ignoreOrder: true, // when use css modules order is not important
            filename: USE_HMR
                ? 'css/[name].css'
                : 'css/[name].[contenthash].css',
        }),
        new CopyPlugin({
            patterns: [
                {
                    from: path.join(__dirname, 'cs/lib/vendor/'),
                    to: path.join(__dirname, 'static/js/lib/'),
                },
            ],
        }),
        USE_HMR ? new ReactRefreshWebpackPlugin() : null,
        !IS_PRODUCTION
            ? new ForkTsCheckerWebpackPlugin({
                  typescript: {
                      configFile: path.join(__dirname, '/tsconfig.json'),
                  },
                  // Це дозволяє не зупиняти збірку, якщо є помилки в TypeScript
                  devServer: false,
              })
            : null,
        new webpack.DefinePlugin({
            TOKENS_PUBLIC_KEY: JSON.stringify(TOKENS_PUBLIC_KEY),
            config: getCSConfig(config),
            'process.env.NODE_ENV': JSON.stringify(NODE_ENV),
            TAG: JSON.stringify(TAG),
            LEVEL: JSON.stringify(LEVEL),
        }),
        ...Object.values(ENTRIES)
            .map(({ plugins }) => plugins)
            .flat(),
        new BundleAnalyzerPlugin({
            analyzerMode: ANALYZE_BUNDLE ? 'server' : 'disabled',
        }),
    ].filter(Boolean), // remove empty plugins

    /* ------------------------------------------------------------------
     * Webpack‑Dev‑Server only (activated by USE_HMR=1)
     * ------------------------------------------------------------------*/
    ...(USE_HMR && {
        devServer: {
            static: {
                directory: path.join(__dirname, 'static'),
                publicPath: PUBLIC_PATH,
            },
            port: 8009,
            host: '0.0.0.0',
            hot: true,
            liveReload: false,
            client: {
                logging: 'info',
                overlay: { errors: true, warnings: false },
            },
            allowedHosts: 'all',
            headers: { 'Access-Control-Allow-Origin': '*' },
            proxy: [
                {
                    // Всі запити, окрім шляхів /static/*, повинні йти на бекенд сервер.
                    // На бекенд також мають потрапити запити на HTML сторінки, бо перед
                    // тим як віддати HTML сторінку, бекенд робить додаткову логіку і
                    // перенаправляє на потрібну сторінку за потреби.
                    context: (path) => !path.startsWith(STATIC_PREFIX),
                    target: 'http://localhost:8000',
                    changeOrigin: true,
                    secure: false,
                    cookieDomainRewrite: 'localhost', // keep cookies working
                },
            ],
            devMiddleware: {
                publicPath: PUBLIC_PATH,
                // В HMR режимі файли не записуються на диск, а віддаються з пам'яті.
                // Але щоб бекенд міг віддавати HTML сторінки, ми їх записуємо на диск.
                // JS, CSS та інші файли не записуються на диск, вони віддаються з пам'яті.
                writeToDisk: (filePath) => filePath.endsWith('.html'),
            },
        },
    }),
});

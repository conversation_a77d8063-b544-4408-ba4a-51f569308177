^[Devs]
* @a-k<PERSON><PERSON><PERSON> @b.dobrovolskyi


^[App]
app/app.py                      @s.ivashchenko
app/config.py                   @s.ivashchenko
app/signals.py                  @s.ivashchenko
app/auth/                       @s.ivashchenko
app/billing/                    @s.ivashchenko
app/contacts/                   @b.dobrovolskyi
app/documents/                  @s.ivashchenko
app/document_template/          @s.ivashchenko
app/document_versions/          @b.dobrovolskyi
app/drafts/                     @b.dobrovolskyi
app/es/                         @s.ivashchenko
app/lib/                        @s.ivashchenko
app/models/migrations/          @s.ivashchenko
app/sign_sessions/              @b.dobrovolskyi
app/tags/                       @s.ivashchenko
app/uploads/                    @s.ivashchenko

# app/actions/
# app/banner/
# app/conversions/
# app/flags/
# app/landing/
# app/miscellaneous/
# app/notifications/
# app/proxy/
# app/statistics/
# app/telegram/
# app/templates/
# app/tests/
# app/tokens/
# app/vendor/


^[Worker]
worker/billing/                 @s.ivashchenko
worker/companies/               @b.dobrovolskyi
worker/crm/                     @b.dobrovolskyi
worker/document_template/       @b.dobrovolskyi
worker/downloading/             @s.ivashchenko
worker/service/                 @b.dobrovolskyi
worker/tags/                    @s.ivashchenko

# worker/banner/
# worker/contacts/
# worker/emailing/
# worker/migrations/
# worker/notifications/
# worker/trigger_notifications/


^[Api]
api/*.py
api/downloads/                  @s.ivashchenko
api/graph/                      @s.ivashchenko
api/public/                     @s.ivashchenko
api/private/blackbox/           @b.dobrovolskyi
api/private/pos/                @s.ivashchenko

# api/


^[Frontend]
*.js *.jsx *.ts *.tsx *.styl    @m.slonevskyi @aleksei.boiko


^[Infra]
vagga.yaml                      @s.ivashchenko @a.kuzmenko
config/                         @s.ivashchenko @a.kuzmenko
indexator/                      @s.ivashchenko @a.kuzmenko
logs/                           @s.ivashchenko @a.kuzmenko
vagga/                          @s.ivashchenko @a.kuzmenko

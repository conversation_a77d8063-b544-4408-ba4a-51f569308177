{"extends": ["stylelint-config-standard", "stylelint-config-idiomatic-order", "stylelint-config-prettier"], "rules": {"indentation": 4, "property-no-unknown": [true, {"ignoreProperties": ["text-fill-color", "/^container/"]}], "font-family-no-missing-generic-family-keyword": [true, {"ignoreFontFamilies": "Roboto"}], "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["export", "import", "global", "local"]}], "no-descending-specificity": null, "at-rule-no-unknown": [true, {"ignoreAtRules": ["/container/"]}]}}
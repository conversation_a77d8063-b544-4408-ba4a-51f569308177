export enum RequestStatus {
    UNCALLED,
    PENDING,
    SUCCESS,
    FAILUR<PERSON>,
}

export interface RequestState {
    status: RequestStatus;
    apiError?: string;
}

export const isRequestPending = (request: RequestState) =>
    request.status === RequestStatus.PENDING;

export type ApiErrorDetails = Record<string, unknown>;

export interface ApiError<T = ApiErrorDetails> {
    status: number;
    code: string;
    reason: string;
    details: T;
}

export const isApiError = (error: unknown): error is ApiError => {
    if (typeof error !== 'object' || error === null) return false;
    return (
        'status' in error &&
        'code' in error &&
        'reason' in error &&
        'details' in error
    );
};

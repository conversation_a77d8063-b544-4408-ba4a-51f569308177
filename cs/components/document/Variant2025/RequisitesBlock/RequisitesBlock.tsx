import React from 'react';
import { FormProvider } from 'react-hook-form';
import { SubmitHandler } from 'react-hook-form';
import { useSelector } from 'react-redux';

import { FlexBox, Title, snackbarToast } from '@vchasno/ui-kit';

import cn from 'classnames';
import { editFormToPayload } from 'components/DocumentEdit/mappers';
import EditIcon from 'components/document/Variant2025/EditIcon';
import LineDivider from 'components/document/Variant2025/MainDocumentSidebar/LineDivider';
import SidebarTabHeader from 'components/document/Variant2025/MainDocumentSidebar/SidebarTabHeader';
import DisplayDetailsInfo from 'components/document/Variant2025/RequisitesBlock/DisplayDetailsInfo';
import DisplayGeneralFields from 'components/document/Variant2025/RequisitesBlock/DisplayGeneralFields';
import { apiErrorMapper } from 'components/document/Variant2025/RequisitesBlock/apiErrorMapper';
import { useDocument } from 'components/document/Variant2025/useDocument';
import { useDocumentActions } from 'components/document/useDocumentActions';
import { getCurrentUser } from 'selectors/app.selectors';
import { updateDocument } from 'services/documents/ts/api';
import { t } from 'ttag';

import { isApiError } from '../../../../types/request';
import { RequisiteForm } from './types';

import DisplayAdditionalFields from './DisplayAdditionalFields';
import RequisitesForm from './RequisitesForm';
import { useDisableRequisiteFields } from './useDisableRequisiteFields';
import { useRequisiteForm } from './useRequisiteForm';

import css from './RequisitesBlock.css';

export interface RequisitesBlockProps {
    className?: string;
}

const RequisitesBlock: React.FC<RequisitesBlockProps> = ({ className }) => {
    const documentActions = useDocumentActions();
    const methods = useRequisiteForm();
    const doc = useDocument();
    const user = useSelector(getCurrentUser);
    const [editMode, setEditMode] = React.useState(false);
    const disableEditFields = useDisableRequisiteFields();

    const hasPaidRate = methods.watch('isPayedRate');

    const isSomethingCanBeEdit =
        Object.values(disableEditFields).some((disable) => !disable) ||
        hasPaidRate;

    const isParametersExists =
        Array.isArray(doc.parameters) && doc.parameters.length > 0;

    const handleEditMode = () => {
        if (isSomethingCanBeEdit) {
            setEditMode((prev) => !prev);
        }
    };

    const onSubmit: SubmitHandler<RequisiteForm> = async (form) => {
        const payloadToUpdate = editFormToPayload({
            doc,
            init: methods.formState.defaultValues,
            form,
            user,
        });

        if (Object.keys(payloadToUpdate).length === 0) {
            handleEditMode();
            return;
        }

        const handleApiError = (error: unknown) => {
            if (isApiError(error)) {
                const invalidFormField = apiErrorMapper(error);

                if (invalidFormField) {
                    methods.setError(invalidFormField, {
                        message: error.reason,
                        type: 'api-error',
                    });
                }

                snackbarToast.error(error.reason);
                return;
            }

            snackbarToast.error(t`Не вдалося зберегти зміни`);
        };

        try {
            await updateDocument(doc.id, payloadToUpdate);
            documentActions.onLoadDocument(doc.id, false);

            handleEditMode();
            snackbarToast.success(t`Зміни було успішно збережено`);
        } catch (error: unknown) {
            handleApiError(error);
        }
    };

    return (
        <>
            <SidebarTabHeader>
                {(activeTab) => (
                    <Title
                        className={css.title}
                        level={4}
                        onClick={handleEditMode}
                    >
                        {activeTab.title} {isSomethingCanBeEdit && <EditIcon />}
                    </Title>
                )}
            </SidebarTabHeader>
            {!editMode && (
                <FlexBox
                    direction="column"
                    gap={32}
                    className={cn(css.root, className)}
                >
                    <DisplayGeneralFields />
                    {isParametersExists && (
                        <>
                            <LineDivider />
                            <DisplayAdditionalFields />
                        </>
                    )}
                    <DisplayDetailsInfo />
                </FlexBox>
            )}
            {editMode && (
                <FormProvider {...methods}>
                    <RequisitesForm
                        onClose={handleEditMode}
                        onSubmit={onSubmit}
                    />
                </FormProvider>
            )}
        </>
    );
};

export default RequisitesBlock;

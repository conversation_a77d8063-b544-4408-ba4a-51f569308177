import { EditDocumentForm } from 'components/DocumentEdit/types';

import { ApiError } from '../../../../types/request';

export const apiErrorMapper = (
    error: ApiError,
): keyof EditDocumentForm | null => {
    // тут ми привязуємося до тексту помилки, бо бекенд не дає більш детальної інфи
    if (error.reason.includes('лише для внутрішніх документів')) {
        return 'category';
    }

    return null;
};

import React from 'react';

import { BlackTooltip, FlexBox, ScrollableBox } from '@vchasno/ui-kit';

import cn from 'classnames';
import AntivirusCheck from 'components/document/Variant2025/MainDocumentSidebar/AntivirusCheck';
import Icon from 'ui/icon';

import { useSidebarContext } from './context';

import css from './MainDocumentSidebar.css';

export interface MainDocumentSidebarProps {
    className?: string;
}

const MainDocumentSidebar = ({ className }: MainDocumentSidebarProps) => {
    const { tabs, activeTab, setActiveTabKey, headerRef } = useSidebarContext();

    return (
        <div data-area="sidebar" className={cn(css.root, className)}>
            <div className={css.tabs}>
                {tabs.map((tab) => (
                    <BlackTooltip
                        key={tab.key}
                        title={tab.tooltip || tab.title}
                        disableInteractive
                        placement="left"
                    >
                        <div
                            onClick={() => setActiveTabKey(tab.key)}
                            className={cn(css.tabBtn, {
                                [css.active]: activeTab?.key === tab.key,
                            })}
                        >
                            <Icon glyph={tab.icon} className={css.tabIcon} />
                            {tab.badge}
                        </div>
                    </BlackTooltip>
                ))}
                <span style={{ flexGrow: 1 }} />
                <AntivirusCheck className={css.antivirus} />
            </div>
            {activeTab && (
                <FlexBox direction="column" grow={1} gap={0}>
                    {/* це де вставляється через портал <SidebarTabHeader />*/}
                    <div className={css.header} ref={headerRef} />
                    {/* це де вставляється через портал <SidebarTabHeader />*/}
                    <ScrollableBox
                        hideScroll
                        shadow="vertical"
                        scrollHeight="calc(100% - 40px)"
                        contentClassName={css.scrollable}
                    >
                        {activeTab.content}
                    </ScrollableBox>
                </FlexBox>
            )}
        </div>
    );
};

export default MainDocumentSidebar;

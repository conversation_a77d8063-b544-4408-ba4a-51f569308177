repos:
-   repo: local
    hooks:
      -   id: ruff-formatter
          name: Format files using ruff formatter
          description: This hook formats files using ruff.
          entry: docker-compose run -T --rm ruff-formatter
          files: .*.py
          language: python

      -   id: ruff-linter
          name: Lint files using ruff linter
          description: This hook lints files using ruff.
          entry: docker-compose run -T --rm ruff-linter --fix
          files: .*.py
          language: python

      -   id: mypy
          name: Verify typing using mypy
          description: This hook verifies correct typing using mypy.
          entry: docker-compose run -T --rm mypy
          files: .*.py
          language: python

      -   id: translations
          name: Update translations
          description: This hook updates newly added translations.
          entry: docker-compose run -T --rm python-dev ./scripts/translations/update_catalog.py
          language: python

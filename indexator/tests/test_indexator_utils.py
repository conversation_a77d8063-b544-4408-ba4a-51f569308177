import time

from app.es.constants import (
    DOCUMENTS_LOCK_KEY,
    INDEXATOR_KEY,
    INDEXATOR_SLOW_KEY,
)
from app.es.utils import send_to_indexator
from app.services import services
from app.tests.common import TEST_UUID, TEST_UUID_2, prepare_client
from indexator import get_document_ids


async def test_get_document_ids(aiohttp_client):
    queue = INDEXATOR_KEY
    queue_slow = INDEXATOR_SLOW_KEY
    queue_lock = DOCUMENTS_LOCK_KEY

    await prepare_client(aiohttp_client)
    now = time.time()
    redis = services.redis
    doc_id_1 = TEST_UUID
    doc_id_2 = TEST_UUID_2

    # Simple test without locking
    await send_to_indexator(document_ids=[doc_id_1])
    doc_ids, queue_name = await get_document_ids(redis)
    assert len(doc_ids) == 1
    assert queue_name == queue

    # With locked doc_id less than 5min
    await redis.hset(queue_lock, mapping={doc_id_1: now})
    await send_to_indexator(document_ids=[doc_id_1])
    doc_ids, _ = await get_document_ids(redis)
    assert len(doc_ids) == 0

    # With locked doc_id more than 5min
    await redis.hset(queue_lock, mapping={doc_id_2: now - 500})
    doc_ids, queue_name = await get_document_ids(redis)
    assert len(doc_ids) == 1
    assert doc_ids[0] == doc_id_2
    assert queue_name == queue_slow

    locked = await redis.hgetall(queue_lock)
    assert len(locked) == 2
    doc_2_locked_time = float(locked[doc_id_2])
    # We refresh locking on that document
    assert doc_2_locked_time > now

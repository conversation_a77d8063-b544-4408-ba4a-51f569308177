import asyncio
import logging
import os
import signal

import uvloop
from prometheus_client import start_http_server as start_metrics_server

from app.app import create_app, setup_timezone
from app.services import services
from indexator import IndexatorWorker

logger = logging.getLogger(__name__)


class GracefulExit(SystemExit):
    code = 0


def raise_graceful_exit() -> None:
    raise GracefulExit()


async def main() -> None:
    asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

    setup_timezone()

    loop = asyncio.get_event_loop()

    web_app = create_app()
    worker = IndexatorWorker()

    if debug := services.config.app.debug:
        loop.set_debug(debug)
        logging.basicConfig(level=logging.DEBUG)

    if os.environ.get('K8S'):
        start_metrics_server(9100)

    loop.add_signal_handler(signal.SIGINT, raise_graceful_exit)
    loop.add_signal_handler(signal.SIGTERM, raise_graceful_exit)

    try:
        await web_app.startup()
        await worker.run(web_app)
    except (<PERSON><PERSON><PERSON><PERSON><PERSON>, KeyboardInterrupt):
        pass
    except Exception:
        logger.exception('Unhandled exception on running indexator')
        raise
    finally:
        await worker.stop()
        await web_app.shutdown()
        await web_app.cleanup()


if __name__ == '__main__':
    import logevo

    with logevo.main_context():
        asyncio.run(main())

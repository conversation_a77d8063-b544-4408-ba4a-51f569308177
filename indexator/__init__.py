import asyncio
import logging
import time
import uuid

import elasticsearch

# elasticmagic refers to BaseException, but it's changed in recent lib version
if not getattr(elasticsearch, 'ElasticsearchException', None):
    setattr(elasticsearch, 'ElasticsearchException', elasticsearch.ApiError)  # noqa

from aiohttp import web
from logevo import set_current_rxid

from app.es.constants import (
    DOCUMENTS_LOCK_KEY,
    INDEXATOR_ARCHIVE_KEY,
    INDEXATOR_DEAD_LETTER_KEY,
    INDEXATOR_KEY,
    INDEXATOR_LISTING_KEY,
    INDEXATOR_SLOW_KEY,
    INDEXATOR_TEMP_KEY,
)
from app.es.errors import BulkActionError
from app.es.indexation import sync_documents
from app.lib import tracking
from app.lib.types import Pipeline, Redis
from app.services import services, setup_services

logger = logging.getLogger('indexator')

BUCKET_SIZE = 500
EXCEPTION_DELAY_SECONDS = 3
LOCK_INDEXING_DOCUMENT_TIMEOUT = 300  # 5 min in seconds


async def _get_document_ids(redis: Redis, queue: str) -> list[str]:
    """
    There are possible race conditions during indexing documents:
    - We remove doc_id from indexing queue and start indexation process on it by worker1
      (which may remain even more than 10seconds).
    - During that period same doc_id may occur in indexation queue again.
      In that situation another worker2 might index it before worker1 finishes.
    To prevent such situations - we use separate KEY in Redis for storing doc_ids
    that is already in process of indexation.
    """

    async def _select_docs_for_indexation(pipe: Pipeline) -> list[str]:
        """
        That function is an argument to redis.transaction helper, keynotes:
        - pipe arg - redis.Pipeline object
        - we can get result of some read commands during transaction for further processing
        - need to call pipe.multi() before start writing any commands that change state in redis
        - don't need to call pipe.execute() in the end of a function, redis.transaction will manage
          all necessary stuff around pipeline automatically
        """
        now = time.time()

        doc_ids = await pipe.zrange(queue, 0, BUCKET_SIZE - 1)

        # select all locked_doc_ids with scores, where each score
        # represents timestamp when document was added to locking hashmap
        locked_doc_ids_with_scores = await pipe.hgetall(DOCUMENTS_LOCK_KEY)

        locked_doc_ids = set()
        put_to_slow_queue = set()
        put_to_current_queue = set()
        for doc_id, score in locked_doc_ids_with_scores.items():
            if float(score) < now - LOCK_INDEXING_DOCUMENT_TIMEOUT:
                # That document locked more than 5 minutes, put it to slow indexing queue
                # That situation may happen during indexator crashes
                put_to_slow_queue.add(doc_id)
            else:
                locked_doc_ids.add(doc_id)

        result_doc_ids = set()
        for doc_id in doc_ids:
            if doc_id in locked_doc_ids:
                put_to_current_queue.add(doc_id)
            elif doc_id in put_to_slow_queue:
                continue
            else:
                result_doc_ids.add(doc_id)

        pipe.multi()
        if result_doc_ids:
            pipe.zrem(queue, *result_doc_ids)
        if current_queue_list := list(put_to_current_queue):
            pipe.zadd(queue, {document_id: 1 for document_id in current_queue_list})
        if slow_queue_list := list(put_to_slow_queue):
            pipe.hdel(DOCUMENTS_LOCK_KEY, *slow_queue_list)
            pipe.zadd(INDEXATOR_SLOW_KEY, {document_id: 1 for document_id in slow_queue_list})
        if result_doc_ids:
            # add selected document_ids to locking hashmap
            pipe.hset(DOCUMENTS_LOCK_KEY, mapping={doc_id: now for doc_id in result_doc_ids})

        return list(result_doc_ids)

    # First_arg - function (callable), that use Redis.Pipeline as its argument
    # Second_arg - *watches, name of KEYs in Redis to "watch", if values in that KEYs
    #              changed before transaction finishes, transaction aborts and tries again
    #              after "watch_delay" seconds
    # value_from_callable = True - means that operation will return result of First_arg (function)
    return await redis.transaction(
        _select_docs_for_indexation,
        queue,
        value_from_callable=True,
        watch_delay=0.1,
    )


async def get_document_ids(redis: Redis) -> tuple[list[str], str]:
    if document_ids := await _get_document_ids(redis, INDEXATOR_KEY):
        return document_ids, INDEXATOR_KEY
    if document_ids := await _get_document_ids(redis, INDEXATOR_ARCHIVE_KEY):
        return document_ids, INDEXATOR_ARCHIVE_KEY
    if document_ids := await _get_document_ids(redis, INDEXATOR_SLOW_KEY):
        return document_ids, INDEXATOR_SLOW_KEY
    if document_ids := await _get_document_ids(redis, INDEXATOR_LISTING_KEY):
        return document_ids, INDEXATOR_LISTING_KEY
    if document_ids := await _get_document_ids(redis, INDEXATOR_DEAD_LETTER_KEY):
        return document_ids, INDEXATOR_DEAD_LETTER_KEY
    if document_ids := await _get_document_ids(redis, INDEXATOR_TEMP_KEY):
        return document_ids, INDEXATOR_TEMP_KEY

    return [], ''


async def sync_or_wait() -> None:
    redis = services.redis

    # use a pipeline to use one reqeust and one round-trip to redis
    # instead of several.
    # Do not use transaction here because don't want to lock these keys
    async with redis.pipeline(transaction=False) as pipe:
        pipe.zcard(INDEXATOR_KEY)
        pipe.zcard(INDEXATOR_SLOW_KEY)
        pipe.zcard(INDEXATOR_LISTING_KEY)
        pipe.zcard(INDEXATOR_ARCHIVE_KEY)
        pipe.zcard(INDEXATOR_DEAD_LETTER_KEY)
        pipe.zcard(INDEXATOR_TEMP_KEY)

        pipe.hlen(DOCUMENTS_LOCK_KEY)

        results = await pipe.execute()

    # queue size monitoring
    tracking.indexing_fast_size.set(results[0])
    tracking.indexing_slow_size.set(results[1])
    tracking.indexing_listing_size.set(results[2])
    tracking.indexing_archive_size.set(results[3])
    tracking.indexing_dead_size.set(results[4])
    tracking.indexing_temp_size.set(results[5])

    tracking.indexing_in_progress_size.set(results[6])

    document_ids, queue = await get_document_ids(redis)

    if not document_ids:
        await asyncio.sleep(0.3)
        return

    log_extra = {'count': len(document_ids), 'queue': queue}
    logger.info('Start indexation', extra=log_extra)

    try:
        # Generate index documents and send to Elastic
        await sync_documents(
            document_ids,
            is_listing_only=queue == INDEXATOR_LISTING_KEY,
            is_archive_only=queue == INDEXATOR_ARCHIVE_KEY,
        )
    except elasticsearch.exceptions.ConnectionTimeout:
        # In case of timeout wait a few seconds and put elements
        # at the top of the queue
        logger.warning('Indexation timeout')
        await asyncio.sleep(EXCEPTION_DELAY_SECONDS)
        async with redis.pipeline() as pipe:
            pipe.zadd(queue, {document_id: 1 for document_id in document_ids})
            pipe.hdel(DOCUMENTS_LOCK_KEY, *document_ids)
            await pipe.execute()
    except BulkActionError as err:
        # In case of BulkActionError we have some document_ids that successfully indexed
        # and some ids that failed
        logger.exception('Bulk indexation error', extra=log_extra)
        if queue == INDEXATOR_DEAD_LETTER_KEY:
            await asyncio.sleep(EXCEPTION_DELAY_SECONDS)
        async with redis.pipeline() as pipe:
            pipe.zadd(
                INDEXATOR_DEAD_LETTER_KEY,
                {document_id: 1 for document_id in err.failed_ids},
            )
            pipe.hdel(DOCUMENTS_LOCK_KEY, *document_ids)
            await pipe.execute()
    except Exception:
        logger.exception('Error indexation', extra=log_extra)
        # In case of unhandled error push documents to the dead letter queue.
        # If you receive unhandled error for dead messages, sleep few seconds to avoid
        # 100% CPU utilization.
        if queue == INDEXATOR_DEAD_LETTER_KEY:
            await asyncio.sleep(EXCEPTION_DELAY_SECONDS)
        async with redis.pipeline() as pipe:
            pipe.zadd(INDEXATOR_DEAD_LETTER_KEY, {document_id: 1 for document_id in document_ids})
            pipe.hdel(DOCUMENTS_LOCK_KEY, *document_ids)
            await pipe.execute()
    else:
        await redis.hdel(DOCUMENTS_LOCK_KEY, *document_ids)

    logger.info('Stop iteration of indexation', extra=log_extra)


class IndexatorWorker:
    def __init__(self) -> None:
        self._running = False
        self._canceled = False

    async def run(self, app: web.Application) -> None:
        logger.info('Start indexator')

        setup_services(app)
        while not self._canceled:
            with set_current_rxid(str(uuid.uuid4())):
                self._running = True
                try:
                    await sync_or_wait()
                finally:
                    self._running = False

    async def stop(self) -> None:
        self._canceled = True

        while self._running:
            await asyncio.sleep(0.1)

        logger.info('Indexator stopped')

[project]
name = "EDO"
dependencies = [
    "conciergelib[aiohttp]==0.5.0",
    "py-eusign==1.3.1",
    "logevo<6",
    "persistql<1",
    "vchasno-crm[aiohttp]==0.3.19",
]
version = "0.1.0"

[tool.uv]
cache-dir = "./.uv_cache"
cache-keys = [{ file = "pyproject.toml" }, { file = "requirements/base.in" }, { file = "requirements/dev.in" }]
index-strategy = "unsafe-best-match"

[tool.uv.sources]
conciergelib = { index = "conciergelib" }
py-eusign = { index = "py-eusign" }
logevo = { index = "logevo" }
persistql = { index = "persistql" }
vchasno-crm = { index = "vchasno-crm" }

[[tool.uv.index]]
name = "conciergelib"
url = "https://gitlab.vchasno.com.ua/api/v4/projects/65/packages/pypi/simple"

[[tool.uv.index]]
name = "py-eusign"
url = "https://gitlab.vchasno.com.ua/api/v4/projects/64/packages/pypi/simple"

[[tool.uv.index]]
name = "logevo"
url = "https://gitlab.vchasno.com.ua/api/v4/projects/66/packages/pypi/simple"

[[tool.uv.index]]
name = "persistql"
url = "https://gitlab.vchasno.com.ua/api/v4/projects/126/packages/pypi/simple"

[[tool.uv.index]]
name = "vchasno-crm"
url = "https://gitlab.vchasno.com.ua/api/v4/projects/69/packages/pypi/simple"


# pytest configuration
[tool.pytest.ini_options]
norecursedirs = [
    "app/models/migrations",
    "app/events/alembic",
    ".ipython",
    "node_modules",
    ".uv_cache",
]
addopts = "--doctest-modules --strict-markers -p no:warnings -p no:unraisableexception -p no:threadexception"
asyncio_mode = "auto"
timeout = 30
markers = [
    # run slow tests as separate testsuite
    "slow",
]
asyncio_default_fixture_loop_scope = "session"

# ruff linter config (replace flak8 + plugins)
[tool.ruff]
target-version = "py312"
line-length = 100
extend-exclude=[
    "app/models/migrations",
    "app/events/alembic",
    "scripts/blake2b_hash.py",
    "scripts/generate_token.py",
    "app/vendor",
    "conftest.py",
]
lint.select = [
    "F",  # Pyflakes
    "E", "W",  # Pycodestyle
    "N",  # pep8-naming
    "C90",  # mccabe
    "T20",  # flake8-print
    "RET",  # flake8-return
    "SIM",  # flake8-simplify
    "B",  # flake8-bugbear
    "A",  # flake8-builtins
    "C4",  # flake8-comprehensions
    "TID", # flake8-tidy-imports
    "Q",  # flake8-quotes
    "B026", "RUF005", "RUF100",  # Ruff-specific rules
    "I", # isort
    "W", # formatting
    "UP" # pyupgrade
]
lint.ignore = [
    # TODO: It is a good idea to uncomment and fix errors :)
    "B904",  # Within an except clause, raise exceptions with raise ... from err or raise ... from None
    "B905",  # zip() without an explicit `strict=` parameter
    "A002",  # argument shadowing a python builtin
    "A003",  # class attribute shadowing a python builtin
    "N818",  # error suffix in exception names (pep8-naming)
    "RET504",  # Unnecessary variable assignment before `return` statement
    "SIM115",  # Use context handler for opening files
    "SIM117",  # Use single `with` statement instead of nested `with` statements
    "SIM103",  # Checks for if statements that can be replaced with bool
    "A005",  # Module `mod_name` shadows a Python standard-library module
]
lint.fixable = [
    "I",  # Import block is un-sorted or un-formatted, missing required import.
    "F401",  # Removing missing imports
    "W291",  # Trim trailing whitespace
    "UP",  # pyupgrade
]

[tool.ruff.lint.flake8-quotes]
inline-quotes = "single"
docstring-quotes = "double"

[tool.ruff.lint.mccabe]
max-complexity = 15

[tool.ruff.format]
indent-style = "space"
quote-style = "single"

# mypy configuration
[tool.mypy]
python_version = '3.12'
check_untyped_defs = true
disallow_any_generics = true
disallow_untyped_calls = true
disallow_untyped_defs = true
follow_imports = 'silent'
ignore_missing_imports = true
strict_optional = true
local_partial_types = true
strict_bytes = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_unreachable = true
no_implicit_optional = true
exclude = '/(tests|alembic|migrations|vendor)/'

[[tool.mypy.overrides]]
module = ["*.tests.*"]
ignore_errors = true

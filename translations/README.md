# Переклади для серверу

Для перекладів на сервері, ми використовуємо пакет [babel](https://babel.pocoo.org/en/latest/)

За замовчуванням всі тексти в коді написані українською, для інших мов ми перекладаємо з української
на потрібну мову у ".po" файлах у каталозі [./translations](./)

## Локально немає перекладів?

Запусти команду, яка скомпілює переклади:

```shell
just translations-compile
```

## Помилка перевірки в Gitlab CI/CD?

Якщо пайплайн Gitlab CI/CD падає з помилкою:

```
⛔ The catalog should be updated (./translations/en/LC_MESSAGES/messages.po)
```

то це означає, що деякі каталог з перекладами не було оновлено після того, як був змінений
обгорнений рядок чи після того, як обгорнули нові рядки. Щоб оновити каталог запустіть команду:

```shell
just translations-update
```

Після цього вручну перегляньте каталоги з перекладом і протестуйте локально чи оновлено каталог:

```shell
just translations-check-catalog
```

## Процес перекладу

#### 1. Підготовка тексту

В python скриптах текст для перекладу обгортуємо у функцію `app.i18n._`:

```python
from app.i18n import _

_("ТЕСТОВІ ЛИСТИ")
```

для jinja2 шаблонів текст обгортуємо у таку конструкцію:

```html
{% trans %}ТЕСТОВІ ЛИСТИ{% endtrans %}
```

#### 2. Генерація файлів для перекладу

Далі запускаємо скрипт, який дістане всі рядки для перекладу, збереже їх у файл-шаблон
"messages.pot" та оновить файл з перекладом "messages.po"

```shell
just translations-update
```

> - messages.pot – це спільний файл для всіх мов і його babel використовує, що

    оновити каталог для конкретної мови

> - messages.po – це файл з перекладом

#### 2. Переклад

Перекладаємо рядки у [messages.po](./en/LC_MESSAGES/messages.po). Для прикладу:

```text
msgid "Безпечно зберігайте документи визначений законодавством час та налаштовуйте права та доступи для командної роботи."
msgstr "Safe storage and customised document access for your team."
```

- msgid – це оригінал для перекладу, його не потрібно редагувати, бо за ним буде здійснюватися
  пошуку перекладу.
- msgstr - це власне переклад для цього рядка.

#### 3. Компіляція

Компілюємо "messages.mo" файли – бінарні файли, які буде зчитувати babel для перекладу в рантаймі:

```shell
just translations-compile
```

Компіляція не обов'язкова, якщо ви не плануєте тестувати переклади локально. Для стейджингу й
продакшену компіляція відбувається на етапі збірки докер образу.

# Переклади для інтерфейсу

Для перекладу на фронтенді ми використовуємо інструмент [ttag](https://ttag.js.org/)

За замовчуванням всі тексти в коді написані українською, для інших мов ми перекладаємо з української
на потрібну мову у ".po" файлах у каталозі [./cs/i18n](../cs/i18n)

## Процес перекладу

#### 1. Підготовка тексту

В js, jsx, ts, tsx скриптах текст для перекладу обгортуємо у літерал `t`:

```js
import { t } from 'ttag';
function hello(name) {
  return t`Hello ${name}`;
}
```

Якщо треба обгорнути текст у якому є JSX, то треба використовувати літерал `jt`:

```jsx
import { jt } from 'ttag';
function Button() {
  const btn = <button key="btn">{t`me`}</button>;
  return <span>{jt`Click ${btn}`}</span>;
}
```

Для перекладу слова в множині: Plurals:

```javascript
import { msgid, ngettext } from 'ttag';
ngettext(msgid`${n} banana`, `${n} bananas`, n);
```

#### 2. Генерація файлів для перекладу

Далі запускаємо скрипт, який дістане всі рядки для перекладу й оновить ".po" файли перекладу в
каталозі `cs/i18n`:

```bash
just translations-update-frontend
```

або, якщо ви не використовуєте just:

```bash
./scripts/translations/update-frontend-catalog.sh
```

#### 2. Переклад

Перекладаємо рядки у [.po](../cs/i18n) файлах. Для прикладу:

```text
msgid "Безпечно зберігайте документи визначений законодавством час та налаштовуйте права та доступи для командної роботи."
msgstr "Safe storage and customised document access for your team."
```

- msgid – це оригінал для перекладу, його не потрібно редагувати, бо за ним буде здійснюватися
  пошуку перекладу.
- msgstr - це власне переклад для цього рядка.

# Rosetta

Для перекладу ми також використовуємо сервіс Rosetta. Цей сервіс сканує репозиторій з кодом,
знаходить файли для перекладу і показує інтерфейс з можливістю перекладати прямо у цьому сервісі.

Приклад файлу у Rosetta
[https://rosetta.evo.dev](https://rosetta.evo.dev/edit/?fileId=92&projectId=43)

# Додавання нової мови

#### Для серверу:

1. Додаємо нову мову у цей файл: `scripts/translations/constants.py::SUPPORTED_LOCALES`
2. Запускаємо команду, яка автоматично згенерує нові файли для перекладу:

```shell
just translations-update
```

#### Для інтерфейсу

1. Додаємо ноу мову для інтерфейсу у цей скрипт: `scripts/translations/update-frontend-catalog.sh`
2. Запускаємо скрипт, який автоматично згенеруємо нові файли для перекладу:

```shell
./scripts/translations/update-frontend-catalog.sh
```

import type { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
    schema: './cs/gql/schema.gql',
    documents: 'cs/**/*.graphql',
    generates: {
        './cs/gql/graphql.ts': {
            config: {
                fetcher: 'graphql-request',
                documentMode: 'string',
                isReactHook: true,
            },
            plugins: [
                'typescript',
                'typescript-operations',
                'typescript-react-query',
            ],
        },
    },
};

export default config;
